import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
# from .graphql.schema import bind_schema_to_fastapi
from .graphql.schema import schema  # Import the schema directly
from .api.v1.api import api_router
import redis
from .db.neo4j_session import init_db, close_db_connection, verify_connectivity, get_db_session
from .core.config import settings
from starlette.requests import Request
from starlette.responses import JSONResponse
from ariadne.asgi.handlers import GraphQLHTTPHandler
from ariadne.asgi import GraphQL

# Configure logging
logging.basicConfig(
    level=logging.DEBUG if settings.DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

logger.debug("Starting FastAPI application initialization")

app = FastAPI(
    title="Tahimoto API",
    description="GraphQL API for the Tahimoto Cross-Media Recommendation System",
    version=settings.VERSION,
    debug=settings.DEBUG
)

logger.debug("FastAPI application created")

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.debug("CORS middleware added")

# Conditional Neo4j session middleware - only for endpoints that need database access
@app.middleware("http")
async def conditional_db_session_middleware(request: Request, call_next):
    """
    Middleware to conditionally add Neo4j session to request state.
    Only creates sessions for endpoints that actually need database access.
    """
    # Define paths that require Neo4j database access
    neo4j_required_paths = [
        "/graphql",
        "/api/v1/stories",
        "/api/v1/themes",  # Legacy themes endpoint (not simple-themes)
        "/api/v1/interactions",
        "/api/v1/theme-mapping",  # Theme mapping endpoints that use database
        "/api/v1/mcp",  # MCP endpoints that use database
    ]

    # Check if this request needs a Neo4j session
    request_path = request.url.path
    needs_neo4j = any(request_path.startswith(path) for path in neo4j_required_paths)

    if needs_neo4j:
        # Create Neo4j session for database-dependent endpoints
        logger.debug(f"Creating Neo4j session for path: {request_path}")
        async for session in get_db_session():
            request.state.db_session = session
            response = await call_next(request)
            return response
    else:
        # Skip Neo4j session creation for endpoints that don't need it
        logger.debug(f"Skipping Neo4j session for path: {request_path}")
        response = await call_next(request)
        return response

# Mount GraphQL routes using Ariadne
logger.debug("Binding GraphQL schema to FastAPI")
# Instead of mounting which might cause redirects, use a direct route
# bind_schema_to_fastapi(app, "/graphql")
# Use a direct route endpoint instead
@app.post("/graphql")
async def graphql_endpoint(request: Request):
    logger.debug("GraphQL endpoint called")
    graphql_app = GraphQL(schema)
    return await graphql_app.handle_request(request)

logger.debug("GraphQL schema bound to FastAPI as direct endpoint")

# Mount REST API routes
logger.debug("Attempting to mount REST API router")
app.include_router(api_router)
logger.debug("REST API router mounted")

@app.get("/")
async def root():
    logger.debug("Root endpoint called")
    return {
        "message": "Welcome to Tahimoto API",
        "docs": "/docs",
        "graphql": "/graphql",
        "health": "/health",
        "debug": {
            "services": "/debug/services",
            "theme-stats": "/debug/theme-stats"
        }
    }

@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {"status": "healthy", "version": settings.VERSION}

# OpenAI API compatibility endpoint
@app.get("/v1/models")
async def openai_models_compatibility():
    """OpenAI API compatibility endpoint for external tool integration."""
    return {
        "object": "list",
        "data": [
            {
                "id": "tahimoto-theme-analyzer",
                "object": "model",
                "created": **********,
                "owned_by": "tahimoto",
                "permission": [],
                "root": "tahimoto-theme-analyzer",
                "parent": None
            }
        ]
    }

@app.get("/debug/services")
async def check_services():
    """Check all service connections"""
    status = {
        "redis": "unknown",
        "neo4j": "unknown",
        "graphql": "active"
    }
    
    # Check Redis
    try:
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            socket_timeout=5
        )
        r.ping()
        status["redis"] = "connected"
        r.close()
    except Exception as e:
        logger.error(f"Redis connection failed: {str(e)}")
        status["redis"] = f"error: {str(e)}"

    # Check Neo4j
    try:
        neo4j_connected = await verify_connectivity()
        status["neo4j"] = "connected" if neo4j_connected else "error: connection failed"
    except Exception as e:
        logger.error(f"Neo4j connection failed: {str(e)}")
        status["neo4j"] = f"error: {str(e)}"

    return status

@app.get("/debug/theme-stats")
async def theme_stats():
    """Get statistics about theme mappings"""
    try:
        # This will be implemented with Neo4j queries later
        return {
            "total_themes": 0,
            "mapped_anime": 0,
            "pending_mappings": 0,
            "cache_stats": {
                "hits": 0,
                "misses": 0
            }
        }
    except Exception as e:
        logger.error(f"Error getting theme stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    try:
        logger.info("Initializing database on startup")
        await init_db()
        logger.info("Database initialized successfully")
        
        # Create Neo4j indexes for optimal performance
        from app.db.neo4j.indexes import create_indexes
        logger.info("Creating Neo4j indexes for optimal query performance")
        index_count = await create_indexes()
        logger.info(f"Neo4j indexes created successfully: {index_count} total indexes")
        
        # Initialize Redis connection
        from app.core.redis import RedisConnection
        logger.info("Initializing Redis connection on startup")
        redis_connection = await RedisConnection.get()
        if redis_connection:
            logger.info("Redis connection initialized successfully")
        else:
            logger.error("Failed to initialize Redis connection")
            
        # Start scheduled tasks
        from app.core.scheduled_tasks import task_manager
        logger.info("Starting scheduled task manager")
        await task_manager.start()
        logger.info("Scheduled task manager started successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        # Don't crash the app, but log the error
    
    # Print all registered routes for debugging
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "methods": route.methods if hasattr(route, 'methods') else None,
            "name": route.name if hasattr(route, 'name') else None
        })
    logger.debug("Available routes: %s", routes)

# Cleanup on shutdown
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down application")
    
    # Stop scheduled tasks
    try:
        from app.core.scheduled_tasks import task_manager
        logger.info("Stopping scheduled task manager")
        await task_manager.stop()
        logger.info("Scheduled task manager stopped successfully")
    except Exception as e:
        logger.error(f"Error stopping scheduled tasks: {str(e)}")
    
    # Close database connection
    try:
        logger.info("Closing database connection")
        await close_db_connection()
        logger.info("Database connection closed successfully")
    except Exception as e:
        logger.error(f"Error closing database connection: {str(e)}")
        
    # Close Redis connection
    try:
        from app.core.redis import RedisConnection
        logger.info("Closing Redis connection")
        await RedisConnection.close()
        logger.info("Redis connection closed successfully")
    except Exception as e:
        logger.error(f"Error closing Redis connection: {str(e)}")
        
    logger.info("Application shutdown complete") 