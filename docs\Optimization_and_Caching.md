# Tahimoto: Database Optimization and Caching Guidelines

This document outlines optimization strategies for Neo4j graph database and caching patterns using Redis for the Tahimoto cross-media recommendation platform.

## Table of Contents
- [Neo4j Optimization](#neo4j-optimization)
- [Redis Caching Strategy](#redis-caching-strategy)
- [Performance Monitoring](#performance-monitoring)
- [Maintenance Tasks](#maintenance-tasks)

## Neo4j Optimization

### Indexing Strategies

Neo4j performance heavily depends on proper indexing, especially for a recommendation engine:

```cypher
// Create indexes on frequently queried properties
CREATE INDEX story_id_index FOR (s:Story) ON (s.id);
CREATE INDEX story_external_id_index FOR (s:Story) ON (s.external_id);
CREATE INDEX story_title_index FOR (s:Story) ON (s.title_romaji);
CREATE INDEX theme_name_index FOR (t:Theme) ON (t.name);
```

For full-text search capabilities:
```cypher
// Full-text indexes for search functionality
CREATE FULLTEXT INDEX story_description_index FOR (s:Story) ON EACH [s.synopsis];
CREATE FULLTEXT INDEX story_title_fulltext FOR (s:Story) ON EACH [s.title_english, s.title_romaji, s.title_native];
```

### Query Optimization Patterns

#### 1. Use Parameterized Queries
Always use parameterized queries rather than string concatenation:

```python
# Good
query = "MATCH (s:Story {id: $story_id}) RETURN s"
result = await tx.run(query, {"story_id": story_id})

# Avoid
query = f"MATCH (s:Story {{id: '{story_id}'}}) RETURN s"  # Bad practice
```

#### 2. Limit Returned Data
Only return what you need:

```cypher
// Instead of
MATCH (s:Story)-[:HAS_THEME]->(t:Theme)
RETURN s, t

// Better
MATCH (s:Story)-[:HAS_THEME]->(t:Theme)
RETURN s.id, s.title_romaji, t.name
```

#### 3. Use `EXPLAIN` and `PROFILE` for Query Analysis
```cypher
EXPLAIN MATCH (s:Story)-[:RECOMMENDS]->(rec:Story) 
WHERE s.id = $story_id
RETURN rec
```

#### 4. Optimize Relationship Traversal
For recommendations, optimize relationship traversal direction:

```cypher
// If you frequently query stories by theme
MATCH (s:Story)-[:HAS_THEME]->(t:Theme {name: "Journey"})
RETURN s

// If you frequently query themes for a story
MATCH (t:Theme)<-[:HAS_THEME]-(s:Story {id: "story_123"})
RETURN t
```

### Connection Pooling

Configure connection pooling in your Neo4j session management:

```python
from neo4j import AsyncDriver, AsyncSession

class Neo4jSessionManager:
    def __init__(self, uri, user, password, max_connection_pool_size=50):
        self.driver = AsyncDriver(uri, auth=(user, password), 
                                max_connection_pool_size=max_connection_pool_size)
```

### Database Configuration

Recommended Neo4j configuration for a recommendation engine:

```
dbms.memory.heap.initial_size=2G
dbms.memory.heap.max_size=4G
dbms.memory.pagecache.size=4G
dbms.transaction.timeout=60s
```

## Redis Caching Strategy

### Multi-level Caching

Implement a tiered caching strategy:

1. Application-level cache (in-memory)
2. Redis cache (distributed)
3. Database (Neo4j)

### TTL Recommendations

| Data Type | TTL (seconds) | Justification |
|-----------|---------------|---------------|
| Story details | 3600 (1 hour) | Content rarely changes |
| Search results | 900 (15 min) | Balance freshness and performance |
| Recommendations | 1800 (30 min) | Recommendation patterns evolve gradually |
| Theme mappings | 7200 (2 hours) | Core mappings are relatively stable |
| User-specific data | 300 (5 min) | User context changes more frequently |

### Cache Key Design

Follow a consistent cache key pattern:
```
{namespace}:{entity_type}:{id}:{action}
```

Examples:
- `story:basic:5937:details`
- `theme:mapping:journey:stories`
- `recommendations:story:5937:default`
- `search:anime:action:page1`

### Invalidation Patterns

#### 1. Surgical Invalidation
When a specific entity changes:

```python
async def invalidate_story_cache(story_id):
    # Invalidate the specific story
    await redis.delete(f"story:basic:{story_id}:details")
    
    # Also invalidate related caches
    await redis.delete(f"recommendations:story:{story_id}:default")
```

#### 2. Pattern-based Invalidation
When a category of cache needs refreshing:

```python
async def invalidate_theme_caches(theme_id):
    # Delete all keys matching the pattern
    theme_keys = await redis.keys(f"theme:*:{theme_id}:*")
    if theme_keys:
        await redis.delete(*theme_keys)
```

#### 3. Time-based Expiration
Let TTL handle cache freshness for less critical data.

### Cache Warming Strategies

For popular content, warm the cache proactively:

```python
async def warm_popular_story_caches():
    # Get top 100 popular stories
    popular_stories = await db.execute_read(
        lambda tx: tx.run(
            "MATCH (s:Story) RETURN s.id ORDER BY s.popularity DESC LIMIT 100"
        )
    )
    
    # Pre-fetch and cache details and recommendations
    for story in popular_stories:
        story_id = story["s.id"]
        await get_story_with_cache(story_id)  # This function stores in cache
        await get_recommendations_with_cache(story_id)
```

Schedule cache warming during off-peak hours.

## Performance Monitoring

### Key Metrics to Track

1. **Query Performance**
   - Average query response time
   - 95th and 99th percentile response times
   - Slowest queries (by type)

2. **Cache Efficiency**
   - Cache hit rate
   - Cache miss rate
   - Average cache retrieval time

3. **Database Health**
   - Connection pool utilization
   - Node and relationship count growth
   - Memory usage
   - Storage usage

### Monitoring Implementation

```python
# Example Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge

# Cache metrics
CACHE_HITS = Counter('tahimoto_cache_hits', 'Cache hit count', ['cache_type'])
CACHE_MISSES = Counter('tahimoto_cache_misses', 'Cache miss count', ['cache_type'])
CACHE_RETRIEVAL_TIME = Histogram(
    'tahimoto_cache_retrieval_seconds', 
    'Time to retrieve data from cache',
    ['cache_type']
)

# Database metrics
DB_QUERY_TIME = Histogram(
    'tahimoto_db_query_seconds', 
    'Database query execution time',
    ['query_type']
)
DB_POOL_USAGE = Gauge(
    'tahimoto_db_pool_usage',
    'Database connection pool usage'
)
```

### Alert Thresholds

Configure alerts for:

- Query response time > 1 second
- Cache hit rate < 60%
- Connection pool usage > 80%
- Rapid growth in node/relationship count (potential data issues)

## Maintenance Tasks

### Weekly Tasks

1. **Review and Optimize Slow Queries**
   ```python
   async def get_slow_queries():
       query = """
       CALL apoc.monitor.karmaticQueryHits()
       YIELD query, negativeHits
       WHERE negativeHits > 0
       RETURN query, negativeHits
       ORDER BY negativeHits DESC
       LIMIT 10
       """
       return await db.execute_read(lambda tx: tx.run(query))
   ```

2. **Cache Analysis**
   - Review cache hit rates by type
   - Adjust TTLs for underperforming caches
   - Consider preloading frequently missed items

### Monthly Tasks

1. **Neo4j Index Management**
   ```python
   async def check_index_usage():
       query = """
       CALL db.indexes()
       YIELD name, type, entityType, properties, state, progress
       RETURN name, type, entityType, properties, state, progress
       """
       return await db.execute_read(lambda tx: tx.run(query))
   ```

2. **Data Growth Analysis**
   - Monitor node and relationship growth
   - Plan for capacity increases if needed
   - Consider archiving old or unused data

3. **Review Caching Strategy**
   - Analyze which routes benefit most from caching
   - Adjust caching rules and TTLs based on data access patterns
   - Consider implementing client-side caching for stable content

### Quarterly Tasks

1. **Neo4j Version Update Assessment**
   - Check for new Neo4j versions
   - Review release notes for performance improvements
   - Test upgrade on staging environment

2. **Full System Performance Review**
   - Run load tests
   - Benchmark against previous quarters
   - Identify optimization opportunities

## Implementation Guidelines

1. **Start Simple**
   - Begin with Redis caching for the most expensive or frequent operations
   - Measure before and after to quantify improvements

2. **Optimize Incrementally**
   - First address the slowest queries or highest volume endpoints
   - Apply Neo4j indexing strategies where most beneficial
   - Add monitoring to validate improvements

3. **Test with Production-like Data**
   - Performance characteristics change with data volume
   - Test with representative data size and access patterns

## Conclusion

This document provides guidelines rather than strict rules. Adapt these strategies based on your specific usage patterns and performance bottlenecks. Regular monitoring and incremental optimization will yield the best results for Tahimoto's performance at scale. 