from typing import Any, Dict, Optional, List
from pydantic_settings import BaseSettings
from pydantic import validator, Field

class Settings(BaseSettings):
    PROJECT_NAME: str = "Tahimoto"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Neo4j settings
    NEO4J_URI: str = Field(..., description="Neo4j connection URI")
    NEO4J_USER: str = Field(..., description="Neo4j username")
    NEO4J_PASSWORD: str = Field(..., description="Neo4j password")
    
    # Redis settings
    REDIS_HOST: str = Field(..., description="Redis host address")
    REDIS_PORT: int = Field(6379, description="Redis port")
    REDIS_DB: int = Field(0, description="Redis database number")
    
    # Agent Service settings
    USE_AGENT_MAPPING: bool = Field(False, description="Whether to use Agent Service for mapping")
    AGENT_SERVICE_URL: str = Field("http://localhost:8001/api", description="Agent Service URL")
    AGENT_SERVICE_TIMEOUT: int = Field(30, description="Agent Service timeout in seconds")
    
    # Cache settings
    CACHE_THEME_MAPPING_TTL: int = Field(3600, description="Theme mapping cache TTL in seconds")  # 1 hour
    CACHE_QUERY_RESULTS_TTL: int = Field(300, description="Query results cache TTL in seconds")  # 5 minutes
    
    # CORS configuration
    BACKEND_CORS_ORIGINS: List[str] = Field(
        ["http://localhost:3000", "http://localhost:8000"],
        description="CORS allowed origins"
    )
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Any) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # API Settings
    API_PORT: int = Field(8000, description="API port")
    API_HOST: str = Field("0.0.0.0", description="API host address")
    DEBUG: bool = Field(False, description="Debug mode")
    
    # Security
    SECRET_KEY: str = Field(..., description="Secret key for JWT")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(60, description="Access token expiration in minutes")
    
    class Config:
        case_sensitive = True
        env_file = ".env"  # Located in the project root, not in /core
        env_file_encoding = "utf-8"
        extra = "ignore"

settings = Settings() 