"""
Base CRUD operations for Neo4j database.
This module provides a generic CRUD class that can be extended for specific entity types.
"""
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from uuid import uuid4
from pydantic import BaseModel
from neo4j import AsyncSession
from app.core.logging import get_logger
from app.services.id_service import IdService

CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class CRUDBase(Generic[CreateSchemaType, UpdateSchemaType]):
    """
    Base class for CRUD operations with Neo4j.
    Provides methods for creating, reading, updating, and deleting nodes.
    """
    
    def __init__(self, label: str):
        """
        Initialize CRUD object with a Neo4j node label.
        
        Args:
            label: The Neo4j node label (e.g., "Theme", "Story")
        """
        self.label = label
        self.logger = get_logger(f"crud_neo4j_{label.lower()}")
        
        # Map Neo4j labels to entity types for IdService
        self.entity_type_map = {
            "Theme": "theme",
            "Story": "story",
            "Mapping": "mapping"
        }
        
        # Get the entity type for this CRUD instance
        self.entity_type = self.entity_type_map.get(label, label.lower())
    
    async def get(self, session: AsyncSession, id: str) -> Optional[Dict[str, Any]]:
        """
        Get a single node by ID.
        
        Args:
            session: Neo4j session
            id: Node ID
            
        Returns:
            Node properties as a dictionary, or None if not found
        """
        try:
            # Standardize ID using IdService
            standard_id = IdService.standardize_id(id, self.entity_type)
            
            # Convert to database format for query
            db_id = IdService.to_database_id(standard_id, self.entity_type)
            
            self.logger.debug(f"Fetching {self.label} with ID: {standard_id} (DB ID: {db_id})")
            
            query = f"""
            MATCH (n:{self.label} {{id: $id}})
            RETURN n
            """
            
            result = await session.run(query, {"id": db_id})
            record = await result.single()
            
            if record:
                node_data = dict(record["n"])
                
                # Ensure ID is standardized in response
                if "id" in node_data:
                    node_data["id"] = IdService.standardize_id(node_data["id"], self.entity_type)
                
                self.logger.debug(f"Found {self.label}: {standard_id}")
                return node_data
            
            self.logger.debug(f"No {self.label} found with ID: {standard_id}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching {self.label} {id}: {str(e)}")
            raise
    
    async def get_multi(
        self, 
        session: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        where: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Get multiple nodes with optional filtering.
        
        Args:
            session: Neo4j session
            skip: Number of records to skip
            limit: Maximum number of records to return
            where: Optional filter conditions as a dictionary
            
        Returns:
            List of node dictionaries
        """
        try:
            self.logger.debug(f"Fetching multiple {self.label} nodes: skip={skip}, limit={limit}, where={where}")
            
            # Build WHERE clause for filtering
            where_clauses = []
            params = {
                "skip": skip,
                "limit": limit
            }
            
            if where:
                for i, (key, value) in enumerate(where.items()):
                    param_name = f"where_{i}"
                    where_clauses.append(f"n.{key} = ${param_name}")
                    params[param_name] = value
                    
            where_clause = ""
            if where_clauses:
                where_clause = "WHERE " + " AND ".join(where_clauses)
                
            query = f"""
            MATCH (n:{self.label})
            {where_clause}
            RETURN n
            ORDER BY n.id
            SKIP $skip
            LIMIT $limit
            """
            
            result = await session.run(query, params)
            records = await result.fetch(limit)
            
            nodes = []
            for record in records:
                node_data = dict(record["n"])
                
                # Ensure ID is standardized in response
                if "id" in node_data:
                    node_data["id"] = IdService.standardize_id(node_data["id"], self.entity_type)
                
                nodes.append(node_data)
                
            self.logger.debug(f"Found {len(nodes)} {self.label} nodes")
            return nodes
            
        except Exception as e:
            self.logger.error(f"Error fetching multiple {self.label} nodes: {str(e)}")
            raise
    
    async def create(self, session: AsyncSession, *, obj_in: Union[Dict[str, Any], CreateSchemaType]) -> Dict[str, Any]:
        """
        Create a new node.
        
        Args:
            session: Neo4j session
            obj_in: Object data to create
            
        Returns:
            Created node as a dictionary
        """
        try:
            # Convert Pydantic model to dict if needed
            obj_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump()
            
            # Clone the data to avoid modifying the input
            db_obj = obj_data.copy()
            
            # Generate ID if not provided
            if "id" not in db_obj or not db_obj["id"]:
                db_obj["id"] = f"{self.entity_type}_{uuid4()}"
                
            # Standardize ID
            standard_id = IdService.standardize_id(db_obj["id"], self.entity_type)
            
            # Convert to database format for storing
            db_id = IdService.to_database_id(standard_id, self.entity_type)
            
            # Store the database ID in the database
            db_obj["id"] = db_id
            
            self.logger.debug(f"Creating {self.label} with ID: {standard_id} (DB ID: {db_id})")
            
            query = f"""
            CREATE (n:{self.label} $props)
            RETURN n
            """
            
            result = await session.run(query, {"props": db_obj})
            record = await result.single()
            
            if not record:
                self.logger.error(f"Failed to create {self.label}")
                raise ValueError(f"Failed to create {self.label}")
                
            created_node = dict(record["n"])
            
            # Ensure ID is standardized in response
            created_node["id"] = standard_id
            
            self.logger.debug(f"Created {self.label}: {standard_id}")
            return created_node
            
        except Exception as e:
            self.logger.error(f"Error creating {self.label}: {str(e)}")
            raise
    
    async def update(
        self,
        session: AsyncSession,
        *,
        id: str,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing node.
        
        Args:
            session: Neo4j session
            id: Node ID
            obj_in: Update data
            
        Returns:
            Updated node as a dictionary, or None if not found
        """
        try:
            # Standardize ID using IdService
            standard_id = IdService.standardize_id(id, self.entity_type)
            
            # Convert to database format for query
            db_id = IdService.to_database_id(standard_id, self.entity_type)
            
            # Convert to dict if it's a Pydantic model
            update_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump(exclude_unset=True)
            
            # Don't allow ID to be updated
            if "id" in update_data:
                del update_data["id"]
                
            self.logger.debug(f"Updating {self.label} with ID: {standard_id} (DB ID: {db_id})")
            
            # Build update SET clause
            set_parts = []
            params = {"id": db_id}
            
            for key, value in update_data.items():
                set_parts.append(f"n.{key} = ${key}")
                params[key] = value
                
            if not set_parts:
                self.logger.warning(f"No updates provided for {self.label} {standard_id}")
                return await self.get(session, id=standard_id)
                
            set_clause = ", ".join(set_parts)
            
            query = f"""
            MATCH (n:{self.label} {{id: $id}})
            SET {set_clause}
            RETURN n
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                self.logger.warning(f"No {self.label} found with ID: {standard_id}")
                return None
                
            node_data = dict(record["n"])
            
            # Ensure ID is standardized in response
            node_data["id"] = standard_id
            
            self.logger.debug(f"Updated {self.label}: {standard_id}")
            return node_data
            
        except Exception as e:
            self.logger.error(f"Error updating {self.label} {id}: {str(e)}")
            raise
    
    async def remove(self, session: AsyncSession, *, id: str) -> bool:
        """
        Remove a node.
        
        Args:
            session: Neo4j session
            id: Node ID
            
        Returns:
            True if successfully removed, False otherwise
        """
        try:
            # Standardize ID using IdService
            standard_id = IdService.standardize_id(id, self.entity_type)
            
            # Convert to database format for query
            db_id = IdService.to_database_id(standard_id, self.entity_type)
            
            self.logger.debug(f"Removing {self.label} with ID: {standard_id} (DB ID: {db_id})")
            
            query = f"""
            MATCH (n:{self.label} {{id: $id}})
            DETACH DELETE n
            """
            
            result = await session.run(query, {"id": db_id})
            summary = await result.summary()
            
            # Check if any nodes were deleted
            counters = summary.counters
            deleted = counters.nodes_deleted
            
            success = deleted > 0
            self.logger.debug(f"{self.label} removal result: {success} (deleted {deleted} nodes)")
            return success
            
        except Exception as e:
            self.logger.error(f"Error removing {self.label} {id}: {str(e)}")
            raise
    
    async def count(self, session: AsyncSession, *, where: Optional[Dict[str, Any]] = None) -> int:
        """
        Count nodes with optional filtering.
        
        Args:
            session: Neo4j session
            where: Optional filter conditions
            
        Returns:
            Number of matching nodes
        """
        try:
            self.logger.debug(f"Counting {self.label} nodes with filters: {where}")
            
            # Build WHERE clause for filtering
            where_clauses = []
            params = {}
            
            if where:
                for i, (key, value) in enumerate(where.items()):
                    # Special handling for ID fields that need standardization
                    if key == "id" and value:
                        # Standardize ID and convert to database format
                        standard_id = IdService.standardize_id(value, self.entity_type)
                        db_id = IdService.to_database_id(standard_id, self.entity_type)
                        where_clauses.append(f"n.id = $id")
                        params["id"] = db_id
                    else:
                        param_name = f"where_{i}"
                        where_clauses.append(f"n.{key} = ${param_name}")
                        params[param_name] = value
                    
            where_clause = ""
            if where_clauses:
                where_clause = "WHERE " + " AND ".join(where_clauses)
                
            query = f"""
            MATCH (n:{self.label})
            {where_clause}
            RETURN count(n) as count
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if record:
                count = record["count"]
                self.logger.debug(f"Found {count} {self.label} nodes")
                return count
                
            return 0
            
        except Exception as e:
            self.logger.error(f"Error counting {self.label} nodes: {str(e)}")
            raise 