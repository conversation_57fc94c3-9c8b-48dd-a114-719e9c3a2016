"""
Test cases for the search component of the stories API.
This file focuses on testing the search functionality in isolation.
"""
import pytest
import json
from unittest.mock import MagicMock, AsyncMock
from typing import Dict, Any, List

# Import the search function - adjust the import path as needed
from app.api.v1.endpoints.stories import search_stories
from app.services.dependencies import get_anilist_service

@pytest.mark.asyncio
class TestSearchComponent:
    """Test suite for the search component."""
    
    @pytest.fixture
    def mock_anilist_service(self):
        """Return a mock AniList service that returns predefined results."""
        mock_service = AsyncMock()
        mock_service.search_anime.return_value = {
            "data": {
                "Page": {
                    "pageInfo": {
                        "total": 3,
                        "perPage": 10,
                        "currentPage": 1,
                        "lastPage": 1,
                        "hasNextPage": False
                    },
                    "media": [
                        {
                            "id": 1,
                            "title": {
                                "english": "Test Anime 1",
                                "romaji": "Test Anime 1",
                                "native": "テストアニメ1"
                            },
                            "type": "ANIME",
                            "format": "TV",
                            "description": "Test description 1",
                            "coverImage": {
                                "large": "https://example.com/1_large.jpg",
                                "medium": "https://example.com/1_medium.jpg"
                            },
                            "bannerImage": "https://example.com/1_banner.jpg",
                            "status": "FINISHED",
                            "averageScore": 75,
                            "popularity": 1000,
                            "source": "ORIGINAL",
                            "genres": ["Action", "Adventure"]
                        },
                        {
                            "id": 2,
                            "title": {
                                "english": "Test Anime 2",
                                "romaji": "Test Anime 2",
                                "native": "テストアニメ2"
                            },
                            "type": "ANIME",
                            "format": "TV",
                            "description": "Test description 2",
                            "coverImage": {
                                "large": "https://example.com/2_large.jpg",
                                "medium": "https://example.com/2_medium.jpg"
                            },
                            "bannerImage": "https://example.com/2_banner.jpg",
                            "status": "RELEASING",
                            "averageScore": 85,
                            "popularity": 2000,
                            "source": "MANGA",
                            "genres": ["Comedy", "Drama"]
                        },
                        {
                            "id": 3,
                            "title": {
                                "english": "Test Anime 3",
                                "romaji": "Test Anime 3",
                                "native": "テストアニメ3"
                            },
                            "type": "ANIME",
                            "format": "TV",
                            "description": "Test description 3",
                            "coverImage": {
                                "large": "https://example.com/3_large.jpg",
                                "medium": "https://example.com/3_medium.jpg"
                            },
                            "bannerImage": "https://example.com/3_banner.jpg",
                            "status": "FINISHED",
                            "averageScore": 90,
                            "popularity": 3000,
                            "source": "LIGHT_NOVEL",
                            "genres": ["Romance", "Slice of Life"]
                        }
                    ]
                }
            }
        }
        
        # Set up transform_media_to_story_data method to return a dict
        async def transform_media(media):
            return {
                "id": f"story_{media['id']}",
                "external_id": str(media["id"]),
                "title_english": media["title"]["english"],
                "title_romaji": media["title"]["romaji"],
                "title_native": media["title"]["native"],
                "synopsis": media["description"],
                "media_type": media["type"],
                "cover_image_large": media["coverImage"]["large"],
                "cover_image_medium": media["coverImage"]["medium"],
                "banner_image": media["bannerImage"],
                "status": media["status"],
                "average_score": media["averageScore"],
                "popularity": media["popularity"],
                "source": media["source"],
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
                "story_metadata": {
                    "genres": media["genres"],
                    "tags": [],
                    "studios": [],
                    "relations": []
                }
            }
            
        mock_service.transform_media_to_story_data.side_effect = transform_media
        
        return mock_service
    
    @pytest.fixture
    def mock_story_cache(self):
        """Return a mock story cache."""
        mock = AsyncMock()
        mock.get.return_value = None  # Default to cache miss
        mock.set.return_value = None
        return mock
    
    @pytest.fixture
    def mock_story_crud(self):
        """Return a mock story CRUD that returns the same dict it receives."""
        mock = AsyncMock()
        
        # Return the same object passed to create_or_update
        async def create_or_update(db, obj_in):
            return obj_in
            
        mock.create_or_update.side_effect = create_or_update
        return mock
    
    @pytest.fixture
    def mock_db_session(self):
        """Return a mock database session."""
        return AsyncMock()
    
    async def test_search_basic_query(self, monkeypatch, mock_anilist_service, mock_db_session, mock_story_cache, mock_story_crud):
        """Test basic search query returning multiple results."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_cache.reset_mock()
        mock_story_crud.reset_mock()
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.search_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        
        # Call search function directly with our mock service
        result = await search_stories(
            query="test", 
            page=1, 
            per_page=10,
            db=mock_db_session,
            anilist_service=mock_anilist_service  # Directly provide the mock service
        )
        
        # Verify search call
        mock_anilist_service.search_anime.assert_called_once_with(
            query="test",
            page=1,
            per_page=10
        )
        
        # Verify result structure
        assert result.total == 3
        assert len(result.items) == 3
        assert result.items[0].id == "story_1"
        assert result.items[0].title_english == "Test Anime 1"
        
        # Verify cache usage
        mock_story_cache.get.assert_called_once()
        mock_story_cache.set.assert_called_once()
        
        # Verify database operations
        assert mock_story_crud.create_or_update.call_count == 3  # One for each result
    
    async def test_search_pagination(self, monkeypatch, mock_anilist_service, mock_db_session, mock_story_cache, mock_story_crud):
        """Test search pagination."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_cache.reset_mock()
        mock_story_crud.reset_mock()
        
        # Modify mock for pagination test
        mock_anilist_service.search_anime.return_value["data"]["Page"]["pageInfo"]["total"] = 30
        mock_anilist_service.search_anime.return_value["data"]["Page"]["pageInfo"]["currentPage"] = 2
        mock_anilist_service.search_anime.return_value["data"]["Page"]["pageInfo"]["lastPage"] = 3
        mock_anilist_service.search_anime.return_value["data"]["Page"]["pageInfo"]["hasNextPage"] = True
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.search_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        
        # Call search function with page 2
        result = await search_stories(
            query="test", 
            page=2, 
            per_page=10,
            db=mock_db_session,
            anilist_service=mock_anilist_service  # Directly provide the mock service
        )
        
        # Verify search call with page 2
        mock_anilist_service.search_anime.assert_called_once_with(
            query="test",
            page=2,
            per_page=10
        )
        
        # Verify result structure
        assert result.total == 30
        assert len(result.items) == 3  # We still use the same mock data
        
        # Verify cache usage
        mock_story_cache.get.assert_called_once()
        mock_story_cache.set.assert_called_once()
    
    async def test_search_cache_hit(self, monkeypatch, mock_anilist_service, mock_db_session, mock_story_cache, mock_story_crud):
        """Test search with cache hit."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_cache.reset_mock()
        mock_story_crud.reset_mock()
        
        # Configure cache to return a hit
        from app.schemas.story import Story, StorySearchResults
        
        cached_results = StorySearchResults(
            total=1,
            items=[
                Story(
                    id="story_cached",
                    external_id="cached",
                    title_english="Cached Anime",
                    title_romaji="Cached Anime",
                    title_native="キャッシュされたアニメ",
                    synopsis="This is a cached result",
                    media_type="ANIME",
                    cover_image_large="https://example.com/cached_large.jpg",
                    cover_image_medium="https://example.com/cached_medium.jpg",
                    banner_image="https://example.com/cached_banner.jpg",
                    average_score=88,
                    popularity=500,
                    status="FINISHED",
                    source="ORIGINAL",
                    created_at="2023-01-01T00:00:00",
                    updated_at="2023-01-01T00:00:00",
                    story_metadata={
                        "genres": ["Action"],
                        "tags": [],
                        "studios": [],
                        "relations": []
                    }
                )
            ]
        )
        
        # Convert to JSON string before returning from mock (to match real cache behavior)
        mock_story_cache.get.return_value = cached_results.model_dump_json()
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.search_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        
        # Call search function
        result = await search_stories(
            query="cached", 
            page=1, 
            per_page=10,
            db=mock_db_session,
            anilist_service=mock_anilist_service  # Directly provide the mock service
        )
        
        # Verify cache usage
        mock_story_cache.get.assert_called_once()
        
        # Verify AniList service was NOT called
        mock_anilist_service.search_anime.assert_not_called()
        
        # Verify database operations were NOT performed
        mock_story_crud.create_or_update.assert_not_called()
        
        # Verify result is from cache
        assert result.total == 1
        assert result.items[0].id == "story_cached"
        assert result.items[0].title_english == "Cached Anime"
    
    async def test_search_empty_results(self, monkeypatch, mock_anilist_service, mock_db_session, mock_story_cache, mock_story_crud):
        """Test search with no results."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_cache.reset_mock()
        mock_story_crud.reset_mock()
        
        # Configure empty results
        mock_anilist_service.search_anime.return_value = {
            "data": {
                "Page": {
                    "pageInfo": {
                        "total": 0,
                        "perPage": 10,
                        "currentPage": 1,
                        "lastPage": 1,
                        "hasNextPage": False
                    },
                    "media": []
                }
            }
        }
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.search_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        
        # Call search function
        result = await search_stories(
            query="nonexistent", 
            page=1, 
            per_page=10,
            db=mock_db_session,
            anilist_service=mock_anilist_service  # Directly provide the mock service
        )
        
        # Verify search call
        mock_anilist_service.search_anime.assert_called_once_with(
            query="nonexistent",
            page=1,
            per_page=10
        )
        
        # Verify result structure
        assert result.total == 0
        assert len(result.items) == 0
        
        # Verify cache is still set even with empty results
        mock_story_cache.set.assert_called_once() 