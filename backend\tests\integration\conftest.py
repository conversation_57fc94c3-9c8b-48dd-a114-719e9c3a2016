"""
Integration test fixtures and configuration.
These fixtures are designed for integration tests that test full workflows with real dependencies.
"""
import pytest
import asyncio
import os
import time
from typing import Dict, Any, AsyncGenerator, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch

# Flag to determine if we're running inside a container
RUNNING_IN_CONTAINER = os.environ.get("RUNNING_IN_CONTAINER", "false").lower() == "true"

# Try importing application modules with fallbacks for missing modules
try:
    from app.main import app
except ImportError:
    app = None

try:
    from app.db.neo4j_session import driver, init_db, get_db_session
except ImportError:
    driver = None
    init_db = None
    get_db_session = None

try:
    # Fixed import for Redis - using the correct path
    from app.core.redis import RedisConnection
except ImportError:
    RedisConnection = None

try:
    from app.core.config import settings
except ImportError:
    settings = None

try:
    from app.services.anilist import anilist_service
except ImportError:
    anilist_service = None

from fastapi import FastAPI
from httpx import AsyncClient, ASGITransport
from neo4j import AsyncGraphDatabase

# Conditionally import docker
try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    # Create a dummy docker client class for type hints
    class DockerClientDummy:
        def __init__(self, *args, **kwargs):
            pass

# Mark all tests in this directory as integration tests
pytest.mark.integration = pytest.mark.integration

@pytest.fixture(scope="session")
def docker_client():
    """Provides a Docker client for managing test containers."""
    if not DOCKER_AVAILABLE:
        pytest.skip("Docker module not available, skipping Docker-dependent tests")
    return docker.from_env()

@pytest.fixture(scope="session")
def neo4j_container(docker_client, request):
    """Creates a Neo4j container for testing."""
    if not DOCKER_AVAILABLE:
        pytest.skip("Docker module not available, skipping Docker-dependent tests")
    
    # Skip if not running container tests
    if not request.config.getoption("--run-container-tests", False):
        pytest.skip("Container tests are disabled. Use --run-container-tests to enable.")
    
    # Check if we should reuse existing containers
    reuse_container = request.config.getoption("--reuse-containers", False)
    container_name = os.environ.get("NEO4J_CONTAINER_NAME", "test-neo4j")
    
    # Try to find existing container if reusing
    if reuse_container:
        try:
            container = docker_client.containers.get(container_name)
            if container.status != "running":
                container.start()
            # Wait for Neo4j to be ready if we just started it
            if container.status != "running":
                time.sleep(20)
            return container
        except docker.errors.NotFound:
            pass  # Container doesn't exist, create a new one
    
    # Create and start the container
    container = docker_client.containers.run(
        image="neo4j:4.4",
        name=container_name,
        ports={"7474/tcp": 7475, "7687/tcp": 7688},
        environment={
            "NEO4J_AUTH": "neo4j/testpassword",
            "NEO4J_dbms_memory_heap_max__size": "512M",
            "NEO4J_dbms_memory_pagecache_size": "256M"
        },
        detach=True,
        remove=True
    )
    
    # Wait for Neo4j to be ready
    time.sleep(20)  # Simple approach - could use health check instead
    
    yield container
    
    # Stop the container after tests unless we're reusing it
    if not reuse_container:
        container.stop()

@pytest.fixture(scope="session")
def redis_container(docker_client, request):
    """Creates a Redis container for testing."""
    if not DOCKER_AVAILABLE:
        pytest.skip("Docker module not available, skipping Docker-dependent tests")
    
    # Skip if not running container tests
    if not request.config.getoption("--run-container-tests", False):
        pytest.skip("Container tests are disabled. Use --run-container-tests to enable.")
    
    # Check if we should reuse existing containers
    reuse_container = request.config.getoption("--reuse-containers", False)
    container_name = os.environ.get("REDIS_CONTAINER_NAME", "test-redis")
    
    # Try to find existing container if reusing
    if reuse_container:
        try:
            container = docker_client.containers.get(container_name)
            if container.status != "running":
                container.start()
            return container
        except docker.errors.NotFound:
            pass  # Container doesn't exist, create a new one
    
    # Create and start the container
    container = docker_client.containers.run(
        image="redis:6",
        name=container_name,
        ports={"6379/tcp": 6380},
        detach=True,
        remove=True
    )
    
    # Wait for Redis to be ready
    time.sleep(5)  # Simple approach - could use health check instead
    
    yield container
    
    # Stop the container after tests unless we're reusing it
    if not reuse_container:
        container.stop()

@pytest.fixture(scope="function")
async def neo4j_test_session(neo4j_container=None):
    """Provides a Neo4j session for testing.
    
    If neo4j_container is provided, connects to that container.
    Otherwise, uses the existing Neo4j connection from the application.
    """
    if DOCKER_AVAILABLE and neo4j_container:
        # Connect to the test container
        uri = os.environ.get("NEO4J_TEST_URI", "bolt://localhost:7688")
        user = os.environ.get("NEO4J_TEST_USER", "neo4j")
        password = os.environ.get("NEO4J_TEST_PASSWORD", "password")
        
        # Create a new driver for the test container
        test_driver = AsyncGraphDatabase.driver(uri, auth=(user, password))
        
        # Create a session
        session = await test_driver.session()
        
        # Clear the database before each test
        try:
            await session.run("MATCH (n) DETACH DELETE n")
        except Exception as e:
            print(f"Error clearing Neo4j database: {e}")
        
        yield session
        
        # Clean up
        await session.close()
        await test_driver.close()
    else:
        # Use the existing Neo4j connection
        if driver:
            async with driver.session() as session:
                yield session
        else:
            pytest.skip("Neo4j driver not available")

@pytest.fixture(scope="function")
async def redis_test_client(redis_container=None):
    """Provides a Redis client for testing.
    
    If redis_container is provided, connects to that container.
    Otherwise, uses the existing Redis connection from the application.
    """
    if DOCKER_AVAILABLE and redis_container:
        # Connect to the test container
        import redis.asyncio as aioredis
        
        host = os.environ.get("REDIS_TEST_HOST", "localhost")
        port = int(os.environ.get("REDIS_TEST_PORT", "6380"))
        
        client = aioredis.Redis(
            host=host,
            port=port,
            db=0,
            decode_responses=True
        )
        
        # Test connection
        await client.ping()
        
        yield client
        
        # Clean up
        await client.close()
    else:
        # Use the existing Redis connection
        if RedisConnection:
            redis = await RedisConnection.get()
            yield redis
        else:
            pytest.skip("Redis module not available")

@pytest.fixture
async def integration_client(neo4j_test_session) -> AsyncGenerator[AsyncClient, None]:
    """Provides an AsyncClient for testing the FastAPI application with database integration."""
    if app is None:
        pytest.skip("FastAPI app not available")
        
    # Override the database dependency
    async def override_get_db():
        yield neo4j_test_session
    
    # Store original
    original_dependency = app.dependency_overrides.get(get_db_session)
    
    # Override for testing
    app.dependency_overrides[get_db_session] = override_get_db
    
    # Create client
    async with AsyncClient(
        transport=ASGITransport(app=app), 
        base_url="http://test"
    ) as client:
        yield client
    
    # Restore original dependency
    if original_dependency:
        app.dependency_overrides[get_db_session] = original_dependency
    else:
        app.dependency_overrides.clear()

@pytest.fixture
async def create_test_stories(neo4j_test_session):
    """Factory fixture for creating test stories in Neo4j."""
    # Function to create stories with parameters
    async def _create_stories(stories: List[Dict[str, Any]]):
        results = []
        for story in stories:
            # Ensure required fields
            assert "id" in story, "Story must have an id"
            assert "title" in story, "Story must have a title"
            
            # Create Cypher parameters
            cypher_params = {
                "id": story["id"],
                "title": story["title"],
                "description": story.get("description", ""),
                "image_url": story.get("image_url", ""),
                "themes": story.get("themes", []),
                "source": story.get("source", "test"),
                "source_id": story.get("source_id", 0),
                "last_updated": story.get("last_updated", "2023-01-01T00:00:00")
            }
            
            # Create story in database
            result = await neo4j_test_session.run("""
                CREATE (s:Story {
                    id: $id,
                    title: $title,
                    description: $description,
                    image_url: $image_url,
                    themes: $themes,
                    source: $source,
                    source_id: $source_id,
                    last_updated: datetime($last_updated)
                })
                RETURN s
            """, **cypher_params)
            
            # Collect results
            async for record in result:
                results.append(record["s"])
        
        return results
    
    return _create_stories

@pytest.fixture
async def sample_stories_for_integration():
    """Provides sample story data for integration tests."""
    return [
        {
            "id": "story_21",
            "title": "One Piece",
            "description": "Test description for One Piece",
            "image_url": "http://example.com/onepiece.jpg",
            "themes": ["Action", "Adventure", "Pirates", "Shounen"],
            "source": "anilist",
            "source_id": 21,
            "last_updated": "2023-01-01T00:00:00"
        },
        {
            "id": "story_5",
            "title": "Cowboy Bebop",
            "description": "Test description for Cowboy Bebop",
            "image_url": "http://example.com/cowboybebop.jpg",
            "themes": ["Action", "SciFi", "Space", "Noir"],
            "source": "anilist",
            "source_id": 5,
            "last_updated": "2023-01-01T00:00:00"
        },
        {
            "id": "story_31240",
            "title": "Re:Zero",
            "description": "Test description for Re:Zero",
            "image_url": "http://example.com/rezero.jpg",
            "themes": ["Fantasy", "Isekai", "Psychological", "Drama"],
            "source": "anilist",
            "source_id": 31240,
            "last_updated": "2023-01-01T00:00:00"
        }
    ] 