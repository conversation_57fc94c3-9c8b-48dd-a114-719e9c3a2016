"""
Test utilities for backend tests.
Contains helper functions for setting up test data and validating responses.
"""
import logging
from typing import Dict, Any
from app.services.anilist import AniListService
from app.crud.neo4j.story import CRUDStory

logger = logging.getLogger(__name__)
story_crud = CRUDStory()

async def get_one_piece_data(db_session):
    """
    Get One Piece data for testing.
    Creates or updates the One Piece anime entry in the database.
    
    Args:
        db_session: Neo4j database session
        
    Returns:
        Dictionary containing One Piece data or None if failed
    """
    try:
        # One Piece AniList ID is 21
        story_id = "story_21"
        
        # Try to get from database first
        db_story = await story_crud.get(db_session, id=story_id)
        
        if db_story and not story_crud.is_stale(db_story):
            logger.info("Using existing One Piece data from database")
            return db_story
        
        # If not in database or stale, fetch from AniList
        logger.info("Fetching One Piece data from AniList API")
        anilist_service = AniListService()
        
        # Get One Piece details from AniList
        anilist_data = await anilist_service.get_anime_details("21")
        story_data = anilist_service.transform_to_story(anilist_data)
        
        # Save to database
        db_story = await story_crud.create_or_update(db_session, obj_in=story_data)
        logger.info("Saved One Piece data to database")
        
        return db_story
    except Exception as e:
        logger.error(f"Error getting One Piece data: {str(e)}")
        return None
        
async def test_story_exists(db_session, story_id: str) -> bool:
    """
    Check if a story exists in the database.
    
    Args:
        db_session: Neo4j database session
        story_id: Story ID to check
        
    Returns:
        True if story exists, False otherwise
    """
    try:
        # Normalize ID
        if not story_id.startswith("story_") and story_id.isdigit():
            normalized_id = f"story_{story_id}"
        else:
            normalized_id = story_id
            
        # Check database
        db_story = await story_crud.get(db_session, id=normalized_id)
        return db_story is not None
    except Exception as e:
        logger.error(f"Error checking if story exists: {str(e)}")
        return False 