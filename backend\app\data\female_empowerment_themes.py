"""
Female Empowerment Theme Hierarchy for Context-Aware Theme Mapping

This module defines comprehensive Female Empowerment theme categories with
detailed descriptions, indicators, and context-aware mapping rules.

Used by the ContextualThemeMapper for multi-dimensional theme analysis.
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class ThemeIndicator:
    """Indicators that suggest a particular theme."""
    character_traits: List[str]
    plot_elements: List[str]
    setting_contexts: List[str]
    staff_correlations: List[str]
    genre_combinations: List[str]


@dataclass
class ThemeDefinition:
    """Complete definition of a theme with mapping rules."""
    id: str
    name: str
    description: str
    confidence_base: float  # Base confidence score (0.0-1.0)
    indicators: ThemeIndicator
    context_modifiers: Dict[str, float]  # Context-based confidence adjustments
    examples: List[str]


# Female Empowerment Theme Hierarchy
FEMALE_EMPOWERMENT_THEMES = {
    "female_agency_restrictive": ThemeDefinition(
        id="female_agency_restrictive",
        name="Female Agency in Restrictive Systems",
        description="Stories featuring women who navigate, challenge, or subvert oppressive social, political, or cultural systems while maintaining their autonomy and pursuing their goals.",
        confidence_base=0.7,
        indicators=ThemeIndicator(
            character_traits=[
                "female_main_character",
                "intelligent_female_lead",
                "resourceful_problem_solver",
                "defies_social_expectations",
                "strategic_thinker"
            ],
            plot_elements=[
                "navigates_court_politics",
                "challenges_authority",
                "uses_intelligence_over_force",
                "finds_creative_solutions",
                "maintains_independence",
                "protects_others_through_wit"
            ],
            setting_contexts=[
                "historical_period",
                "feudal_society",
                "court_setting",
                "patriarchal_system",
                "restrictive_social_norms",
                "imperial_palace",
                "traditional_society"
            ],
            staff_correlations=[
                "female_character_designer",
                "female_writer",
                "female_director"
            ],
            genre_combinations=[
                "historical + drama",
                "mystery + historical",
                "political + drama",
                "slice_of_life + historical"
            ]
        ),
        context_modifiers={
            "historical_setting": 0.2,
            "court_politics": 0.15,
            "female_mc_and_historical": 0.25,
            "mystery_elements": 0.1,
            "female_staff_involvement": 0.1
        },
        examples=[
            "The Apothecary Diaries - Maomao navigating imperial court politics",
            "Emma - Victorian maid challenging class boundaries",
            "Maria the Virgin Witch - Female witch in medieval warfare"
        ]
    ),

    "subversion_gender_expectations": ThemeDefinition(
        id="subversion_gender_expectations",
        name="Subversion of Gender Expectations",
        description="Stories where female characters actively challenge traditional gender roles, expectations, and stereotypes through their actions, choices, or mere existence.",
        confidence_base=0.65,
        indicators=ThemeIndicator(
            character_traits=[
                "unconventional_female_lead",
                "rejects_traditional_roles",
                "pursues_male_dominated_field",
                "physically_strong_female",
                "leadership_qualities",
                "technical_expertise"
            ],
            plot_elements=[
                "enters_forbidden_profession",
                "outperforms_male_counterparts",
                "leads_group_or_organization",
                "masters_combat_or_skills",
                "chooses_career_over_marriage",
                "breaks_social_taboos"
            ],
            setting_contexts=[
                "military_setting",
                "professional_environment",
                "sports_competition",
                "academic_institution",
                "workplace_drama",
                "adventure_setting"
            ],
            staff_correlations=[
                "female_director",
                "female_writer",
                "female_action_coordinator"
            ],
            genre_combinations=[
                "sports + drama",
                "action + slice_of_life",
                "workplace + comedy",
                "military + drama",
                "adventure + comedy"
            ]
        ),
        context_modifiers={
            "sports_setting": 0.2,
            "workplace_environment": 0.15,
            "action_genre": 0.1,
            "comedy_elements": 0.1,
            "female_director": 0.15
        },
        examples=[
            "Keijo!!!!!!!! - Female athletes in unconventional sport",
            "New Game! - Women in game development industry",
            "Shirobako - Women in anime production"
        ]
    ),

    "strength_through_adversity": ThemeDefinition(
        id="strength_through_adversity",
        name="Strength Through Adversity",
        description="Stories focusing on female characters who develop resilience, inner strength, and personal growth through overcoming significant challenges, trauma, or hardship.",
        confidence_base=0.6,
        indicators=ThemeIndicator(
            character_traits=[
                "resilient_personality",
                "overcomes_trauma",
                "personal_growth_arc",
                "emotional_strength",
                "protective_of_others",
                "survivor_mentality"
            ],
            plot_elements=[
                "faces_major_loss",
                "overcomes_abuse_or_neglect",
                "rebuilds_life_after_tragedy",
                "finds_inner_strength",
                "helps_others_heal",
                "transforms_pain_into_purpose"
            ],
            setting_contexts=[
                "post_apocalyptic",
                "war_torn_society",
                "dystopian_world",
                "harsh_environment",
                "survival_scenario",
                "recovery_setting"
            ],
            staff_correlations=[
                "female_writer",
                "female_director",
                "trauma_informed_storytelling"
            ],
            genre_combinations=[
                "drama + psychological",
                "thriller + drama",
                "post_apocalyptic + drama",
                "slice_of_life + drama",
                "supernatural + drama"
            ]
        ),
        context_modifiers={
            "psychological_elements": 0.2,
            "drama_heavy": 0.15,
            "survival_themes": 0.1,
            "character_development_focus": 0.15,
            "female_writer": 0.1
        },
        examples=[
            "A Silent Voice - Overcoming bullying and social isolation",
            "Violet Evergarden - War veteran finding purpose and healing",
            "March Comes in Like a Lion - Dealing with family trauma"
        ]
    ),

    "hidden_female_networks": ThemeDefinition(
        id="hidden_female_networks",
        name="Hidden Female Networks/Solidarity",
        description="Stories highlighting the power of female relationships, mentorship, and support systems that operate within or alongside dominant structures.",
        confidence_base=0.55,
        indicators=ThemeIndicator(
            character_traits=[
                "strong_female_friendships",
                "mentor_mentee_relationships",
                "collaborative_problem_solving",
                "mutual_support_systems",
                "shared_knowledge_networks",
                "intergenerational_wisdom"
            ],
            plot_elements=[
                "women_helping_women",
                "secret_knowledge_sharing",
                "collective_problem_solving",
                "female_mentorship",
                "sisterhood_bonds",
                "underground_networks"
            ],
            setting_contexts=[
                "all_female_environments",
                "traditional_societies",
                "workplace_settings",
                "educational_institutions",
                "community_settings",
                "domestic_spaces"
            ],
            staff_correlations=[
                "female_writer",
                "female_director",
                "female_character_designer",
                "predominantly_female_staff"
            ],
            genre_combinations=[
                "slice_of_life + drama",
                "school + comedy",
                "workplace + slice_of_life",
                "historical + drama",
                "mystery + slice_of_life"
            ]
        ),
        context_modifiers={
            "slice_of_life_genre": 0.2,
            "school_setting": 0.15,
            "workplace_setting": 0.1,
            "multiple_female_characters": 0.2,
            "female_staff_majority": 0.15
        },
        examples=[
            "K-On! - Female friendship through music",
            "Little Women - Sisterhood and mutual support",
            "Hanasaku Iroha - Female mentorship in traditional inn"
        ]
    ),

    "professional_intellectual_excellence": ThemeDefinition(
        id="professional_intellectual_excellence",
        name="Professional/Intellectual Female Excellence",
        description="Stories celebrating female expertise, intelligence, and professional competence in various fields, often highlighting their unique contributions and perspectives.",
        confidence_base=0.7,
        indicators=ThemeIndicator(
            character_traits=[
                "expert_in_field",
                "intellectual_prowess",
                "professional_competence",
                "innovative_thinking",
                "teaching_or_mentoring",
                "research_oriented"
            ],
            plot_elements=[
                "solves_complex_problems",
                "demonstrates_expertise",
                "innovates_in_field",
                "teaches_or_mentors_others",
                "leads_professional_projects",
                "makes_scientific_discoveries"
            ],
            setting_contexts=[
                "academic_environment",
                "research_facility",
                "medical_setting",
                "scientific_laboratory",
                "professional_workplace",
                "educational_institution"
            ],
            staff_correlations=[
                "female_technical_advisor",
                "female_writer",
                "female_director",
                "subject_matter_expert"
            ],
            genre_combinations=[
                "medical + drama",
                "science + slice_of_life",
                "educational + comedy",
                "mystery + intellectual",
                "workplace + drama"
            ]
        ),
        context_modifiers={
            "medical_setting": 0.2,
            "academic_setting": 0.15,
            "science_elements": 0.15,
            "mystery_solving": 0.1,
            "educational_themes": 0.1
        },
        examples=[
            "Dr. Stone - Female scientist contributing to civilization rebuilding",
            "Cells at Work - Female white blood cell professional competence",
            "Moyashimon - Female students in agricultural science"
        ]
    )
}


def get_theme_by_id(theme_id: str) -> ThemeDefinition:
    """Get a theme definition by its ID."""
    return FEMALE_EMPOWERMENT_THEMES.get(theme_id)


def get_all_themes() -> Dict[str, ThemeDefinition]:
    """Get all female empowerment theme definitions."""
    return FEMALE_EMPOWERMENT_THEMES


def get_themes_by_context(context_tags: List[str]) -> List[ThemeDefinition]:
    """Get themes that match given context tags."""
    matching_themes = []
    
    for theme in FEMALE_EMPOWERMENT_THEMES.values():
        # Check if any context tags match theme indicators
        matches = any(
            tag in theme.indicators.setting_contexts or
            tag in theme.indicators.genre_combinations or
            tag in theme.indicators.plot_elements
            for tag in context_tags
        )
        
        if matches:
            matching_themes.append(theme)
    
    return matching_themes
