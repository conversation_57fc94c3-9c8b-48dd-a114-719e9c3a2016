from typing import Dict, List, Optional
from datetime import datetime, timedelta
from app.core.cache import <PERSON><PERSON><PERSON><PERSON>ger
from app.core.logging import get_logger
import json
from app.core.redis import RedisConnection
from app.crud.neo4j import theme as theme_neo4j

logger = get_logger(__name__)

"""
Theme Redis Service

This module provides a Redis-based caching service for theme data. It includes:
1. Basic CRUD operations for theme data in Redis
2. Integration with Neo4j theme operations
3. Cache staleness detection to ensure data consistency with Neo4j
4. Batch operations for refreshing multiple themes
5. Statistics and monitoring for cache health

Cache Staleness Detection System:
--------------------------------
The cache staleness detection system ensures that cached theme data remains
in sync with the Neo4j database, avoiding outdated information while maintaining
performance benefits. The system works as follows:

1. Every cached theme object includes a `cache_timestamp` field that records
   when it was last updated.
   
2. When retrieving a theme through `get_theme_with_fallback`, the system:
   - Checks if the theme exists in cache
   - If found, checks if the cached data is stale using `check_if_stale`
   - If stale, refreshes from Neo4j and updates the cache
   
3. The `check_if_stale` method determines staleness based on:
   - Missing timestamp (always considered stale)
   - Age exceeding the configured staleness threshold (default: 24 hours)
   - Comparison with Neo4j data to detect changes
   
4. The `refresh_stale_themes` method proactively scans Redis for theme keys
   and refreshes any that are determined to be stale, improving overall
   data freshness without impacting user experience.
   
5. The system is integrated with scheduled tasks through
   `backend/app/core/scheduled_tasks.py` to periodically refresh stale themes.

6. Cache invalidation is handled through `invalidate_related_caches` 
   to ensure that when data is updated, related cache entries are cleared.

These mechanisms ensure a consistent and efficient caching strategy that
maximizes performance while maintaining data accuracy.
"""

class ThemeRedisService:
    """Service for managing theme data in Redis."""
    
    def __init__(self):
        logger.info("Initializing ThemeRedisService")
        self.theme_prefix = "theme"
        self.analysis_prefix = "analysis"
        self.stats_prefix = "theme_stats"
        self.theme_ttl = 24 * 3600  # 24 hours
        self.analysis_ttl = 3600  # 1 hour
        self.stats_ttl = 300  # 5 minutes
        self.theme_neo4j = theme_neo4j
        self.max_stale_time = 12 * 3600  # 12 hours - threshold for considering cache potentially stale
        logger.info("Theme cache service initialized with Neo4j integration")

    async def get_theme(self, theme_id: str) -> Optional[Dict]:
        """Get a theme by ID."""
        if not theme_id.startswith("theme_"):
            theme_id = f"theme_{theme_id}"
        key = f"{self.theme_prefix}:{theme_id}"
        return await CacheManager.get_json(key)

    async def set_theme(self, theme_id: str, theme_data: Dict):
        """Store a theme."""
        if not theme_id.startswith("theme_"):
            theme_id = f"theme_{theme_id}"
        key = f"{self.theme_prefix}:{theme_id}"
        
        # Add cache timestamp if not present
        if "cache_timestamp" not in theme_data:
            theme_data["cache_timestamp"] = datetime.utcnow().isoformat()
            
        logger.debug(f"Storing theme data: {theme_data}")
        await CacheManager.set_json(key, theme_data, self.theme_ttl)

    async def check_if_stale(self, session, theme_id: str, cached_theme: Dict) -> bool:
        """
        Check if cached theme data is stale compared to Neo4j.
        
        Args:
            session: Neo4j database session
            theme_id: Theme ID
            cached_theme: The cached theme data
            
        Returns:
            True if cache is stale, False otherwise
        """
        # If no cache timestamp, consider it stale
        if "cache_timestamp" not in cached_theme:
            logger.debug(f"No cache timestamp for theme {theme_id}, considering stale")
            return True
            
        try:
            # Parse cache timestamp
            cache_time = datetime.fromisoformat(cached_theme["cache_timestamp"])
            now = datetime.utcnow()
            
            # If cache is older than max_stale_time, verify with Neo4j
            if (now - cache_time).total_seconds() > self.max_stale_time:
                logger.debug(f"Cache for theme {theme_id} is older than threshold, checking Neo4j")
                
                # Get theme updated_at from Neo4j
                theme_id_clean = theme_id.replace("theme_", "") if theme_id.startswith("theme_") else theme_id
                db_theme = await self.theme_neo4j.get(session, id=f"theme_{theme_id_clean}")
                
                if not db_theme:
                    logger.debug(f"Theme {theme_id} not found in Neo4j, cache is stale")
                    return True
                    
                # If theme has updated_at field, compare with cache timestamp
                if "updated_at" in db_theme:
                    try:
                        db_update_time = datetime.fromisoformat(db_theme["updated_at"])
                        return db_update_time > cache_time
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid updated_at format for theme {theme_id} in Neo4j")
                        return True
                
                # No updated_at field, use other fields to detect changes
                for key in ["name", "description", "status"]:
                    if key in db_theme and key in cached_theme and db_theme[key] != cached_theme[key]:
                        logger.debug(f"Theme {theme_id} field '{key}' changed in Neo4j, cache is stale")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if theme {theme_id} is stale: {str(e)}")
            # On error, consider it not stale to avoid unnecessary updates
            return False

    async def get_theme_with_fallback(self, session, theme_id: str) -> Optional[Dict]:
        """
        Get a theme by ID from cache with fallback to Neo4j.
        
        Args:
            session: Neo4j database session
            theme_id: Theme ID
            
        Returns:
            Theme data or None if not found
        """
        # Try cache first
        cached_theme = await self.get_theme(theme_id)
        if cached_theme:
            # Check if cache might be stale
            is_stale = await self.check_if_stale(session, theme_id, cached_theme)
            
            if not is_stale:
                logger.debug(f"Cache hit for theme {theme_id}")
                return cached_theme
            else:
                logger.debug(f"Cache hit for theme {theme_id}, but data is stale")
            
        # Cache miss or stale, get from Neo4j
        logger.debug(f"Cache miss or stale for theme {theme_id}, fetching from Neo4j")
        theme_id_clean = theme_id.replace("theme_", "") if theme_id.startswith("theme_") else theme_id
        db_theme = await self.theme_neo4j.get(session, id=f"theme_{theme_id_clean}")
        
        if db_theme:
            # Store in cache for future requests
            await self.set_theme(theme_id, db_theme)
            logger.debug(f"Stored theme {theme_id} in cache from Neo4j")
            return db_theme
            
        logger.debug(f"Theme {theme_id} not found in Neo4j")
        return None

    async def get_analysis(self, source_type: str, source_id: str) -> Optional[Dict]:
        """Get theme analysis for a source."""
        if source_type == "story" and not source_id.startswith("story_"):
            source_id = f"story_{source_id}"
        elif source_type == "theme" and not source_id.startswith("theme_"):
            source_id = f"theme_{source_id}"
        elif source_type == "mapping" and not source_id.startswith("mapping_"):
            source_id = f"mapping_{source_id}"
            
        key = f"{self.analysis_prefix}:{source_id}"
        data = await CacheManager.get_json(key)
        
        # Ensure all IDs in the analysis have proper prefixes
        if data:
            if "themes" in data:
                for theme in data["themes"]:
                    if "id" in theme and not theme["id"].startswith(("theme_", "mapping_")):
                        theme["id"] = f"theme_{theme['id']}"
                    if "mapping_id" in theme and not theme["mapping_id"].startswith("mapping_"):
                        theme["mapping_id"] = f"mapping_{theme['mapping_id']}"
                    if "theme_id" in theme and not theme["theme_id"].startswith("theme_"):
                        theme["theme_id"] = f"theme_{theme['theme_id']}"
        return data

    async def set_analysis(self, source_type: str, source_id: str, analysis_data: Dict):
        """Store theme analysis results."""
        if source_type == "story" and not source_id.startswith("story_"):
            source_id = f"story_{source_id}"
        elif source_type == "theme" and not source_id.startswith("theme_"):
            source_id = f"theme_{source_id}"
        elif source_type == "mapping" and not source_id.startswith("mapping_"):
            source_id = f"mapping_{source_id}"
            
        key = f"{self.analysis_prefix}:{source_id}"
        logger.debug(f"Storing analysis data: {analysis_data}")
        
        # Ensure all IDs have proper prefixes before storing
        if "themes" in analysis_data:
            for theme in analysis_data["themes"]:
                if "id" in theme and not theme["id"].startswith(("theme_", "mapping_")):
                    theme["id"] = f"theme_{theme['id']}"
                if "mapping_id" in theme and not theme["mapping_id"].startswith("mapping_"):
                    theme["mapping_id"] = f"mapping_{theme['mapping_id']}"
                if "theme_id" in theme and not theme["theme_id"].startswith("theme_"):
                    theme["theme_id"] = f"theme_{theme['theme_id']}"
                    
        analysis_data["last_analyzed"] = datetime.utcnow().isoformat()
        await CacheManager.set_json(key, analysis_data, self.analysis_ttl)

    async def get_analysis_with_fallback(self, session, source_type: str, source_id: str) -> Optional[Dict]:
        """
        Get theme analysis from cache with fallback to Neo4j.
        
        Args:
            session: Neo4j database session
            source_type: Type of source (story, theme, mapping)
            source_id: ID of the source
            
        Returns:
            Analysis data or None if not found
        """
        # Try cache first
        cached_analysis = await self.get_analysis(source_type, source_id)
        if cached_analysis:
            logger.debug(f"Cache hit for analysis {source_type}:{source_id}")
            cached_analysis["cache_hit"] = True
            return cached_analysis
            
        # Cache miss, get from Neo4j
        logger.debug(f"Cache miss for analysis {source_type}:{source_id}, fetching from Neo4j")
        
        # Standardize ID
        if source_type == "story" and not source_id.startswith("story_"):
            source_id = f"story_{source_id}"
        elif source_type == "theme" and not source_id.startswith("theme_"):
            source_id = f"theme_{source_id}"
        
        try:
            # Get theme analysis from Neo4j
            db_analysis = await self.theme_neo4j.get_analysis(
                session,
                source_type=source_type,
                source_id=source_id
            )
            
            if db_analysis:
                # Format analysis data
                formatted_analysis = {
                    "themes": db_analysis.get("all", []),
                    "primary_themes": db_analysis.get("primary", []),
                    "secondary_themes": db_analysis.get("secondary", []),
                    "mood_themes": db_analysis.get("mood", []),
                    "character_themes": db_analysis.get("character", []),
                    "plot_themes": db_analysis.get("plot", []),
                    "confidence": sum([t.get("mapping_strength", 0) for t in db_analysis.get("all", [])]) / 
                                 len(db_analysis.get("all", [])) if db_analysis.get("all", []) else 0.0,
                    "last_analyzed": datetime.utcnow().isoformat(),
                    "cache_hit": False,
                    "metadata": {
                        "source": "neo4j",
                        "analyzed_at": datetime.utcnow().isoformat()
                    }
                }
                
                # Store in cache for future requests
                await self.set_analysis(source_type, source_id, formatted_analysis)
                logger.debug(f"Stored analysis for {source_type}:{source_id} in cache from Neo4j")
                return formatted_analysis
        except Exception as e:
            logger.error(f"Error fetching analysis from Neo4j: {str(e)}")
            
        logger.debug(f"Analysis for {source_type}:{source_id} not found in Neo4j")
        return None

    async def update_theme_mapping(self, mapping_id: str, updates: Dict) -> bool:
        """Update a theme mapping."""
        if not mapping_id.startswith("mapping_"):
            mapping_id = f"mapping_{mapping_id}"
        logger.debug(f"Updating theme mapping: {mapping_id} with {updates}")
        
        # Get all analysis keys
        pattern = f"{self.analysis_prefix}:*"
        redis = RedisConnection.get_instance()
        updated = False
        
        for key in redis.scan_iter(match=pattern):
            data = await CacheManager.get_json(key.decode())
            if not data:
                continue
                
            # Update in all theme lists
            for theme_list in ["themes", "primary_themes", "secondary_themes", 
                             "mood_themes", "character_themes", "plot_themes"]:
                if theme_list in data:
                    for mapping in data[theme_list]:
                        if mapping["id"] == mapping_id:
                            mapping.update(updates)
                            updated = True
            
            if updated:
                logger.debug(f"Updated mapping in {key}")
                await CacheManager.set_json(key.decode(), data, self.analysis_ttl)
                return True
        
        logger.debug(f"Mapping {mapping_id} not found in any analysis")
        return False

    async def get_theme_stats(self) -> Dict:
        """Get statistics about theme mappings."""
        # Try to get cached stats first
        stats = await CacheManager.get_json(f"{self.stats_prefix}:global")
        if stats:
            return stats
            
        # Calculate new stats
        total_themes = 0
        mapped_count = 0
        needs_review = 0
        cache_hits = 0
        total_requests = 0
        
        # Get all analysis keys
        pattern = f"{self.analysis_prefix}:*"
        redis = RedisConnection.get_instance()
        
        for key in redis.scan_iter(match=pattern):
            data = await CacheManager.get_json(key.decode())
            if not data:
                continue
                
            if "themes" in data:
                total_themes += len(data["themes"])
                mapped_count += len([t for t in data["themes"] if not t.get("needs_review")])
                needs_review += len([t for t in data["themes"] if t.get("needs_review")])
                if data.get("cache_hit"):
                    cache_hits += 1
                total_requests += 1
        
        stats = {
            "total_themes": total_themes,
            "mapped_count": mapped_count,
            "needs_review_count": needs_review,
            "cache_hit_rate": cache_hits / float(total_requests) if float(total_requests) > 0 else 0
        }
        
        # Cache the stats
        await CacheManager.set_json(f"{self.stats_prefix}:global", stats, self.stats_ttl)
        return stats

    async def set_stats(self, stats_data: Dict) -> bool:
        """
        Store theme statistics in Redis.
        
        Args:
            stats_data: Dictionary containing theme statistics
            
        Returns:
            True if successful, False otherwise
        """
        logger.debug(f"Storing theme stats: {stats_data}")
        try:
            await CacheManager.set_json(f"{self.stats_prefix}:global", stats_data, self.stats_ttl)
            return True
        except Exception as e:
            logger.error(f"Error storing theme stats: {str(e)}")
            return False

    async def invalidate_related_caches(self, source_type: str, source_id: str) -> int:
        """
        Invalidate all caches related to a specific source when data is updated in Neo4j.
        
        Args:
            source_type: Type of source (story, theme, mapping)
            source_id: ID of the source
            
        Returns:
            Number of invalidated cache keys
        """
        logger.info(f"Invalidating related caches for {source_type}:{source_id}")
        invalidated = 0
        
        # Standardize ID
        if source_type == "story" and not source_id.startswith("story_"):
            source_id = f"story_{source_id}"
        elif source_type == "theme" and not source_id.startswith("theme_"):
            source_id = f"theme_{source_id}"
        elif source_type == "mapping" and not source_id.startswith("mapping_"):
            source_id = f"mapping_{source_id}"
        
        # 1. Clear direct analysis cache
        key = f"{self.analysis_prefix}:{source_id}"
        if await CacheManager.delete(key):
            invalidated += 1
            logger.debug(f"Invalidated analysis cache for {key}")
        
        # 2. Clear theme cache if it's a theme
        if source_type == "theme":
            if await CacheManager.delete(source_id):
                invalidated += 1
                logger.debug(f"Invalidated theme cache for {source_id}")
        
        # 3. Clear all related analysis caches (for relationships)
        if source_type == "story":
            # If it's a story, also invalidate any theme analyses that include this story
            pattern = f"{self.analysis_prefix}:theme:*"
            invalidated += await CacheManager.delete_pattern(pattern)
            logger.debug(f"Invalidated theme analyses that might reference story {source_id}")
        elif source_type == "theme":
            # If it's a theme, invalidate story analyses that might include this theme
            pattern = f"{self.analysis_prefix}:story:*"
            invalidated += await CacheManager.delete_pattern(pattern)
            logger.debug(f"Invalidated story analyses that might reference theme {source_id}")
        
        # 4. Always invalidate stats cache
        if await CacheManager.delete("global"):
            invalidated += 1
            logger.debug("Invalidated global theme stats cache")
        
        logger.info(f"Invalidated {invalidated} cache entries related to {source_type}:{source_id}")
        return invalidated

    async def refresh_stale_themes(self, session, max_themes: int = 50) -> Dict[str, int]:
        """
        Refresh theme caches that may be stale by checking with Neo4j.
        This is useful for batch updating frequently used themes.
        
        Args:
            session: Neo4j database session
            max_themes: Maximum number of themes to check
            
        Returns:
            Dictionary with statistics about the refresh operation
        """
        logger.info(f"Refreshing stale themes (max: {max_themes})")
        stats = {
            "checked": 0,
            "refreshed": 0,
            "errors": 0,
            "not_found": 0
        }
        
        try:
            # Get all theme keys
            pattern = f"{self.theme_prefix}:*"
            redis = await RedisConnection.get()
            if not redis:
                logger.warning("Redis connection not available for stale theme refresh")
                return stats
                
            # Scan for theme keys
            theme_keys = []
            async for key in redis.scan_iter(match=pattern):
                if isinstance(key, bytes):
                    theme_keys.append(key.decode())
                else:
                    theme_keys.append(key)
                
                if len(theme_keys) >= max_themes:
                    break
                    
            logger.debug(f"Found {len(theme_keys)} theme keys in cache")
            
            # Process each theme
            for key in theme_keys:
                stats["checked"] += 1
                theme_id = key.replace(f"{self.theme_prefix}:", "")
                
                try:
                    # Get cached theme
                    cached_theme = await self.get_theme(theme_id)
                    if not cached_theme:
                        continue
                        
                    # Check if stale
                    is_stale = await self.check_if_stale(session, theme_id, cached_theme)
                    if is_stale:
                        # Refresh from Neo4j
                        theme_id_clean = theme_id.replace("theme_", "") if theme_id.startswith("theme_") else theme_id
                        db_theme = await self.theme_neo4j.get(session, id=f"theme_{theme_id_clean}")
                        
                        if db_theme:
                            # Update cache
                            await self.set_theme(theme_id, db_theme)
                            logger.debug(f"Refreshed stale theme {theme_id}")
                            stats["refreshed"] += 1
                        else:
                            # Theme no longer exists in Neo4j
                            await CacheManager.delete(key)
                            logger.debug(f"Deleted cache for non-existent theme {theme_id}")
                            stats["not_found"] += 1
                            
                except Exception as e:
                    logger.error(f"Error refreshing theme {theme_id}: {str(e)}")
                    stats["errors"] += 1
            
            logger.info(f"Stale theme refresh complete: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error during stale theme refresh: {str(e)}")
            stats["errors"] += 1
            return stats

    async def bulk_operation(self, operations: List[Dict]) -> Dict[str, int]:
        """Perform bulk operations on the cache."""
        results = {
            "set": 0,
            "delete": 0,
            "error": 0
        }
        
        # Get Redis pipeline for batch operations
        try:
            redis_instance = await RedisConnection.get()
            if not redis_instance:
                logger.warning("Redis connection not available for bulk operation")
                return results
                
            for op in operations:
                op_type = op.get("type")
                cache_type = op.get("cache_type")
                key = op.get("key")
                
                if not op_type or not cache_type or not key:
                    logger.warning(f"Invalid operation: {op}")
                    results["error"] += 1
                    continue
                
                # Determine TTL based on cache type
                ttl = None
                if cache_type == "theme":
                    ttl = self.theme_ttl
                    prefix = self.theme_prefix
                elif cache_type == "analysis":
                    ttl = self.analysis_ttl
                    prefix = self.analysis_prefix
                elif cache_type == "theme_stats":
                    ttl = self.stats_ttl
                    prefix = self.stats_prefix
                else:
                    logger.warning(f"Unknown cache type: {cache_type}")
                    results["error"] += 1
                    continue
                
                # Apply TTL override if provided
                if "ttl" in op:
                    ttl = op["ttl"]
                
                # Format the key with prefix
                formatted_key = f"{prefix}:{key}"
                
                try:
                    if op_type == "set":
                        value = op.get("value")
                        if value is None:
                            logger.warning(f"No value provided for set operation: {op}")
                            results["error"] += 1
                            continue
                        
                        # Store as JSON if it's a dict
                        if isinstance(value, dict):
                            await CacheManager.set_json(formatted_key, value, ttl)
                        else:
                            await CacheManager.set(formatted_key, str(value), ttl)
                        results["set"] += 1
                    elif op_type == "delete":
                        await CacheManager.delete(formatted_key)
                        results["delete"] += 1
                    else:
                        logger.warning(f"Unknown operation type: {op_type}")
                        results["error"] += 1
                except Exception as e:
                    logger.error(f"Error in bulk operation: {e}")
                    results["error"] += 1
        except Exception as e:
            logger.error(f"Error in bulk operation: {e}")
            results["error"] += 1
            
        return results

    async def warm_popular_theme_cache(self, session, limit: int = 20) -> int:
        """
        Retrieve popular themes from Neo4j and cache them for faster access.
        
        Args:
            session: Neo4j database session
            limit: Maximum number of themes to cache
            
        Returns:
            Number of themes cached
        """
        logger.info(f"Warming theme cache with up to {limit} popular themes")
        try:
            # Find the most frequently used themes by checking relationships
            cypher_query = """
            MATCH (s)-[r:HAS_THEME]->(t:Theme)
            WITH t, count(r) as usage_count
            ORDER BY usage_count DESC
            LIMIT $limit
            RETURN t
            """
            
            result = await session.run(cypher_query, {"limit": limit})
            records = await result.fetch(limit)
            
            if not records:
                logger.warning("No themes found for cache warming")
                return 0
                
            cached_count = 0
            for record in records:
                if "t" in record and record["t"]:
                    theme_data = dict(record["t"])
                    theme_id = theme_data.get("id")
                    
                    if not theme_id:
                        continue
                        
                    # Ensure ID has proper prefix
                    if not theme_id.startswith("theme_"):
                        theme_id = f"theme_{theme_id}"
                        theme_data["id"] = theme_id
                    
                    # Cache the theme
                    await self.set_theme(theme_id, theme_data)
                    cached_count += 1
            
            logger.info(f"Warmed cache with {cached_count} themes")
            return cached_count
            
        except Exception as e:
            logger.error(f"Error warming theme cache: {str(e)}")
            return 0

theme_redis = ThemeRedisService() 