import logging
from fastapi import APIRouter, HTTPException
import redis
from app.db.neo4j_session import verify_connectivity

logger = logging.getLogger(__name__)

"""
Debug endpoints for checking service health.
Note: For theme-related operations, including stats, please use the GraphQL API endpoint at /api/v1/graphql.
"""
router = APIRouter(
    tags=["debug"]
)

@router.get("/services")
async def check_services():
    """Check all service connections"""
    status = {
        "redis": "unknown",
        "neo4j": "unknown",
        "graphql": "active"
    }
    
    # Check Redis
    try:
        r = redis.Redis(
            host="redis",
            port=6379,
            db=0,
            socket_timeout=5
        )
        r.ping()
        status["redis"] = "connected"
        r.close()
    except Exception as e:
        logger.error(f"Redis connection failed: {str(e)}")
        status["redis"] = f"error: {str(e)}"

    # Check Neo4j
    try:
        neo4j_connected = await verify_connectivity()
        status["neo4j"] = "connected" if neo4j_connected else "error: connection failed"
    except Exception as e:
        logger.error(f"Neo4j connection failed: {str(e)}")
        status["neo4j"] = f"error: {str(e)}"

    return status