import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTT<PERSON>Exception
from datetime import datetime

from app.api.v1.endpoints.themes import analyze_themes, ThemeAnalysisRequest
from app.services.theme_analysis import ThemeAnalysisService

# Sample test data
TEST_ANALYSIS_REQUEST = ThemeAnalysisRequest(
    source_type="anime",
    source_id="12345",
    genres=["Action", "Adventure"],
    tags=[{"name": "Magic", "category": "Elements"}, {"name": "School", "category": "Setting"}],
    description="A story about magical adventures in a school setting."
)

TEST_ANALYSIS_RESULT = {
    "themes": [
        {
            "id": "theme_adventure",
            "name": "Adventure",
            "confidence": 0.85,
            "mapping_type": "PRIMARY",
            "source": "genre:Adventure"
        },
        {
            "id": "theme_magic_school",
            "name": "Magic School",
            "confidence": 0.75,
            "mapping_type": "SECONDARY",
            "source": "tag_combination:Magic+School"
        }
    ],
    "primary_themes": [
        {
            "id": "theme_adventure",
            "name": "Adventure",
            "confidence": 0.85,
            "mapping_type": "PRIMARY",
            "source": "genre:Adventure"
        }
    ],
    "secondary_themes": [
        {
            "id": "theme_magic_school",
            "name": "Magic School",
            "confidence": 0.75,
            "mapping_type": "SECONDARY",
            "source": "tag_combination:Magic+School"
        }
    ],
    "mood_themes": [],
    "character_themes": [],
    "plot_themes": [],
    "confidence": 0.8,
    "last_analyzed": datetime.utcnow().isoformat(),
    "cache_hit": False
}

@pytest.mark.asyncio
async def test_analyze_themes_success():
    """Test successful theme analysis."""
    # Mock the ThemeAnalysisService
    with patch.object(ThemeAnalysisService, 'analyze_story', new_callable=AsyncMock) as mock_analyze:
        # Configure the mock to return test data
        mock_analyze.return_value = TEST_ANALYSIS_RESULT
        
        # Mock the session
        mock_session = MagicMock()
        
        # Call the endpoint function
        result = await analyze_themes(TEST_ANALYSIS_REQUEST, mock_session)
        
        # Verify the service was called with correct parameters
        mock_analyze.assert_called_once_with(
            source_type=TEST_ANALYSIS_REQUEST.source_type,
            source_id=TEST_ANALYSIS_REQUEST.source_id,
            genres=TEST_ANALYSIS_REQUEST.genres,
            tags=TEST_ANALYSIS_REQUEST.tags,
            description=TEST_ANALYSIS_REQUEST.description
        )
        
        # Verify the result
        assert result == TEST_ANALYSIS_RESULT
        assert len(result["themes"]) == 2
        assert result["themes"][0]["id"] == "theme_adventure"
        assert result["themes"][1]["id"] == "theme_magic_school"

@pytest.mark.asyncio
async def test_analyze_themes_error():
    """Test error handling in theme analysis."""
    # Mock the ThemeAnalysisService to raise an exception
    with patch.object(ThemeAnalysisService, 'analyze_story', new_callable=AsyncMock) as mock_analyze:
        # Configure the mock to raise an exception
        mock_analyze.side_effect = Exception("Test error")
        
        # Mock the session
        mock_session = MagicMock()
        
        # Call the endpoint function and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await analyze_themes(TEST_ANALYSIS_REQUEST, mock_session)
        
        # Verify the exception
        assert excinfo.value.status_code == 500
        assert "An unexpected error occurred" in excinfo.value.detail 