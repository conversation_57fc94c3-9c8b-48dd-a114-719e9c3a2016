"""
Contextual Theme Analysis API Endpoints

Enhanced theme mapping API that uses character, staff, and contextual data
for more nuanced theme analysis. Part of Phase 1 theme expansion.
"""

import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.services.contextual_theme_mapper import ContextualThemeMapper, ThemeMatch
from app.services.anilist import AniListService

logger = logging.getLogger(__name__)
router = APIRouter()


class ContextualThemeRequest(BaseModel):
    """Request model for contextual theme analysis."""
    anime_id: str = Field(..., description="AniList anime ID")
    include_reasoning: bool = Field(default=True, description="Include detailed reasoning in response")


class ContextualThemeResponse(BaseModel):
    """Response model for contextual theme analysis."""
    theme_id: str
    theme_name: str
    confidence: float
    confidence_percent: str
    base_indicators: List[str]
    context_boosts: Dict[str, float]
    reasoning: List[str]


class ContextualAnalysisResponse(BaseModel):
    """Complete response for contextual theme analysis."""
    anime_title: str
    anime_id: str
    genres: List[str]
    character_analysis: Dict[str, Any]
    staff_analysis: Dict[str, Any]
    theme_matches: List[ContextualThemeResponse]
    analysis_summary: Dict[str, Any]


class EnhancedMetadataRequest(BaseModel):
    """Request model for direct metadata analysis."""
    title: str
    genres: List[str]
    characters: List[Dict[str, Any]]
    staff: List[Dict[str, Any]]
    tags: List[Dict[str, Any]] = Field(default_factory=list)


# Dependencies
async def get_contextual_theme_mapper() -> ContextualThemeMapper:
    """Get contextual theme mapper instance."""
    return ContextualThemeMapper()


async def get_anilist_service() -> AniListService:
    """Get AniList service instance."""
    return AniListService()


@router.post("/anime/{anime_id}/contextual-themes", response_model=ContextualAnalysisResponse)
async def analyze_contextual_themes(
    anime_id: str,
    request: ContextualThemeRequest,
    theme_mapper: ContextualThemeMapper = Depends(get_contextual_theme_mapper),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> ContextualAnalysisResponse:
    """
    Analyze anime themes using contextual mapping with character and staff data.
    
    This endpoint fetches enhanced metadata from AniList (including character and staff data)
    and performs sophisticated theme analysis considering multiple contextual factors.
    """
    try:
        logger.info(f"Starting contextual theme analysis for anime ID: {anime_id}")
        
        # Fetch anime details with enhanced metadata
        anime_data = await anilist_service.get_anime_details(anime_id)
        
        if not anime_data:
            raise HTTPException(status_code=404, detail=f"Anime not found: {anime_id}")
        
        # Transform to story format to get enhanced metadata
        story_data = await anilist_service.transform_to_story(anime_data)
        story_metadata = story_data.get("story_metadata", {})
        
        # Perform contextual theme analysis
        theme_matches = theme_mapper.analyze_story_themes(story_metadata)
        
        # Analyze character demographics
        characters = story_metadata.get("characters", [])
        main_chars = [c for c in characters if c.get('role') == 'MAIN']
        female_main = [c for c in main_chars if c.get('gender') == 'Female']
        
        character_analysis = {
            "total_characters": len(characters),
            "main_characters": len(main_chars),
            "female_main_characters": len(female_main),
            "has_female_mc": len(female_main) > 0,
            "female_mc_names": [c.get('name') for c in female_main]
        }
        
        # Analyze staff composition
        staff = story_metadata.get("staff", [])
        directors = [s for s in staff if 'Director' in s.get('role', '')]
        female_staff = [s for s in staff if s.get('gender') == 'Female']
        
        staff_analysis = {
            "total_staff": len(staff),
            "directors": len(directors),
            "female_staff": len(female_staff),
            "female_staff_names": [s.get('name') for s in female_staff[:5]]
        }
        
        # Convert theme matches to response format
        theme_responses = []
        for match in theme_matches:
            theme_responses.append(ContextualThemeResponse(
                theme_id=match.theme_id,
                theme_name=match.theme_name,
                confidence=match.confidence,
                confidence_percent=f"{match.confidence * 100:.1f}%",
                base_indicators=match.base_indicators,
                context_boosts=match.context_boosts,
                reasoning=match.reasoning if request.include_reasoning else []
            ))
        
        # Create analysis summary
        analysis_summary = {
            "total_themes_detected": len(theme_matches),
            "high_confidence_themes": len([m for m in theme_matches if m.confidence >= 0.8]),
            "medium_confidence_themes": len([m for m in theme_matches if 0.5 <= m.confidence < 0.8]),
            "contextual_factors_detected": {
                "female_protagonist": character_analysis["has_female_mc"],
                "historical_setting": any("historical" in g.lower() for g in story_metadata.get("genres", [])),
                "mystery_elements": "Mystery" in story_metadata.get("genres", []),
                "female_creative_input": len(female_staff) > 0
            }
        }
        
        logger.info(f"Completed contextual analysis for {anime_id}: {len(theme_matches)} themes detected")
        
        return ContextualAnalysisResponse(
            anime_title=story_data.get('title_english', story_data.get('title_romaji', 'Unknown')),
            anime_id=anime_id,
            genres=story_metadata.get("genres", []),
            character_analysis=character_analysis,
            staff_analysis=staff_analysis,
            theme_matches=theme_responses,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in contextual theme analysis for {anime_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error analyzing contextual themes: {str(e)}")


@router.post("/analyze-enhanced-metadata", response_model=List[ContextualThemeResponse])
async def analyze_enhanced_metadata(
    request: EnhancedMetadataRequest,
    theme_mapper: ContextualThemeMapper = Depends(get_contextual_theme_mapper)
) -> List[ContextualThemeResponse]:
    """
    Analyze themes from directly provided enhanced metadata.
    
    This endpoint allows direct analysis without fetching from AniList,
    useful for testing or when you already have the enhanced metadata.
    """
    try:
        logger.info(f"Analyzing enhanced metadata for: {request.title}")
        
        # Create story metadata structure
        story_metadata = {
            "genres": request.genres,
            "characters": request.characters,
            "staff": request.staff,
            "tags": request.tags
        }
        
        # Perform contextual theme analysis
        theme_matches = theme_mapper.analyze_story_themes(story_metadata)
        
        # Convert to response format
        theme_responses = []
        for match in theme_matches:
            theme_responses.append(ContextualThemeResponse(
                theme_id=match.theme_id,
                theme_name=match.theme_name,
                confidence=match.confidence,
                confidence_percent=f"{match.confidence * 100:.1f}%",
                base_indicators=match.base_indicators,
                context_boosts=match.context_boosts,
                reasoning=match.reasoning
            ))
        
        logger.info(f"Analyzed {len(theme_matches)} themes for: {request.title}")
        return theme_responses
        
    except Exception as e:
        logger.error(f"Error analyzing enhanced metadata: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error analyzing themes: {str(e)}")


@router.get("/themes/female-empowerment")
async def get_female_empowerment_themes() -> Dict[str, Any]:
    """
    Get information about available Female Empowerment themes.
    
    Returns the theme hierarchy and definitions used by the contextual mapper.
    """
    try:
        from app.data.female_empowerment_themes import FEMALE_EMPOWERMENT_THEMES
        
        themes_info = {}
        for theme_id, theme_def in FEMALE_EMPOWERMENT_THEMES.items():
            themes_info[theme_id] = {
                "name": theme_def.name,
                "description": theme_def.description,
                "base_confidence": theme_def.confidence_base,
                "examples": theme_def.examples,
                "indicators": {
                    "character_traits": theme_def.indicators.character_traits,
                    "plot_elements": theme_def.indicators.plot_elements,
                    "setting_contexts": theme_def.indicators.setting_contexts,
                    "staff_correlations": theme_def.indicators.staff_correlations,
                    "genre_combinations": theme_def.indicators.genre_combinations
                },
                "context_modifiers": theme_def.context_modifiers
            }
        
        return {
            "description": "Female Empowerment theme hierarchy for contextual analysis",
            "total_themes": len(themes_info),
            "themes": themes_info
        }
        
    except Exception as e:
        logger.error(f"Error getting female empowerment themes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting themes: {str(e)}")


@router.get("/mapper/info")
async def get_contextual_mapper_info() -> Dict[str, Any]:
    """
    Get information about the contextual theme mapper.
    
    Returns configuration and capabilities of the contextual mapping system.
    """
    return {
        "name": "Contextual Theme Mapper",
        "version": "1.0.0",
        "description": "Advanced theme mapping using character, staff, and contextual data",
        "features": [
            "Character demographic analysis",
            "Staff composition analysis", 
            "Genre combination detection",
            "Context-aware confidence scoring",
            "Multi-factor theme matching",
            "Detailed reasoning and transparency"
        ],
        "supported_themes": [
            "Female Agency in Restrictive Systems",
            "Subversion of Gender Expectations", 
            "Strength Through Adversity",
            "Hidden Female Networks/Solidarity",
            "Professional/Intellectual Female Excellence"
        ],
        "contextual_factors": [
            "Character gender and roles",
            "Staff gender and positions",
            "Historical settings",
            "Genre combinations",
            "Plot elements from tags"
        ]
    }
