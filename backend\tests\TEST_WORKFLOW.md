# Search-to-Display Testing Workflow

This document outlines the comprehensive testing strategy for the search-to-display workflow in the Tahimoto application, detailing test categories and specific test cases for each component.

## Workflow Overview

The search-to-display workflow involves the following high-level steps:

1. User submits a search query
2. API processes the search request and returns results
3. User selects a story from the results
4. API provides detailed information about the selected story
5. System provides recommendations based on the selected story

## Test Categories

### 1. Search Functionality Tests

These tests validate the story search API endpoint (`/api/v1/stories/search`).

#### Test Cases:
- **Basic Search Query**: 
  - Test search with a valid query returning multiple results
  - Verify pagination works correctly (page number and per_page parameters)
  - Verify response structure matches `StorySearchResults` schema

- **Edge Cases**:
  - Empty results (search for non-existent title)
  - Minimum query length validation
  - Maximum per_page enforcement (should be capped at 50)
  - Invalid page numbers (negative or zero)

- **Cache Behavior**:
  - Multiple identical searches should hit the cache after first request
  - Cache TTL expiration should trigger new API request

### 2. Story Retrieval Tests

These tests validate the story retrieval API endpoint (`/api/v1/stories/{story_id}`).

#### Test Cases:
- **Valid Story Retrieval**:
  - Get existing story by ID
  - Verify all expected fields are present
  - Verify media type and other categorical fields have expected values

- **Caching Behavior**:
  - First request should store in cache
  - Subsequent requests within TTL should use cache
  - Story update should invalidate cache

- **Error Handling**:
  - Non-existent story ID should return 404
  - Invalid story ID format should be handled appropriately
  - Test stale data detection and refresh logic

### 3. Recommendations Engine Tests

These tests validate the recommendations API endpoint (`/api/v1/stories/{story_id}/recommendations`).

#### Test Cases:
- **Neo4j Recommendation Source**:
  - Verify Neo4j returns recommendations when available
  - Test recommendation structure and relationship strength values
  - Verify source attribution is correct

- **AniList Fallback**:
  - When Neo4j has no recommendations, verify AniList API is called
  - Test that AniList recommendations are stored in Neo4j for future use
  - Verify correct transformation of AniList data to internal format

- **Filtering Logic**:
  - Test genre filtering (include and exclude specific genres)
  - Test minimum score filtering at various thresholds (0, 50, 75, 90)
  - Test status filtering (RELEASING, FINISHED, etc.)
  - Test limit parameter (min, max, and default values)

- **Response Format**:
  - Verify `filters_applied` section matches request parameters
  - Verify count field matches number of recommendations
  - Verify each recommendation has required fields (id, title, strength)

### 4. Integration Tests

These tests validate the entire workflow from search to recommendations.

#### Test Cases:
- **Full Workflow**:
  - Search for a story
  - Select result and get details
  - Get recommendations for the selected story
  - Verify all data is consistent across requests

- **Database Persistence**:
  - Verify search results are stored in Neo4j
  - Verify recommendations relationships are created and maintained
  - Test that repeat searches use database data when available

- **Error Recovery**:
  - Test behavior when external API is unavailable
  - Test recovery from database connection errors
  - Verify appropriate error messages are returned to client

### 5. Performance Tests

These tests measure the performance characteristics of the workflow.

#### Test Cases:
- **Response Time**:
  - Measure time for initial uncached search
  - Measure time for cached search responses
  - Measure time for recommendation retrieval (Neo4j vs AniList source)

- **Scaling Behavior**:
  - Test with increasing number of concurrent users
  - Test with large result sets
  - Profile Neo4j query performance under load

- **Cache Effectiveness**:
  - Measure cache hit ratio
  - Evaluate TTL settings effectiveness
  - Test cache memory utilization

## One Piece Test Case

We use One Piece (ID: 21) as a consistent test case because:

1. It's a popular anime with stable data
2. It has many recommendations
3. It has well-defined genres and metadata
4. It's likely to remain available in external APIs

### One Piece Specific Tests:
- Search for "One Piece" and verify results
- Get One Piece details and verify accuracy
- Get One Piece recommendations with various filters
- Verify One Piece metadata (genres, studios, relations)

## Test Implementation

Tests are implemented using pytest and httpx AsyncClient for API testing. The main test files are:

- `tests/api/stories/test_stories.py` - General story endpoint tests
- `tests/api/stories/test_one_piece.py` - One Piece specific tests

Run all story tests with:
```bash
docker exec tahimoto-backend python -m pytest tests/api/stories -v
```

Run specific recommendation tests with:
```bash
docker exec tahimoto-backend python -m pytest tests/api/stories/test_stories.py::TestStoriesEndpoints::test_get_recommendations -v
```

Run One Piece recommendation tests with:
```bash
docker exec tahimoto-backend python -m pytest tests/api/stories/test_one_piece.py::TestOneStoryEndpoints::test_one_piece_recommendations -v
```

## Continuous Integration

These tests should be integrated into the CI pipeline to ensure:

1. New changes don't break existing functionality
2. Database schema changes are compatible
3. External API integration remains working
4. Response formats stay consistent

## Known Limitations

- Tests requiring external API calls may fail if rate limits are exceeded
- Neo4j test database may need periodic cleanup for optimal performance
- Some tests assume specific data exists in AniList API 