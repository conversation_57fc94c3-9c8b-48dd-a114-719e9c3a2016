import pytest
import time
from app.core.redis import RedisConnection

pytestmark = pytest.mark.asyncio

async def test_redis_pipeline_bulk_operations():
    """Test the Redis pipeline bulk operations."""
    # Clean up any test keys
    test_keys = ["test:pipeline:1", "test:pipeline:2", "test:pipeline:3", "test:pipeline:bulk"]
    await RedisConnection.bulk_delete(test_keys)
    
    # Test bulk_set
    test_data = {
        "test:pipeline:1": "value1",
        "test:pipeline:2": "value2",
        "test:pipeline:3": "value3",
    }
    
    result = await RedisConnection.bulk_set(test_data, ex=60)  # 60 seconds expiry
    assert result is True, "Bulk set should succeed"
    
    # Test bulk_get
    values = await RedisConnection.bulk_get(list(test_data.keys()))
    assert len(values) == 3, "Should retrieve 3 values"
    assert values["test:pipeline:1"] == "value1"
    assert values["test:pipeline:2"] == "value2"
    assert values["test:pipeline:3"] == "value3"
    
    # Test bulk_get with non-existent key
    values = await RedisConnection.bulk_get(list(test_data.keys()) + ["test:nonexistent"])
    assert len(values) == 4, "Should have 4 entries in result"
    assert values["test:nonexistent"] is None, "Non-existent key should return None"
    
    # Test bulk_delete
    deleted_count = await RedisConnection.bulk_delete(list(test_data.keys()))
    assert deleted_count == 3, "Should delete 3 keys"
    
    # Verify keys are gone
    values = await RedisConnection.bulk_get(list(test_data.keys()))
    assert all(v is None for v in values.values()), "All values should be None after deletion"

async def test_raw_pipeline_usage():
    """Test using the raw pipeline API."""
    # Get a pipeline
    pipe = await RedisConnection.pipeline()
    assert pipe is not None, "Pipeline should be created"
    
    # Clean up any test keys
    await RedisConnection.delete("test:pipeline:raw")
    
    # Queue multiple operations
    pipe.set("test:pipeline:raw", "pipeline value", ex=60)
    pipe.get("test:pipeline:raw")
    pipe.ttl("test:pipeline:raw")
    
    # Execute all commands in a single network round-trip
    results = await pipe.execute()
    
    # Check results
    assert len(results) == 3, "Should have 3 results"
    assert results[0] is True, "SET should return True"
    assert results[1] == "pipeline value", "GET should return the set value"
    assert results[2] > 0, "TTL should be positive"
    
    # Clean up
    await RedisConnection.delete("test:pipeline:raw")

async def test_pipeline_performance():
    """Demonstrate performance benefits of using a pipeline."""
    # Number of operations to perform
    n = 100
    keys = [f"test:perf:{i}" for i in range(n)]
    values = {key: f"value{i}" for i, key in enumerate(keys)}
    
    # Clean up any leftover keys
    await RedisConnection.bulk_delete(keys)
    
    # Measure time for individual operations
    start_time = time.time()
    for key, value in values.items():
        await RedisConnection.set(key, value)
    individual_time = time.time() - start_time
    
    # Delete the keys
    await RedisConnection.bulk_delete(keys)
    
    # Measure time for bulk operations
    start_time = time.time()
    await RedisConnection.bulk_set(values)
    bulk_time = time.time() - start_time
    
    # Clean up
    await RedisConnection.bulk_delete(keys)
    
    # Assert that bulk operations are faster (usually by at least 10x)
    # We're not enforcing this as a hard test case because it depends on the
    # environment and Redis server connection speed
    assert bulk_time < individual_time, "Bulk operations should be faster than individual operations"
    print(f"\nPerformance comparison for {n} operations:")
    print(f"Individual operations: {individual_time:.4f} seconds")
    print(f"Pipeline bulk operations: {bulk_time:.4f} seconds")
    print(f"Speed improvement: {individual_time/bulk_time:.2f}x") 