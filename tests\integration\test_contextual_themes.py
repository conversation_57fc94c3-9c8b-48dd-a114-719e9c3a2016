#!/usr/bin/env python3
"""
Test script for Contextual Theme Analysis API

Tests the contextual theme analysis endpoints to verify theme detection works correctly.
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_contextual_themes():
    """Test the contextual theme analysis API."""
    
    print("🎯 Testing Contextual Theme Analysis API")
    print("=" * 50)
    
    # Test 1: Get mapper info
    print("\n1. Testing Mapper Info...")
    try:
        response = requests.get(f"{BASE_URL}/contextual-themes/mapper/info")
        response.raise_for_status()
        info = response.json()
        print(f"   ✅ Mapper: {info['name']} v{info['version']}")
        print(f"   📋 Features: {len(info['features'])} features")
        print(f"   🎭 Supported Themes: {len(info['supported_themes'])} themes")
        for theme in info['supported_themes']:
            print(f"      • {theme}")
    except Exception as e:
        print(f"   ❌ Mapper info error: {e}")
        return
    
    # Test 2: Get female empowerment themes
    print("\n2. Testing Female Empowerment Themes...")
    try:
        response = requests.get(f"{BASE_URL}/contextual-themes/themes/female-empowerment")
        response.raise_for_status()
        themes_data = response.json()
        themes = themes_data['themes']
        print(f"   ✅ Retrieved {themes_data['total_themes']} female empowerment themes")
        for theme_id, theme_info in themes.items():
            print(f"      • {theme_id}: {theme_info['name']} (base: {theme_info['base_confidence']})")
    except Exception as e:
        print(f"   ❌ Female empowerment themes error: {e}")
        return
    
    # Test 3: Analyze specific anime
    print("\n3. Testing Anime Contextual Analysis...")
    try:
        # The Apothecary Diaries
        request_data = {
            "anime_id": "161645",
            "include_reasoning": True
        }
        response = requests.post(f"{BASE_URL}/contextual-themes/anime/161645/contextual-themes", json=request_data)
        response.raise_for_status()
        analysis = response.json()
        
        print(f"   ✅ Analysis for: {analysis['anime_title']}")
        print(f"   📊 Found {len(analysis['theme_matches'])} theme matches")
        
        # Show top themes
        for i, match in enumerate(analysis['theme_matches'][:3], 1):
            print(f"   {i}. {match['theme_name']} ({match['confidence_percent']})")
            print(f"      Reasoning: {match['reasoning'][:100]}...")
        
        # Show character analysis
        char_analysis = analysis['character_analysis']
        print(f"   👥 Characters: {char_analysis['total_characters']} total")
        print(f"      Female MCs: {char_analysis['female_main_characters']}")
        if 'female_supporting_characters' in char_analysis:
            print(f"      Female Support: {char_analysis['female_supporting_characters']}")

        # Show staff analysis
        staff_analysis = analysis['staff_analysis']
        print(f"   🎬 Staff: {staff_analysis['total_staff']} total")
        if 'female_staff_count' in staff_analysis:
            print(f"      Female Staff: {staff_analysis['female_staff_count']}")
        elif 'female_staff' in staff_analysis:
            print(f"      Female Staff: {staff_analysis['female_staff']}")
        
    except Exception as e:
        print(f"   ❌ Anime analysis error: {e}")
        return
    
    # Test 4: Enhanced metadata analysis
    print("\n4. Testing Enhanced Metadata Analysis...")
    try:
        # Create mock enhanced metadata
        enhanced_metadata = {
            "title": "Test Anime",
            "genres": ["Drama", "Historical"],
            "characters": [
                {
                    "name": "Maomao",
                    "role": "MAIN",
                    "gender": "Female",
                    "age": 17,
                    "description": "Apothecary with medical knowledge"
                }
            ],
            "staff": [
                {
                    "role": "Director",
                    "name": "Norihiro Naganuma",
                    "gender": "Male"
                }
            ],
            "tags": [
                {"name": "Female Protagonist"},
                {"name": "Palace"},
                {"name": "Medicine"}
            ]
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/analyze-enhanced-metadata",
            json=enhanced_metadata
        )
        response.raise_for_status()
        analysis = response.json()
        
        print(f"   ✅ Enhanced analysis completed")
        print(f"   🎯 Theme matches: {len(analysis)}")

        for match in analysis:
            print(f"      • {match['theme_name']}: {match['confidence_percent']}")
            
    except Exception as e:
        print(f"   ❌ Enhanced metadata analysis error: {e}")
        return
    
    print(f"\n🎉 Contextual Theme Analysis API Test COMPLETED!")
    print(f"   ✅ All endpoints working correctly")
    print(f"   ✅ Theme detection: Character + Staff + Context analysis")
    print(f"   ✅ Confidence scoring with detailed reasoning")


if __name__ == "__main__":
    test_contextual_themes()
