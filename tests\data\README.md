# Test Data

This directory contains test data files used by various tests throughout the application.

## Data Files

### Anime Test Data
- **`test_anime.json`** - Sample anime data for testing anime-related functionality, theme mapping, and recommendations

### API Test Data
- **`test_request.json`** - Sample API request payloads for testing various endpoints and request formats

## Usage

Test data files can be loaded in tests using standard JSON loading:

```python
import json
import os

# Load test anime data
with open('tests/data/test_anime.json', 'r') as f:
    test_anime = json.load(f)

# Load test request data
with open('tests/data/test_request.json', 'r') as f:
    test_request = json.load(f)
```

## File Formats

All test data files are in JSON format for easy parsing and modification. When adding new test data:

1. Use descriptive filenames that indicate the type of test data
2. Include comments in the JSON where possible (using `_comment` fields)
3. Keep data realistic but avoid using real user data
4. Validate JSON format before committing

## Notes

- Test data should be representative of real data but not contain sensitive information
- Keep test data files small and focused on specific test scenarios
- Update test data when API schemas or data structures change
