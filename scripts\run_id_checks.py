#!/usr/bin/env python3
"""
Demo script for ID standardization checks.

This script demonstrates how the ID standardization system works by:
1. Creating example IDs in different formats
2. Showing how IdService standardizes them
3. Explaining how the validation scripts would check them
"""

import sys
import os
from pprint import pprint

# Import our mock IdService
from mock_id_service import IdService

def main():
    """Main demo function."""
    print("\n=== ID Standardization Demo ===\n")
    
    # Example IDs in different formats
    examples = {
        "theme": [
            "adventure",                              # No prefix
            "theme_adventure",                        # With prefix
            "7fc75255-6e7f-400f-a74c-2ec78668aaf3",   # UUID without prefix
            "theme_7fc75255-6e7f-400f-a74c-2ec78668aaf3"  # UUID with prefix
        ],
        "story": [
            "123",            # No prefix
            "story_123",      # With prefix
            "one_piece",      # Semantic ID without prefix
            "story_one_piece" # Semantic ID with prefix
        ],
        "mapping": [
            "abc123",                                # Simple ID without prefix
            "mapping_abc123",                        # Simple ID with prefix
            "7fc75255-6e7f-400f-a74c-2ec78668aaf3",   # UUID without prefix
            "mapping_7fc75255-6e7f-400f-a74c-2ec78668aaf3"  # UUID with prefix
        ]
    }
    
    print("1. Standardizing IDs\n")
    print("The IdService.standardize_id() method ensures all IDs have the correct prefix:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            standardized = IdService.standardize_id(id_value, entity_type)
            print(f"  {id_value:50} -> {standardized}")
    
    print("\n2. Database ID Format\n")
    print("The IdService.to_database_id() method converts IDs to the correct format for database storage:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            # First standardize the ID
            std_id = IdService.standardize_id(id_value, entity_type)
            # Then convert to database format
            db_id = IdService.to_database_id(std_id, entity_type)
            print(f"  {std_id:50} -> {db_id}")
    
    print("\n3. UUID Detection\n")
    print("The IdService can detect if an ID is a UUID or a semantic ID:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            is_uuid = IdService.is_uuid(id_value)
            is_semantic = IdService.is_semantic_id(id_value)
            print(f"  {id_value:50} -> UUID: {is_uuid}, Semantic: {is_semantic}")
    
    print("\n4. ID Validation\n")
    print("The IdService.validate_id() method checks if an ID follows the standard format:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            is_valid = IdService.validate_id(id_value, entity_type)
            if is_valid:
                result = "✓ VALID"
            else:
                std_id = IdService.standardize_id(id_value, entity_type)
                result = f"✗ INVALID - Should be: {std_id}"
            print(f"  {id_value:50} -> {result}")
    
    print("\n5. Finding by ID or Name\n")
    print("The IdService.find_by_id_or_name() method determines how to query for an entity:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            lookup = IdService.find_by_id_or_name(id_value, entity_type)
            print(f"  {id_value:50} -> {lookup['param_name']}: {lookup['param_value']}")
            print(f"                                                       condition: {lookup['condition']}")
    
    print("\n6. Cache Keys\n")
    print("The IdService.get_cache_key() method generates standardized cache keys:")
    
    for entity_type, ids in examples.items():
        print(f"\n{entity_type.upper()} IDs:")
        for id_value in ids:
            cache_key = IdService.get_cache_key(id_value, entity_type)
            cache_key_with_suffix = IdService.get_cache_key(id_value, entity_type, "stats")
            print(f"  {id_value:50} -> {cache_key}")
            print(f"                                                       with suffix: {cache_key_with_suffix}")
    
    # Bonus: Relationship IDs
    print("\n7. Relationship IDs\n")
    print("The IdService can create and parse relationship IDs:")
    
    theme1 = "adventure"
    theme2 = "journey"
    rel_type = "PARENT_OF"
    
    rel_id = IdService.create_relationship_id(theme1, rel_type, theme2)
    print(f"Creating relationship ID:")
    print(f"  {theme1} -{rel_type}-> {theme2} = {rel_id}")
    
    source_id, parsed_type, target_id = IdService.parse_relationship_id(rel_id)
    print(f"Parsing relationship ID:")
    print(f"  {rel_id} = {source_id} -{parsed_type}-> {target_id}")
    
    print("\n=== End of Demo ===\n")
    
    print("This demonstrates how the IdService works in your code.")
    print("The validation scripts would scan your entire codebase and database")
    print("to find IDs that don't match these standardization rules.")

if __name__ == "__main__":
    main() 