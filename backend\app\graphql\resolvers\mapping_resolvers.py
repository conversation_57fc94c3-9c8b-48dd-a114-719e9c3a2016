"""
GraphQL resolvers for theme mapping operations.
"""
import logging
from typing import Dict, List, Any
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme
import neo4j
from app.graphql.resolvers.utils.formatters import format_theme_mapping, format_theme
from app.graphql.resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

@query_resolvers.field("themeMapping")
async def resolve_theme_mapping(_, info, id: str):
    """Resolver for themeMapping query."""
    logger.debug(f"Resolving theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return None
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation
    mapping_data = await theme.get_relationship(
        session, 
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id
    )
    
    if not mapping_data:
        return None
    
    return format_theme_mapping(mapping_data)

@query_resolvers.field("themeMappings")
async def resolve_theme_mappings(_, info, sourceType=None, sourceId=None, themeId=None, limit=10, offset=0):
    """Resolver for themeMappings query."""
    logger.debug(f"Resolving theme mappings with filters: sourceType={sourceType}, sourceId={sourceId}, themeId={themeId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operations based on provided filters
    if sourceType and sourceId:
        # Get mappings for a specific source
        analysis = await theme.get_analysis(session, source_type=sourceType, source_id=sourceId)
        mappings = analysis["themes"]
    else:
        # This is a simplified implementation - in a real system, you'd need a more sophisticated query
        # that can handle various filter combinations
        logger.warning("Fetching all theme mappings is not yet implemented")
        mappings = []
    
    # Apply themeId filter if provided
    if themeId and mappings:
        mappings = [m for m in mappings if m["theme"]["id"] == themeId]
    
    # Apply pagination
    paginated_mappings = mappings[offset:offset+limit]
    
    return [format_theme_mapping(m) for m in paginated_mappings]

@mutation_resolvers.field("createThemeMapping")
async def resolve_create_theme_mapping(_, info, input):
    """Resolver for createThemeMapping mutation."""
    logger.debug(f"Creating theme mapping with input: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Extract input data
    source_type = input["sourceType"]
    source_id = input["sourceId"]
    theme_id = input["themeId"]
    
    # Prepare relationship data
    relationship_data = {
        "mapping_strength": input.get("mappingStrength", 1.0),
        "mapping_type": input.get("mappingType", "PRIMARY"),
        "notes": input.get("context"),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "llm_confidence": input.get("llmConfidence"),
        "needs_review": input.get("needsReview", False)
    }
    
    # Use Neo4j CRUD operation
    result = await theme.create_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME",
        relationship_data=relationship_data
    )
    
    if not result:
        return None
    
    return format_theme_mapping(result)

@mutation_resolvers.field("updateThemeMapping")
async def resolve_update_theme_mapping(_, info, id, input):
    """Resolver for updateThemeMapping mutation."""
    logger.debug(f"Updating theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return None
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Prepare update data
    update_data = {}
    if "mappingStrength" in input:
        update_data["mapping_strength"] = input["mappingStrength"]
    if "mappingType" in input:
        update_data["mapping_type"] = input["mappingType"]
    if "context" in input:
        update_data["notes"] = input["context"]
    if "llmConfidence" in input:
        update_data["llm_confidence"] = input["llmConfidence"]
    if "needsReview" in input:
        update_data["needs_review"] = input["needsReview"]
    
    # Add updated timestamp
    update_data["updated_at"] = datetime.now().isoformat()
    
    # Use Neo4j CRUD operation
    result = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME",
        relationship_data=update_data
    )
    
    if not result:
        return None
    
    return format_theme_mapping(result)

@mutation_resolvers.field("deleteThemeMapping")
async def resolve_delete_theme_mapping(_, info, id):
    """Resolver for deleteThemeMapping mutation."""
    logger.debug(f"Deleting theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return {"success": False, "id": id}
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation
    success = await theme.delete_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME"
    )
    
    return {"success": success, "id": id}

@mutation_resolvers.field("approveThemeMapping")
async def resolve_approve_theme_mapping(_, info, id):
    """Resolver for approveThemeMapping mutation."""
    logger.debug(f"Approving theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return {"success": False, "id": id}
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Prepare update data
    update_data = {
        "needs_review": False,
        "updated_at": datetime.now().isoformat()
    }
    
    # Use Neo4j CRUD operation
    result = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME",
        relationship_data=update_data
    )
    
    return {"success": bool(result), "id": id}

@mutation_resolvers.field("rejectThemeMapping")
async def resolve_reject_theme_mapping(_, info, id):
    """Resolver for rejectThemeMapping mutation."""
    logger.debug(f"Rejecting theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # We'll simply delete the mapping when rejected
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return {"success": False, "id": id}
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation
    success = await theme.delete_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME"
    )
    
    return {"success": success, "id": id}

@mutation_resolvers.field("requestReview")
async def resolve_request_review(_, info, id):
    """Resolver for requestReview mutation."""
    logger.debug(f"Requesting review for theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return {"success": False, "id": id}
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Prepare update data
    update_data = {
        "needs_review": True,
        "updated_at": datetime.now().isoformat()
    }
    
    # Use Neo4j CRUD operation
    result = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        relationship_type="HAS_THEME",
        relationship_data=update_data
    )
    
    return {"success": bool(result), "id": id} 