from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("db_session")

# Create async engine
try:
    logger.info("Initializing database engine")
    engine = create_async_engine(
        settings.SQLALCHEMY_DATABASE_URI,
        echo=True,  # Log all SQL
        future=True,
        pool_pre_ping=True,  # Enable connection health checks
        pool_size=5,  # Set reasonable pool size
        max_overflow=10,  # Maximum number of connections above pool_size
    )
    logger.info("Database engine initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database engine: {str(e)}")
    raise

# Create session factory
try:
    logger.debug("Creating session factory")
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=False,  # Don't autoflush - let services control this
    )
    logger.debug("Session factory created successfully")
except Exception as e:
    logger.error(f"Failed to create session factory: {str(e)}")
    raise

# Dependency to get DB sessions
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides a database session.
    Ensures proper handling of session lifecycle and error conditions.
    """
    session = None
    try:
        logger.debug("Creating new database session")
        session = async_session_maker()
        yield session
        
        try:
            logger.debug("Committing database session")
            await session.commit()
        except SQLAlchemyError as e:
            logger.error(f"Error during session commit: {str(e)}")
            await session.rollback()
            raise
            
    except SQLAlchemyError as e:
        logger.error(f"Database session error: {str(e)}")
        if session:
            await session.rollback()
        raise
        
    finally:
        if session:
            logger.debug("Closing database session")
            await session.close() 