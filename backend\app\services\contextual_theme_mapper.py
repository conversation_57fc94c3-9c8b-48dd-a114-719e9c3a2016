"""
Contextual Theme Mapper for Enhanced Theme Analysis

This service implements context-aware theme mapping that considers multiple factors:
- Character demographics (gender, role)
- Staff information (director, writer gender)
- Genre combinations
- Setting contexts
- Plot elements

Used for Phase 1 theme expansion with Female Empowerment themes.
"""

import logging
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

from app.data.female_empowerment_themes import (
    FEMALE_EMPOWERMENT_THEMES,
    ThemeDefinition,
    get_themes_by_context
)

logger = logging.getLogger(__name__)


@dataclass
class ThemeMatch:
    """Represents a theme match with confidence score and reasoning."""
    theme_id: str
    theme_name: str
    confidence: float
    reasoning: List[str]
    base_indicators: List[str]
    context_boosts: Dict[str, float]


class ContextualThemeMapper:
    """
    Advanced theme mapper that uses character, staff, and contextual data
    to provide more nuanced theme analysis.
    """
    
    def __init__(self):
        self.themes = FEMALE_EMPOWERMENT_THEMES
        logger.info("ContextualThemeMapper initialized with Female Empowerment themes")
    
    def analyze_story_themes(self, story_metadata: Dict[str, Any]) -> List[ThemeMatch]:
        """
        Analyze a story's metadata to identify relevant themes with confidence scores.
        
        Args:
            story_metadata: Enhanced metadata including characters, staff, genres, etc.
            
        Returns:
            List of ThemeMatch objects sorted by confidence score
        """
        logger.info("Starting contextual theme analysis")
        
        # Extract key information
        characters = story_metadata.get("characters", [])
        staff = story_metadata.get("staff", [])
        genres = story_metadata.get("genres", [])
        tags = story_metadata.get("tags", [])
        
        # Analyze character demographics
        char_analysis = self._analyze_characters(characters)
        
        # Analyze staff composition
        staff_analysis = self._analyze_staff(staff)
        
        # Analyze genre and setting context
        context_analysis = self._analyze_context(genres, tags)
        
        # Map themes based on combined analysis
        theme_matches = []
        
        for theme_id, theme_def in self.themes.items():
            match = self._evaluate_theme_match(
                theme_def, char_analysis, staff_analysis, context_analysis
            )
            
            if match.confidence > 0.3:  # Only include themes with reasonable confidence
                theme_matches.append(match)
        
        # Sort by confidence score (highest first)
        theme_matches.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"Found {len(theme_matches)} theme matches")
        return theme_matches
    
    def _analyze_characters(self, characters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze character demographics and roles."""
        analysis = {
            "total_characters": len(characters),
            "main_characters": [],
            "female_main_characters": [],
            "male_main_characters": [],
            "female_supporting": [],
            "has_female_mc": False,
            "female_mc_names": [],
            "character_traits": []
        }
        
        for char in characters:
            role = char.get("role", "").upper()
            gender = char.get("gender")
            name = char.get("name", "Unknown")
            
            if role == "MAIN":
                analysis["main_characters"].append(char)
                
                if gender == "Female":
                    analysis["female_main_characters"].append(char)
                    analysis["has_female_mc"] = True
                    analysis["female_mc_names"].append(name)
                elif gender == "Male":
                    analysis["male_main_characters"].append(char)
            
            elif role == "SUPPORTING" and gender == "Female":
                analysis["female_supporting"].append(char)
        
        # Determine character traits based on analysis
        if analysis["has_female_mc"]:
            analysis["character_traits"].append("female_main_character")
        
        if len(analysis["female_main_characters"]) > 1:
            analysis["character_traits"].append("multiple_female_leads")
        
        if len(analysis["female_supporting"]) >= 3:
            analysis["character_traits"].append("strong_female_cast")
        
        return analysis
    
    def _analyze_staff(self, staff: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze staff composition and roles."""
        analysis = {
            "total_staff": len(staff),
            "directors": [],
            "writers": [],
            "female_staff": [],
            "male_staff": [],
            "key_female_roles": [],
            "staff_traits": []
        }
        
        for person in staff:
            role = person.get("role", "")
            gender = person.get("gender")
            name = person.get("name", "Unknown")
            
            # Categorize by role
            if "Director" in role:
                analysis["directors"].append(person)
            
            if any(keyword in role for keyword in ["Script", "Writer", "Screenplay", "Series Composition"]):
                analysis["writers"].append(person)
            
            # Categorize by gender
            if gender == "Female":
                analysis["female_staff"].append(person)
                
                # Track key female roles
                if any(key_role in role for key_role in ["Director", "Writer", "Character Design", "Series Composition"]):
                    analysis["key_female_roles"].append({"name": name, "role": role})
            
            elif gender == "Male":
                analysis["male_staff"].append(person)
        
        # Determine staff traits
        female_directors = [d for d in analysis["directors"] if d.get("gender") == "Female"]
        female_writers = [w for w in analysis["writers"] if w.get("gender") == "Female"]
        
        if female_directors:
            analysis["staff_traits"].append("female_director")
        
        if female_writers:
            analysis["staff_traits"].append("female_writer")
        
        if len(analysis["female_staff"]) > len(analysis["male_staff"]):
            analysis["staff_traits"].append("female_staff_majority")
        
        if len(analysis["key_female_roles"]) >= 2:
            analysis["staff_traits"].append("significant_female_creative_input")
        
        return analysis
    
    def _analyze_context(self, genres: List[str], tags: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze genre and setting context."""
        analysis = {
            "genres": [g.lower() for g in genres],
            "genre_combinations": [],
            "setting_contexts": [],
            "plot_elements": [],
            "context_traits": []
        }
        
        # Create genre combinations
        genre_lower = [g.lower() for g in genres]
        if len(genre_lower) >= 2:
            for i, g1 in enumerate(genre_lower):
                for g2 in genre_lower[i+1:]:
                    analysis["genre_combinations"].append(f"{g1} + {g2}")
        
        # Extract setting and plot contexts from tags
        for tag in tags:
            tag_name = tag.get("name", "").lower()
            category = tag.get("category", "").lower()
            
            # Identify setting contexts
            if any(setting in tag_name for setting in [
                "historical", "school", "workplace", "court", "palace", 
                "traditional", "feudal", "imperial", "medical", "academic"
            ]):
                analysis["setting_contexts"].append(tag_name)
            
            # Identify plot elements
            if any(plot in tag_name for plot in [
                "politics", "mystery", "investigation", "problem solving",
                "leadership", "mentorship", "friendship", "rivalry"
            ]):
                analysis["plot_elements"].append(tag_name)
        
        # Determine context traits
        if "historical" in analysis["genres"] or any("historical" in ctx for ctx in analysis["setting_contexts"]):
            analysis["context_traits"].append("historical_setting")
        
        if "mystery" in analysis["genres"]:
            analysis["context_traits"].append("mystery_elements")
        
        if "drama" in analysis["genres"]:
            analysis["context_traits"].append("drama_heavy")
        
        if "slice of life" in analysis["genres"]:
            analysis["context_traits"].append("slice_of_life_genre")
        
        return analysis
    
    def _evaluate_theme_match(
        self, 
        theme_def: ThemeDefinition, 
        char_analysis: Dict[str, Any],
        staff_analysis: Dict[str, Any], 
        context_analysis: Dict[str, Any]
    ) -> ThemeMatch:
        """Evaluate how well a theme matches the story based on all factors."""
        
        confidence = theme_def.confidence_base
        reasoning = []
        base_indicators = []
        context_boosts = {}
        
        # Check character trait matches
        char_matches = set(char_analysis["character_traits"]) & set(theme_def.indicators.character_traits)
        if char_matches:
            confidence += len(char_matches) * 0.1
            base_indicators.extend(list(char_matches))
            reasoning.append(f"Character traits: {', '.join(char_matches)}")
        
        # Check staff correlation matches
        staff_matches = set(staff_analysis["staff_traits"]) & set(theme_def.indicators.staff_correlations)
        if staff_matches:
            confidence += len(staff_matches) * 0.05
            base_indicators.extend(list(staff_matches))
            reasoning.append(f"Staff correlations: {', '.join(staff_matches)}")
        
        # Check context matches
        context_matches = set(context_analysis["context_traits"]) & set(theme_def.context_modifiers.keys())
        for match in context_matches:
            boost = theme_def.context_modifiers[match]
            confidence += boost
            context_boosts[match] = boost
            reasoning.append(f"Context boost '{match}': +{boost:.2f}")
        
        # Special combination bonuses
        if char_analysis["has_female_mc"] and "historical_setting" in context_analysis["context_traits"]:
            if "female_mc_and_historical" in theme_def.context_modifiers:
                boost = theme_def.context_modifiers["female_mc_and_historical"]
                confidence += boost
                context_boosts["female_mc_and_historical"] = boost
                reasoning.append(f"Female MC + Historical setting: +{boost:.2f}")
        
        # Cap confidence at 1.0
        confidence = min(confidence, 1.0)
        
        return ThemeMatch(
            theme_id=theme_def.id,
            theme_name=theme_def.name,
            confidence=confidence,
            reasoning=reasoning,
            base_indicators=base_indicators,
            context_boosts=context_boosts
        )
