import pytest
from httpx import AsyncClient
from sqlalchemy.orm import Session
from app.crud.story import story as crud_story
from app.schemas.story import StoryCreate

@pytest.mark.asyncio
async def test_get_recommendations(client: AsyncClient, db: Session):
    """Test getting recommendations for a story."""
    # Create a test story with recommendations
    story = await crud_story.create(
        db,
        obj_in=StoryCreate(
            external_id="test-123",
            title_romaji="Test Story",
            media_type="ANIME",
            source="anilist",
            story_metadata={
                "genres": ["Action", "Drama"],
                "tags": [],
                "studios": [],
                "relations": [
                    {
                        "id": 1,
                        "title": "Test Recommendation 1",
                        "type": "SEQUEL",
                        "media_type": "ANIME",
                        "format": "TV",
                        "status": "Finished",
                        "source": "ORIGINAL",
                        "average_score": 85,
                        "genres": ["Action", "Drama"],
                        "recommendation_strength": 90
                    },
                    {
                        "id": 2,
                        "title": "Test Recommendation 2",
                        "type": "PREQUEL",
                        "media_type": "ANIME",
                        "format": "TV",
                        "status": "Currently Airing",
                        "source": "ORIGINAL",
                        "average_score": 75,
                        "genres": ["Comedy", "Romance"],
                        "recommendation_strength": 70
                    }
                ]
            }
        )
    )

    # Test getting all recommendations
    response = await client.get(f"/api/v1/stories/{story.id}/recommendations")
    assert response.status_code == 200
    data = response.json()
    assert data["story_id"] == story.id
    assert len(data["recommendations"]) == 2
    assert data["total_recommendations"] == 2

    # Test filtering by genre
    response = await client.get(
        f"/api/v1/stories/{story.id}/recommendations",
        params={"genres": ["Action"]}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data["recommendations"]) == 1
    assert data["recommendations"][0]["title"] == "Test Recommendation 1"

    # Test filtering by minimum score
    response = await client.get(
        f"/api/v1/stories/{story.id}/recommendations",
        params={"min_score": 80}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data["recommendations"]) == 1
    assert data["recommendations"][0]["average_score"] >= 80

    # Test filtering by status
    response = await client.get(
        f"/api/v1/stories/{story.id}/recommendations",
        params={"status": "Currently Airing"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data["recommendations"]) == 1
    assert data["recommendations"][0]["title"] == "Test Recommendation 2"

@pytest.mark.asyncio
async def test_get_recommendations_not_found(client: AsyncClient):
    """Test getting recommendations for a non-existent story."""
    response = await client.get("/api/v1/stories/999999/recommendations")
    assert response.status_code == 404
    assert response.json()["detail"] == "Story not found"

@pytest.mark.asyncio
async def test_get_recommendations_with_invalid_filters(client: AsyncClient, db: Session):
    """Test getting recommendations with invalid filter values."""
    story = await crud_story.create(
        db,
        obj_in=StoryCreate(
            external_id="test-456",
            title_romaji="Test Story",
            media_type="ANIME",
            source="anilist",
            story_metadata={"relations": []}
        )
    )

    # Test invalid min_score
    response = await client.get(
        f"/api/v1/stories/{story.id}/recommendations",
        params={"min_score": 101}  # Score should be 0-100
    )
    assert response.status_code == 422

    # Test invalid limit
    response = await client.get(
        f"/api/v1/stories/{story.id}/recommendations",
        params={"limit": 26}  # Limit should be 1-25
    )
    assert response.status_code == 422 