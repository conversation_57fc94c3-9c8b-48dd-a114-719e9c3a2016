"""
Test the integration between Neo4j operations and Redis caching.
This file focuses on how Neo4j writes affect Redis caches and how Redis caches are used during Neo4j reads.
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from app.core.redis import RedisConnection
from app.crud.neo4j.theme import CRUDTheme
from app.crud.neo4j.story import CRUDStory
from app.services.theme_redis import ThemeRedisService
from app.services.theme_analysis import ThemeAnalysisService

# Test data
TEST_STORY = {
    "id": "story_42",
    "title_english": "Test Story",
    "title_romaji": "Tesuto Sutōrī",
    "external_id": "42",
    "media_type": "ANIME",
    "status": "FINISHED",
    "genres": ["Action", "Adventure"],
    "tags": [{"name": "Magic", "category": "Setting"}],
    "cover_image": "https://example.com/cover.jpg",
    "banner_image": "https://example.com/banner.jpg",
    "popularity": 12345,
    "average_score": 85,
    "updated_at": datetime.now().isoformat()
}

TEST_THEME = {
    "id": "theme_abc123",
    "name": "Coming of Age",
    "description": "A journey of personal growth and maturation",
    "status": "ACTIVE",
    "created_at": datetime.now().isoformat(),
    "updated_at": datetime.now().isoformat()
}

@pytest.fixture
def neo4j_session_mock():
    """Mock Neo4j session."""
    session = AsyncMock()
    record_mock = MagicMock()
    record_mock.__getitem__.return_value = TEST_STORY
    session.run.return_value.single.return_value = record_mock
    
    # For multiple records (like search results)
    record_list = [record_mock]
    session.run.return_value.__aiter__.return_value = record_list
    
    return session

@pytest.fixture
def redis_mock():
    """Mock Redis client."""
    redis_instance = AsyncMock()
    # Default behavior - no cache hits
    redis_instance.get.return_value = None
    return redis_instance

@pytest.mark.asyncio
class TestNeo4jRedisIntegration:
    """Test the integration between Neo4j operations and Redis caching."""
    
    @pytest.mark.skip(reason="Requires proper cache invalidation implementation")
    async def test_cache_invalidation_on_write(self, monkeypatch, redis_mock):
        """Test that cache is invalidated when Neo4j data is written."""
        # Setup Redis mock
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "get_keys", AsyncMock(return_value=["theme:theme_abc123"]))
        
        # Track deleted keys
        deleted_keys = []
        
        async def mock_delete(key):
            deleted_keys.append(key)
            return True
            
        redis_mock.delete = mock_delete
        
        # Create services
        theme_redis = ThemeRedisService()
        theme_crud = CRUDTheme()
        
        # Patch the theme_crud.update method to call cache invalidation
        original_update = theme_crud.update
        
        async def patched_update(theme_id, update_data):
            # Call original update
            result = await original_update(theme_id, update_data)
            
            # Call cache invalidation
            await theme_redis.invalidate_related_caches("theme", theme_id)
            
            return result
            
        monkeypatch.setattr(theme_crud, "update", patched_update)
        
        # Update a theme
        await theme_crud.update("theme_abc123", {"name": "Updated Theme"})
        
        # Check that the cache was invalidated
        assert "theme:theme_abc123" in deleted_keys
    
    @pytest.mark.skip(reason="Requires theme_redis attribute in app.crud.neo4j.theme module")
    async def test_cache_read_before_neo4j(self, monkeypatch, redis_mock):
        """Test that cache is checked before Neo4j database."""
        # Setup Redis mock to return a cached result
        redis_mock.get.return_value = json.dumps({
            "id": "theme_test",
            "name": "Cached Theme",
            "description": "This theme is cached"
        })
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "exists", AsyncMock(return_value=True))
        
        # Setup Neo4j mock to track calls
        neo4j_calls = []
        
        # Patch Neo4j session to track calls
        async def mock_neo4j_execute(*args, **kwargs):
            neo4j_calls.append(args)
            # Return a different result than cache to verify source
            return [{"t": {"id": "theme_test", "name": "Database Theme"}}]
            
        # Import and patch the actual module that needs the theme_redis attribute
        import app.crud.neo4j.theme
        
        # Create and attach a Redis service to the module
        app.crud.neo4j.theme.theme_redis = ThemeRedisService()
        
        # Now patch the execute method
        monkeypatch.setattr(app.crud.neo4j.theme, "_execute_query", mock_neo4j_execute)
        
        # Test the get method
        theme_crud = CRUDTheme()
        theme = await theme_crud.get("theme_test")
        
        # Verify that we got the cached version, not the Neo4j version
        assert theme["name"] == "Cached Theme"
        
        # Verify Neo4j was not called
        assert len(neo4j_calls) == 0
    
    async def test_ttl_settings_compliance(self, monkeypatch, redis_mock):
        """Test that TTL settings comply with the optimization document recommendations."""
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Track set calls with TTL
        ttl_values = {}
        
        async def mock_set(key, value, ex=None, px=None, nx=False, xx=False):
            if ex is not None:
                ttl_values[key] = ex
            return True
            
        redis_mock.set = mock_set
        
        # Set various types of data
        await theme_redis.set_theme("theme_abc123", TEST_THEME)
        await theme_redis.set_analysis("story", "story_42", {"themes": []})
        
        # Test the set_stats method we implemented
        stats_data = {
            "total_themes": 150,
            "mapped_count": 120,
            "needs_review_count": 30,
            "cache_hit_rate": 0.85
        }
        await theme_redis.set_stats(stats_data)
        
        # Verify TTLs match the optimization document recommendations
        # From Optimization_and_Caching.md:
        # Theme mappings: 7200 (2 hours)
        # Story details: 3600 (1 hour)
        
        theme_key = next((k for k in ttl_values.keys() if "theme:theme_abc123" in k), None)
        analysis_key = next((k for k in ttl_values.keys() if "analysis:story:story_42" in k), None)
        stats_key = next((k for k in ttl_values.keys() if "theme_stats:global" in k), None)
        
        assert theme_key is not None, "Theme cache key not found"
        assert analysis_key is not None, "Analysis cache key not found"
        assert stats_key is not None, "Stats cache key not found"
        
        # Theme data should have longer TTL (closer to 7200)
        assert ttl_values[theme_key] >= 3600, "Theme TTL should be at least 1 hour"
        # Analysis data should have medium TTL
        assert 1800 <= ttl_values[analysis_key] <= 3600, "Analysis TTL should be between 30 min and 1 hour"
        # Stats data should have short TTL
        assert ttl_values[stats_key] <= 600, "Stats TTL should be at most 10 minutes"
    
    @pytest.mark.skip(reason="Requires implementation of cache update detection")
    async def test_stale_data_detection(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test that stale data in Redis cache is properly detected and refreshed."""
        # Create a story with older timestamp in cache
        now = datetime.now()
        stale_date = (now - timedelta(days=2)).isoformat()
        fresh_date = now.isoformat()
        
        # Stale story in cache
        stale_story = dict(TEST_STORY)
        stale_story["updated_at"] = stale_date
        
        # Fresh story in Neo4j
        fresh_story = dict(TEST_STORY)
        fresh_story["updated_at"] = fresh_date
        fresh_story["title_english"] = "Updated Test Story"  # Some change to verify refresh
        
        # First simulate cache hit with stale data
        redis_mock.get.return_value = json.dumps(stale_story)
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Setup Neo4j to return fresh data
        record_mock = MagicMock()
        record_mock.__getitem__.return_value = fresh_story
        neo4j_session_mock.run.return_value.single.return_value = record_mock
        
        # Track calls to set cache
        cache_updates = []
        
        async def mock_set(key, value, ex=None, px=None, nx=False, xx=False):
            cache_updates.append((key, json.loads(value)))
            return True
            
        redis_mock.set = mock_set
        
        # Create CRUD instance
        crud_story = CRUDStory()
        
        # Create a custom is_stale function to detect our test case
        def is_stale(data):
            """Check if data is stale based on updated_at."""
            if not data or "updated_at" not in data:
                return True
                
            data_date = data["updated_at"]
            # If data is older than 1 day, consider it stale
            try:
                data_timestamp = datetime.fromisoformat(data_date)
                cutoff = now - timedelta(days=1)
                return data_timestamp < cutoff
            except (ValueError, TypeError):
                return True
        
        # Patch the is_stale method
        monkeypatch.setattr(crud_story, "is_stale", is_stale)
        
        # Fetch the story - should detect stale data and refresh from Neo4j
        result = await crud_story.get(neo4j_session_mock, id="story_42")
        
        # Verify the result is the fresh data
        assert result["title_english"] == "Updated Test Story"
        
        # Verify the cache was updated with fresh data
        assert len(cache_updates) > 0, "Cache should be updated with fresh data"
        
        # Find the story cache update
        story_update = next((v for k, v in cache_updates if "story_42" in k), None)
        assert story_update is not None, "Story cache update not found"
        assert story_update["title_english"] == "Updated Test Story"
        assert story_update["updated_at"] == fresh_date
    
    @pytest.mark.skip(reason="Pipeline implementation needed for bulk_operations")
    async def test_bulk_operations_performance(self, monkeypatch, redis_mock):
        """Test that bulk operations use pipeline for performance."""
        # Mock Redis to verify pipeline usage
        pipeline_used = False
        
        # Create a mock pipeline
        pipeline = AsyncMock()
        redis_mock.pipeline.return_value = pipeline
        pipeline.execute.return_value = [True] * 10  # Simulate success for 10 operations
        
        # Mock the pipeline method
        def mock_pipeline():
            nonlocal pipeline_used
            pipeline_used = True
            return pipeline
            
        redis_mock.pipeline = mock_pipeline
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create service
        theme_redis = ThemeRedisService()
        
        # Create bulk operations
        operations = [
            {"type": "set", "cache_type": "theme", "key": f"theme_{i}", "value": {"id": f"theme_{i}", "name": f"Theme {i}"}}
            for i in range(10)
        ]
        
        # Execute bulk operations
        results = await theme_redis.bulk_operation(operations)
        
        # Verify pipeline was used
        assert pipeline_used, "Pipeline should have been used for bulk operations"
        
        # Verify operations were successful
        assert results["set"] > 0
        assert results["error"] == 0
    
    async def test_cache_invalidation_on_write(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test that Neo4j writes invalidate the relevant Redis caches."""
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create spies for Redis operations
        delete_keys = []
        
        async def mock_delete(*keys):
            delete_keys.extend(keys)
            return len(keys)
            
        redis_mock.delete = mock_delete
        
        # Create CRUD instances
        crud_theme = CRUDTheme()
        
        # 1. Update a theme in Neo4j
        await crud_theme.update(
            neo4j_session_mock,
            id="theme_abc123",
            obj_in={"name": "Updated Theme Name", "description": "New description"}
        )
        
        # Verify Redis keys were invalidated
        assert any("theme:theme_abc123" in key for key in delete_keys), "Theme cache was not invalidated"
        
        # 2. Create a relationship in Neo4j
        relationship_data = {
            "source_id": "story_42",
            "theme_id": "theme_abc123",
            "mapping_type": "PRIMARY",
            "mapping_strength": 0.9
        }
        
        # Clear previous calls
        delete_keys.clear()
        
        # Create relationship
        await crud_theme.create_relationship(
            neo4j_session_mock,
            source_type="story",
            source_id="42",  # Note: This should be normalized to story_42
            theme_id="theme_abc123",
            mapping_type="PRIMARY",
            mapping_strength=0.9
        )
        
        # Verify both story and theme caches were invalidated
        assert any("story:story_42" in key for key in delete_keys), "Story cache was not invalidated"
        assert any("theme:theme_abc123" in key for key in delete_keys), "Theme cache was not invalidated"
        
        # 3. Delete a theme relationship
        delete_keys.clear()
        
        await crud_theme.remove_relationship(
            neo4j_session_mock,
            source_type="story",
            source_id="42",
            theme_id="theme_abc123"
        )
        
        # Verify both caches were invalidated again
        assert any("story:story_42" in key for key in delete_keys), "Story cache was not invalidated on relationship deletion"
        assert any("theme:theme_abc123" in key for key in delete_keys), "Theme cache was not invalidated on relationship deletion"
    
    async def test_cache_read_before_neo4j(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test that Redis cache is checked before performing Neo4j operations."""
        # Setup ThemeRedisService
        theme_redis = ThemeRedisService()
        
        # Create a spy to track Neo4j calls
        neo4j_calls = []
        original_run = neo4j_session_mock.run
        
        async def spy_run(query, params=None):
            neo4j_calls.append((query, params))
            return await original_run(query, params)
            
        neo4j_session_mock.run = spy_run
        
        # First test: Cache miss, should query Neo4j
        # Patch Redis to return no data
        redis_mock.get.return_value = None
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create CRUD instance
        crud_theme = CRUDTheme()
        
        # Patch the theme_redis in crud module
        with patch("app.crud.neo4j.theme.theme_redis", theme_redis):
            # Attempt to get a theme
            theme = await crud_theme.get(neo4j_session_mock, id="theme_abc123")
            
            # Verify Neo4j was queried
            assert len(neo4j_calls) == 1, "Neo4j should be queried on cache miss"
            assert "MATCH (t:Theme {id: $id})" in neo4j_calls[0][0], "Neo4j query should look for theme"
        
        # Second test: Cache hit, should not query Neo4j
        # Clear previous calls
        neo4j_calls.clear()
        
        # Simulate cache hit
        cached_theme = dict(TEST_THEME)
        redis_mock.get.return_value = json.dumps(cached_theme)
        
        # Create fresh instances to avoid state from previous test
        crud_theme = CRUDTheme()
        
        with patch("app.crud.neo4j.theme.theme_redis", theme_redis):
            # Attempt to get the same theme
            theme = await crud_theme.get(neo4j_session_mock, id="theme_abc123")
            
            # Verify Neo4j was NOT queried
            assert len(neo4j_calls) == 0, "Neo4j should not be queried on cache hit"
            
            # Verify we got the cached theme data
            assert theme["id"] == "theme_abc123"
            assert theme["name"] == "Coming of Age"
    
    async def test_stale_data_detection(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test that stale data in Redis cache is properly detected and refreshed."""
        # Create a story with older timestamp in cache
        now = datetime.now()
        stale_date = (now - timedelta(days=2)).isoformat()
        fresh_date = now.isoformat()
        
        # Stale story in cache
        stale_story = dict(TEST_STORY)
        stale_story["updated_at"] = stale_date
        
        # Fresh story in Neo4j
        fresh_story = dict(TEST_STORY)
        fresh_story["updated_at"] = fresh_date
        fresh_story["title_english"] = "Updated Test Story"  # Some change to verify refresh
        
        # First simulate cache hit with stale data
        redis_mock.get.return_value = json.dumps(stale_story)
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Setup Neo4j to return fresh data
        record_mock = MagicMock()
        record_mock.__getitem__.return_value = fresh_story
        neo4j_session_mock.run.return_value.single.return_value = record_mock
        
        # Track calls to set cache
        cache_updates = []
        
        async def mock_set(key, value, ex=None, px=None, nx=False, xx=False):
            cache_updates.append((key, json.loads(value)))
            return True
            
        redis_mock.set = mock_set
        
        # Create CRUD instance
        crud_story = CRUDStory()
        
        # Create a custom is_stale function to detect our test case
        def is_stale(data):
            """Check if data is stale based on updated_at."""
            if not data or "updated_at" not in data:
                return True
                
            data_date = data["updated_at"]
            # If data is older than 1 day, consider it stale
            try:
                data_timestamp = datetime.fromisoformat(data_date)
                cutoff = now - timedelta(days=1)
                return data_timestamp < cutoff
            except (ValueError, TypeError):
                return True
        
        # Patch the is_stale method
        monkeypatch.setattr(crud_story, "is_stale", is_stale)
        
        # Fetch the story - should detect stale data and refresh from Neo4j
        result = await crud_story.get(neo4j_session_mock, id="story_42")
        
        # Verify the result is the fresh data
        assert result["title_english"] == "Updated Test Story"
        
        # Verify the cache was updated with fresh data
        assert len(cache_updates) > 0, "Cache should be updated with fresh data"
        
        # Find the story cache update
        story_update = next((v for k, v in cache_updates if "story_42" in k), None)
        assert story_update is not None, "Story cache update not found"
        assert story_update["title_english"] == "Updated Test Story"
        assert story_update["updated_at"] == fresh_date
    
    async def test_bulk_operations_performance(self, monkeypatch, redis_mock):
        """Test that bulk operations are more efficient than individual operations."""
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Track individual operation calls vs pipeline calls
        individual_calls = []
        pipeline_calls = 0
        
        # Mock Redis operations
        async def mock_set(key, value, *args, **kwargs):
            individual_calls.append(("set", key))
            return True
            
        async def mock_delete(key):
            individual_calls.append(("delete", key))
            return True
            
        redis_mock.set = mock_set
        redis_mock.delete = mock_delete
        
        # Mock pipeline
        def mock_pipeline():
            nonlocal pipeline_calls
            pipeline_calls += 1
            return redis_mock
            
        redis_mock.pipeline = mock_pipeline
        
        # Create data for bulk operations
        bulk_operations = [
            {
                "operation": "set",
                "cache_type": "theme",
                "key": f"theme_{i}",
                "value": {"id": f"theme_{i}", "name": f"Theme {i}"}
            } for i in range(10)
        ]
        
        bulk_operations.extend([
            {
                "operation": "delete",
                "cache_type": "theme",
                "key": f"theme_{i}"
            } for i in range(10, 20)
        ])
        
        # Execute bulk operations
        result = await theme_redis.bulk_operation(bulk_operations)
        
        # Verify pipeline was used
        assert pipeline_calls > 0, "Pipeline should be used for bulk operations"
        
        # Now test individual operations
        individual_calls.clear()
        
        # Perform the same operations individually
        for op in bulk_operations:
            if op["operation"] == "set":
                await theme_redis.set_theme(op["key"], op["value"])
            elif op["operation"] == "delete":
                await theme_redis.delete_theme(op["key"])
        
        # Verify individual calls were made
        assert len(individual_calls) == len(bulk_operations), "Individual operations should make one call per operation"
        
        # Bulk operation should involve fewer Redis calls than individual operations
        # Even though the actual implementation can't be fully tested with mocks,
        # this verifies that the patterns are there
        assert pipeline_calls <= len(individual_calls), "Bulk operation should be more efficient than individual operations" 