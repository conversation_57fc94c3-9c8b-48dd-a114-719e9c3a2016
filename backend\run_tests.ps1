# Master test script for Tahimoto backend

param(
    [Parameter(Position=0)]
    [ValidateSet("all", "component", "api", "resolver", "neo4j", "anilist", "infrastructure", "redis")]
    [string]$TestType = "all",
    
    [switch]$RebuildContainer = $false
)

Write-Host "=== Tahimoto Backend Test Runner ===" -ForegroundColor Cyan

# Check if Docker is running
try {
    $dockerStatus = docker info 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "ERROR: Docker is not installed or not in the PATH. Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

# Check if container exists
$containerExists = docker ps -a --format "{{.Names}}" | Select-String -Pattern "tahimoto-backend"
if (-not $containerExists) {
    Write-Host "WARNING: Container 'tahimoto-backend' does not exist." -ForegroundColor Yellow
    $createContainer = Read-Host "Would you like to start the containers with docker-compose? (y/n)"
    if ($createContainer -eq "y") {
        $RebuildContainer = $true
    } else {
        Write-Host "ERROR: Cannot run tests without the tahimoto-backend container." -ForegroundColor Red
        exit 1
    }
} else {
    # Check if container is running
    $containerRunning = docker ps --format "{{.Names}}" | Select-String -Pattern "tahimoto-backend"
    if (-not $containerRunning) {
        Write-Host "WARNING: Container 'tahimoto-backend' exists but is not running." -ForegroundColor Yellow
        $startContainer = Read-Host "Would you like to start the container? (y/n)"
        if ($startContainer -eq "y") {
            Write-Host "Starting container..." -ForegroundColor Yellow
            docker start tahimoto-backend
        } else {
            Write-Host "ERROR: Cannot run tests without the tahimoto-backend container running." -ForegroundColor Red
            exit 1
        }
    }
}

# Rebuild containers if requested
if ($RebuildContainer) {
    Write-Host "Rebuilding Docker containers..." -ForegroundColor Yellow
    docker-compose down
    docker-compose build --no-cache backend
    docker-compose up -d
    
    # Wait for services to be ready
    Write-Host "Waiting for services to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
}

# Function to run tests
function Run-Tests {
    param (
        [string]$TestName,
        [string]$Command
    )
    Write-Host "Testing $TestName..." -ForegroundColor Yellow
    Write-Host "Running command: $Command" -ForegroundColor Gray
    Invoke-Expression $Command
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "$TestName tests failed with exit code $LASTEXITCODE" -ForegroundColor Red
        return $false
    } else {
        Write-Host "$TestName tests completed successfully" -ForegroundColor Green
        return $true
    }
}

# Counter for successful tests
$successCount = 0
$totalTests = 0

# Run tests based on the selected type
switch ($TestType) {
    "all" {
        $totalTests = 5
        # Neo4j connection test
        if (Run-Tests "Neo4j connection" "docker exec tahimoto-backend python -m tests.infrastructure.test_neo4j_connection") { $successCount++ }
        
        # GraphQL resolver tests
        if (Run-Tests "GraphQL resolvers" "docker exec tahimoto-backend python run_resolver_tests.py") { $successCount++ }
        
        # AniList integration tests
        if (Run-Tests "AniList integration" "docker exec tahimoto-backend python -m tests.infrastructure.test_anilist_integration") { $successCount++ }
        
        # Component tests
        if (Run-Tests "component tests" "docker exec tahimoto-backend python -m pytest tests/api/stories/components -v") { $successCount++ }
        
        # API tests
        if (Run-Tests "API tests" "docker exec tahimoto-backend python -m pytest tests/api/stories/test_stories.py tests/api/stories/test_one_piece.py -v") { $successCount++ }
    }
    "component" {
        $totalTests = 9  # Updated count to include new Redis integration tests
        # Individual component tests
        if (Run-Tests "search component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_search_component.py -v") { $successCount++ }
        if (Run-Tests "story retrieval component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_story_retrieval_component.py -v") { $successCount++ }
        if (Run-Tests "recommendations component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_recommendations_component.py -v") { $successCount++ }
        if (Run-Tests "Neo4j queries component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_neo4j_queries.py -v") { $successCount++ }
        if (Run-Tests "cache component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_cache_component.py -v") { $successCount++ }
        if (Run-Tests "advanced Redis tests" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_redis_advanced.py -v") { $successCount++ }
        if (Run-Tests "Neo4j-Redis integration" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_neo4j_redis_integration.py -v") { $successCount++ }
        if (Run-Tests "search-to-display workflow" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_search_to_display_workflow.py -v") { $successCount++ }
        if (Run-Tests "all component tests together" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/ -v") { $successCount++ }
    }
    "api" {
        $totalTests = 1
        if (Run-Tests "API tests" "docker exec tahimoto-backend python -m pytest tests/api/stories/test_stories.py tests/api/stories/test_one_piece.py -v") { $successCount++ }
    }
    "resolver" {
        $totalTests = 1
        if (Run-Tests "GraphQL resolvers" "docker exec tahimoto-backend python run_resolver_tests.py") { $successCount++ }
    }
    "neo4j" {
        $totalTests = 1
        if (Run-Tests "Neo4j connection" "docker exec tahimoto-backend python -m tests.infrastructure.test_neo4j_connection") { $successCount++ }
    }
    "anilist" {
        $totalTests = 1
        if (Run-Tests "AniList integration" "docker exec tahimoto-backend python -m tests.infrastructure.test_anilist_integration") { $successCount++ }
    }
    "infrastructure" {
        $totalTests = 1
        if (Run-Tests "infrastructure tests" "docker exec tahimoto-backend python -m pytest tests/infrastructure -v") { $successCount++ }
    }
    "redis" {
        $totalTests = 3
        # Redis-specific tests
        if (Run-Tests "basic cache component" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_cache_component.py -v") { $successCount++ }
        if (Run-Tests "advanced Redis tests" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_redis_advanced.py -v") { $successCount++ }
        if (Run-Tests "Neo4j-Redis integration" "docker exec tahimoto-backend python -m pytest tests/api/stories/components/test_neo4j_redis_integration.py -v") { $successCount++ }
    }
}

Write-Host "Test Summary: $successCount/$totalTests tests passed" -ForegroundColor Cyan
if ($TestType -eq "component") {
    Write-Host "Note: Some recommendation tests may show failures due to AsyncMock limitations. These failures are expected." -ForegroundColor DarkYellow
}
if ($TestType -eq "redis") {
    Write-Host "Note: The Redis integration tests verify that Redis caching conforms to the patterns described in the Optimization and Caching guidelines." -ForegroundColor Cyan
} 