"""
GraphQL resolvers for theme analysis operations.
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme, story
import neo4j
from app.services.mapping import get_theme_mapper
from app.services.theme_analysis import ThemeAnalysisService
from app.graphql.resolvers import query_resolvers, mutation_resolvers
from app.graphql.resolvers.utils.formatters import format_theme, format_theme_mapping
from app.graphql.resolvers.utils.error_handlers import handle_resolver_errors, log_resolver_call

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

# Initialize services
theme_analysis_service = ThemeAnalysisService()

@query_resolvers.field("analyzeMedia")
@handle_resolver_errors()
@log_resolver_call
async def resolve_analyze_media(_, info, sourceType: str, sourceId: str):
    """
    Resolver for analyzeMedia query - retrieves theme analysis for a media item.
    
    Args:
        sourceType: The type of media (Anime, Manga, etc.)
        sourceId: The ID of the media item
        
    Returns:
        Theme analysis result
    """
    logger.debug(f"Analyzing media: {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Get theme analysis for the source
    analysis = await theme.get_analysis(session, source_type=sourceType, source_id=sourceId)
    
    if not analysis:
        logger.warning(f"No analysis found for {sourceType}/{sourceId}")
        return {
            "sourceId": sourceId,
            "sourceType": sourceType,
            "themes": [],
            "stats": {
                "matchCount": 0,
                "confidenceAvg": 0,
                "topCategories": [],
                "dominantThemes": []
            }
        }
    
    # Extract themes from analysis
    themes = analysis.get("themes", [])
    
    # Calculate statistics
    match_count = len(themes)
    confidence_avg = sum([t.get("relationship", {}).get("mapping_strength", 0) for t in themes]) / max(match_count, 1)
    
    # Calculate category counts
    category_counts = {}
    for t in themes:
        theme_data = t.get("theme", {})
        category = theme_data.get("category", "UNCATEGORIZED")
        if category in category_counts:
            category_counts[category] += 1
        else:
            category_counts[category] = 1
    
    # Get top categories
    top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:3]
    top_category_names = [cat[0] for cat in top_categories]
    
    # Get dominant themes (themes with highest mapping strength)
    sorted_themes = sorted(themes, key=lambda x: x.get("relationship", {}).get("mapping_strength", 0), reverse=True)
    dominant_themes = [t.get("theme", {}).get("name") for t in sorted_themes[:5] if t.get("theme", {}).get("name")]
    
    # Format the result
    result = {
        "sourceId": sourceId,
        "sourceType": sourceType,
        "themes": [format_theme_mapping(t) for t in themes],
        "stats": {
            "matchCount": match_count,
            "confidenceAvg": confidence_avg,
            "topCategories": top_category_names,
            "dominantThemes": dominant_themes
        }
    }
    
    return result

@query_resolvers.field("analyzeStoryThemes")
@handle_resolver_errors()
@log_resolver_call
async def resolve_analyze_story_themes(_, info, sourceType: str, sourceId: str, genres: List[str], tags: List[Dict[str, Any]], description: Optional[str] = None):
    """
    Resolver for analyzeStoryThemes query - uses ThemeAnalysisService to analyze story themes.
    
    Args:
        sourceType: The type of media
        sourceId: The ID of the media
        genres: List of genres
        tags: List of tags with name and relevance
        description: Optional description text
        
    Returns:
        Theme analysis result
    """
    logger.debug(f"Analyzing themes for story: {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Call the analyze_story method
    analysis_result = await theme_analysis_service.analyze_story(
        source_type=sourceType,
        source_id=sourceId,
        genres=genres,
        tags=tags,
        description=description
    )
    
    # Transform the result to match the GraphQL schema
    themes = analysis_result["themes"]
    
    # Calculate statistics
    total_matches = len(themes)
    confidence_avg = analysis_result.get("confidence", 0.0)
    category_counts = {}
    for theme in themes:
        category = theme.get("category", "UNCATEGORIZED")
        if category in category_counts:
            category_counts[category] += 1
        else:
            category_counts[category] = 1
    
    # Get top 3 categories by count
    top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:3]
    top_category_names = [cat[0] for cat in top_categories]
    
    result = {
        "themes": themes,
        "stats": {
            "total_matches": total_matches,
            "confidence_avg": confidence_avg,
            "top_categories": top_category_names
        }
    }
    
    return result 