#!/usr/bin/env python3
"""
Database ID Consistency Checker

This script verifies that IDs in the Neo4j database follow the standardization rules.
It can also fix issues by updating database records to use standardized IDs.

Usage:
    python verify_database_ids.py [--fix] [--report=<filename>]

Options:
    --fix                Attempt to fix issues automatically
    --report=<filename>  Save report to file (default: db_id_report.json)
"""

import os
import json
import argparse
import sys
import asyncio
from typing import Dict, List, Any, Tuple
import csv
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import necessary modules
from app.services.id_service import IdService
from app.db.neo4j_session import get_session

async def check_themes() -> List[Dict[str, Any]]:
    """
    Check theme IDs in the database.
    
    Returns:
        List of issues found
    """
    issues = []
    
    async with await get_session() as session:
        query = """
        MATCH (t:Theme)
        RETURN t.id as id, t.name as name, labels(t) as labels
        """
        
        result = await session.run(query)
        records = await result.fetch()
        
        for record in records:
            theme_id = record["id"]
            theme_name = record["name"]
            
            # Check if ID is properly formatted
            standardized_id = IdService.standardize_id(theme_id, "theme")
            db_id = IdService.to_database_id(theme_id, "theme")
            
            # If the ID doesn't match our standardization rules
            if theme_id != db_id:
                issues.append({
                    "entity_type": "theme",
                    "id": theme_id,
                    "name": theme_name,
                    "standardized_id": standardized_id,
                    "database_id": db_id,
                    "issue": "ID should not have prefix in database",
                    "is_valid": False
                })
    
    return issues

async def check_stories() -> List[Dict[str, Any]]:
    """
    Check story IDs in the database.
    
    Returns:
        List of issues found
    """
    issues = []
    
    async with await get_session() as session:
        query = """
        MATCH (s:Story)
        RETURN s.id as id, s.title as name, labels(s) as labels
        """
        
        result = await session.run(query)
        records = await result.fetch()
        
        for record in records:
            story_id = record["id"]
            story_name = record["name"]
            
            # Check if ID is properly formatted
            standardized_id = IdService.standardize_id(story_id, "story")
            db_id = IdService.to_database_id(story_id, "story")
            
            # If the ID doesn't match our standardization rules
            if story_id != db_id:
                issues.append({
                    "entity_type": "story",
                    "id": story_id,
                    "name": story_name,
                    "standardized_id": standardized_id,
                    "database_id": db_id,
                    "issue": "ID should not have prefix in database",
                    "is_valid": False
                })
    
    return issues

async def check_mappings() -> List[Dict[str, Any]]:
    """
    Check mapping IDs in the database.
    
    Returns:
        List of issues found
    """
    issues = []
    
    async with await get_session() as session:
        query = """
        MATCH (t:Theme)<-[m:MAPS_TO]-(s:Story)
        RETURN m.id as id, t.name as theme_name, s.title as story_title
        """
        
        result = await session.run(query)
        records = await result.fetch()
        
        for record in records:
            mapping_id = record["id"]
            theme_name = record["theme_name"]
            story_title = record["story_title"]
            
            if not mapping_id:
                issues.append({
                    "entity_type": "mapping",
                    "id": "missing",
                    "theme_name": theme_name,
                    "story_title": story_title,
                    "issue": "Mapping is missing an ID",
                    "is_valid": False
                })
                continue
            
            # Check if ID is properly formatted
            standardized_id = IdService.standardize_id(mapping_id, "mapping")
            db_id = IdService.to_database_id(mapping_id, "mapping")
            
            # If the ID doesn't match our standardization rules
            if mapping_id != db_id:
                issues.append({
                    "entity_type": "mapping",
                    "id": mapping_id,
                    "theme_name": theme_name,
                    "story_title": story_title,
                    "standardized_id": standardized_id,
                    "database_id": db_id,
                    "issue": "ID should not have prefix in database",
                    "is_valid": False
                })
    
    return issues

async def check_relationships() -> List[Dict[str, Any]]:
    """
    Check relationship IDs in the database.
    
    Returns:
        List of issues found
    """
    issues = []
    
    async with await get_session() as session:
        query = """
        MATCH (source:Theme)-[r]->(target:Theme)
        RETURN source.id as source_id, source.name as source_name,
               target.id as target_id, target.name as target_name,
               type(r) as relationship_type, r.id as relationship_id
        """
        
        result = await session.run(query)
        records = await result.fetch()
        
        for record in records:
            source_id = record["source_id"]
            source_name = record["source_name"]
            target_id = record["target_id"]
            target_name = record["target_name"]
            relationship_type = record["relationship_type"]
            relationship_id = record.get("relationship_id")
            
            # Generate expected relationship ID
            source_std_id = IdService.standardize_id(source_id, "theme")
            target_std_id = IdService.standardize_id(target_id, "theme")
            expected_rel_id = IdService.create_relationship_id(source_std_id, relationship_type, target_std_id)
            
            # Check if relationship has an ID property
            if not relationship_id:
                issues.append({
                    "entity_type": "relationship",
                    "source_id": source_id,
                    "source_name": source_name,
                    "target_id": target_id,
                    "target_name": target_name,
                    "relationship_type": relationship_type,
                    "relationship_id": "missing",
                    "expected_id": expected_rel_id,
                    "issue": "Relationship is missing an ID",
                    "is_valid": False
                })
    
    return issues

async def fix_theme_ids(issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fix theme ID issues in the database.
    
    Args:
        issues: List of issues to fix
        
    Returns:
        List of fixing results
    """
    results = []
    
    async with await get_session() as session:
        for issue in issues:
            if issue["entity_type"] != "theme":
                continue
            
            # Get the corrected database ID format
            db_id = issue["database_id"]
            old_id = issue["id"]
            
            # Only fix if the ID is actually different
            if db_id != old_id:
                query = """
                MATCH (t:Theme {id: $old_id})
                SET t.id = $new_id
                RETURN t.id as id, t.name as name
                """
                
                try:
                    result = await session.run(query, {"old_id": old_id, "new_id": db_id})
                    record = await result.single()
                    
                    if record:
                        results.append({
                            "entity_type": "theme",
                            "old_id": old_id,
                            "new_id": db_id,
                            "name": record["name"],
                            "success": True
                        })
                    else:
                        results.append({
                            "entity_type": "theme",
                            "old_id": old_id,
                            "new_id": db_id,
                            "success": False,
                            "error": "Theme not found"
                        })
                except Exception as e:
                    results.append({
                        "entity_type": "theme",
                        "old_id": old_id,
                        "new_id": db_id,
                        "success": False,
                        "error": str(e)
                    })
    
    return results

async def fix_story_ids(issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fix story ID issues in the database.
    
    Args:
        issues: List of issues to fix
        
    Returns:
        List of fixing results
    """
    results = []
    
    async with await get_session() as session:
        for issue in issues:
            if issue["entity_type"] != "story":
                continue
            
            # Get the corrected database ID format
            db_id = issue["database_id"]
            old_id = issue["id"]
            
            # Only fix if the ID is actually different
            if db_id != old_id:
                query = """
                MATCH (s:Story {id: $old_id})
                SET s.id = $new_id
                RETURN s.id as id, s.title as name
                """
                
                try:
                    result = await session.run(query, {"old_id": old_id, "new_id": db_id})
                    record = await result.single()
                    
                    if record:
                        results.append({
                            "entity_type": "story",
                            "old_id": old_id,
                            "new_id": db_id,
                            "name": record["name"],
                            "success": True
                        })
                    else:
                        results.append({
                            "entity_type": "story",
                            "old_id": old_id,
                            "new_id": db_id,
                            "success": False,
                            "error": "Story not found"
                        })
                except Exception as e:
                    results.append({
                        "entity_type": "story",
                        "old_id": old_id,
                        "new_id": db_id,
                        "success": False,
                        "error": str(e)
                    })
    
    return results

async def fix_mapping_ids(issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fix mapping ID issues in the database.
    
    Args:
        issues: List of issues to fix
        
    Returns:
        List of fixing results
    """
    results = []
    
    async with await get_session() as session:
        for issue in issues:
            if issue["entity_type"] != "mapping":
                continue
            
            # Skip missing IDs - we'd need more information to create them
            if issue["id"] == "missing":
                continue
            
            # Get the corrected database ID format
            db_id = issue.get("database_id")
            old_id = issue["id"]
            
            # Only fix if the ID is actually different
            if db_id and db_id != old_id:
                query = """
                MATCH (t:Theme)<-[m:MAPS_TO {id: $old_id}]-(s:Story)
                SET m.id = $new_id
                RETURN m.id as id, t.name as theme_name, s.title as story_title
                """
                
                try:
                    result = await session.run(query, {"old_id": old_id, "new_id": db_id})
                    record = await result.single()
                    
                    if record:
                        results.append({
                            "entity_type": "mapping",
                            "old_id": old_id,
                            "new_id": db_id,
                            "theme_name": record["theme_name"],
                            "story_title": record["story_title"],
                            "success": True
                        })
                    else:
                        results.append({
                            "entity_type": "mapping",
                            "old_id": old_id,
                            "new_id": db_id,
                            "success": False,
                            "error": "Mapping not found"
                        })
                except Exception as e:
                    results.append({
                        "entity_type": "mapping",
                        "old_id": old_id,
                        "new_id": db_id,
                        "success": False,
                        "error": str(e)
                    })
    
    return results

async def fix_relationship_ids(issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fix relationship ID issues in the database.
    
    Args:
        issues: List of issues to fix
        
    Returns:
        List of fixing results
    """
    results = []
    
    async with await get_session() as session:
        for issue in issues:
            if issue["entity_type"] != "relationship":
                continue
            
            # Only fix missing IDs by adding the expected ID
            if issue["relationship_id"] == "missing":
                query = """
                MATCH (source:Theme {id: $source_id})-[r:$rel_type]->(target:Theme {id: $target_id})
                WHERE NOT EXISTS(r.id)
                SET r.id = $rel_id
                RETURN source.name as source_name, target.name as target_name
                """
                
                try:
                    result = await session.run(query, {
                        "source_id": issue["source_id"],
                        "target_id": issue["target_id"],
                        "rel_type": issue["relationship_type"],
                        "rel_id": issue["expected_id"]
                    })
                    
                    record = await result.single()
                    
                    if record:
                        results.append({
                            "entity_type": "relationship",
                            "source_name": record["source_name"],
                            "target_name": record["target_name"],
                            "relationship_type": issue["relationship_type"],
                            "new_id": issue["expected_id"],
                            "success": True
                        })
                    else:
                        results.append({
                            "entity_type": "relationship",
                            "source_id": issue["source_id"],
                            "target_id": issue["target_id"],
                            "relationship_type": issue["relationship_type"],
                            "new_id": issue["expected_id"],
                            "success": False,
                            "error": "Relationship not found"
                        })
                except Exception as e:
                    results.append({
                        "entity_type": "relationship",
                        "source_id": issue["source_id"],
                        "target_id": issue["target_id"],
                        "relationship_type": issue["relationship_type"],
                        "new_id": issue["expected_id"],
                        "success": False,
                        "error": str(e)
                    })
    
    return results

async def export_issues_to_csv(issues: List[Dict[str, Any]], filename: str):
    """
    Export issues to a CSV file for easier review.
    
    Args:
        issues: List of issues to export
        filename: CSV filename
    """
    # Determine all possible columns from the issues
    all_columns = set()
    for issue in issues:
        all_columns.update(issue.keys())
    
    columns = ["entity_type", "id", "name", "standardized_id", "database_id", "issue", "is_valid"]
    columns.extend([col for col in all_columns if col not in columns])
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=columns)
            writer.writeheader()
            
            for issue in issues:
                # Ensure all columns exist in the row
                row = {col: issue.get(col, "") for col in columns}
                writer.writerow(row)
    except Exception as e:
        print(f"Error exporting to CSV: {str(e)}")

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Database ID Consistency Checker')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix issues automatically')
    parser.add_argument('--report', type=str, default='db_id_report.json', help='Save report to file')
    
    args = parser.parse_args()
    
    print("Checking theme IDs...")
    theme_issues = await check_themes()
    
    print("Checking story IDs...")
    story_issues = await check_stories()
    
    print("Checking mapping IDs...")
    mapping_issues = await check_mappings()
    
    print("Checking relationship IDs...")
    relationship_issues = await check_relationships()
    
    # Combine all issues
    all_issues = theme_issues + story_issues + mapping_issues + relationship_issues
    
    # Display summary
    print("\nDatabase ID Issues Summary:")
    print("--------------------------")
    print(f"Themes: {len(theme_issues)} issues")
    print(f"Stories: {len(story_issues)} issues")
    print(f"Mappings: {len(mapping_issues)} issues")
    print(f"Relationships: {len(relationship_issues)} issues")
    print(f"Total: {len(all_issues)} issues")
    
    # Export issues to CSV for easier review
    csv_filename = args.report.replace('.json', '.csv')
    await export_issues_to_csv(all_issues, csv_filename)
    print(f"\nExported issues to {csv_filename}")
    
    # Fix issues if requested
    fix_results = []
    if args.fix and all_issues:
        print("\nFixing issues...")
        
        theme_fixes = await fix_theme_ids(theme_issues)
        story_fixes = await fix_story_ids(story_issues)
        mapping_fixes = await fix_mapping_ids(mapping_issues)
        relationship_fixes = await fix_relationship_ids(relationship_issues)
        
        fix_results = theme_fixes + story_fixes + mapping_fixes + relationship_fixes
        
        # Count successful fixes
        successful_fixes = sum(1 for result in fix_results if result.get("success", False))
        print(f"Fixed {successful_fixes} issues out of {len(all_issues)} total issues.")
    
    # Save report
    report = {
        "timestamp": datetime.now().isoformat(),
        "issues": {
            "themes": theme_issues,
            "stories": story_issues,
            "mappings": mapping_issues,
            "relationships": relationship_issues,
            "total": len(all_issues)
        },
        "fixes": fix_results if args.fix else []
    }
    
    with open(args.report, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to {args.report}")
    
    # Return error code if issues found
    return 1 if all_issues else 0

if __name__ == "__main__":
    asyncio.run(main()) 