"""
Test Pydantic model serialization for Redis caching.
This file tests the serialization and deserialization of Pydantic models for Redis caching.
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from app.core.redis import RedisConnection
from app.core.cache import <PERSON>acheManager
from app.schemas.story import Story, StorySearch, StorySearchResult
from app.schemas.theme import Theme, ThemeBase

# Sample data for testing
TEST_STORY_DATA = {
    "id": "story_42",
    "title_english": "Test Story",
    "title_romaji": "Tesuto Sutōrī",
    "external_id": "42",
    "media_type": "ANIME",
    "status": "FINISHED",
    "cover_image": "https://example.com/cover.jpg",
    "banner_image": "https://example.com/banner.jpg",
    "popularity": 12345,
    "average_score": 85,
    "description": "A test story description",
    "year": 2023,
    "season": "WINTER",
    "created_at": datetime.now(timezone.utc).isoformat(),
    "updated_at": datetime.now(timezone.utc).isoformat(),
    "story_metadata": {
        "genres": ["Action", "Adventure"],
        "tags": [{"name": "Magic", "category": "Setting"}],
        "studios": [],
        "relations": []
    }
}

TEST_SEARCH_RESULTS_DATA = {
    "items": [
        {
            "id": "story_42",
            "title_english": "Test Story 1",
            "title_romaji": "Tesuto Sutōrī 1",
            "media_type": "ANIME",
            "cover_image": "https://example.com/cover1.jpg",
            "year": 2023,
            "season": "WINTER",
            "external_id": "42",
            "genres": ["Action", "Adventure"]
        },
        {
            "id": "story_43",
            "title_english": "Test Story 2",
            "title_romaji": "Tesuto Sutōrī 2",
            "media_type": "ANIME",
            "cover_image": "https://example.com/cover2.jpg",
            "year": 2022,
            "season": "FALL",
            "external_id": "43",
            "genres": ["Comedy", "Slice of Life"]
        }
    ],
    "total": 2
}

@pytest.fixture
def redis_mock():
    """Mock Redis client."""
    redis_instance = AsyncMock()
    redis_instance.get.return_value = None  # Default to cache miss
    redis_instance.set.return_value = True  # Default to successful set
    return redis_instance

@pytest.mark.asyncio
class TestPydanticSerialization:
    """Test suite for Pydantic model serialization with Redis."""
    
    async def test_story_serialization(self, monkeypatch, redis_mock):
        """Test serialization and deserialization of Story model."""
        # Create a Story model from test data
        story = Story(**TEST_STORY_DATA)
        
        # Serialize the Story model to JSON
        story_json = story.model_dump_json()
        
        # Configure Redis mock to return the serialized story
        redis_mock.get.return_value = story_json
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", AsyncMock(return_value=True))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=story_json))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Create a CacheManager instance
        cache_manager = CacheManager()
        
        # Set the story in cache
        cache_key = "story:42"
        await cache_manager.set(cache_key, story_json)
        
        # Get the story from cache
        cached_result = await cache_manager.get(cache_key)
        
        # Deserialize the JSON back to a Story model
        deserialized_story = Story.model_validate_json(cached_result)
        
        # Verify deserialized story matches original story
        assert deserialized_story.id == story.id
        assert deserialized_story.title_english == story.title_english
        assert deserialized_story.title_romaji == story.title_romaji
        assert deserialized_story.media_type == story.media_type
        # Access genres through story_metadata
        assert deserialized_story.story_metadata["genres"] == story.story_metadata["genres"]
    
    async def test_search_results_serialization(self, monkeypatch, redis_mock):
        """Test serialization and deserialization of StorySearch model."""
        # Create search results from test data
        search_results = StorySearch(**TEST_SEARCH_RESULTS_DATA)
        
        # Serialize the search results to JSON
        search_results_json = search_results.model_dump_json()
        
        # Configure Redis mock to return the serialized search results
        redis_mock.get.return_value = search_results_json
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", AsyncMock(return_value=True))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=search_results_json))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Create a CacheManager instance
        cache_manager = CacheManager()
        
        # Set the search results in cache
        cache_key = "search:test:1:10"
        await cache_manager.set(cache_key, search_results_json)
        
        # Get the search results from cache
        cached_result = await cache_manager.get(cache_key)
        
        # Deserialize the JSON back to a StorySearch model
        deserialized_results = StorySearch.model_validate_json(cached_result)
        
        # Verify deserialized results match original results
        assert deserialized_results.total == search_results.total
        # Check items count instead of non-existent page/per_page fields
        assert len(deserialized_results.items) == len(search_results.items)
        assert deserialized_results.items[0].id == search_results.items[0].id
        assert deserialized_results.items[1].id == search_results.items[1].id
    
    async def test_error_handling_invalid_json(self, monkeypatch, redis_mock):
        """Test error handling when invalid JSON is stored in Redis."""
        # Configure Redis mock to return invalid JSON
        invalid_json = "{invalid_json"
        redis_mock.get.return_value = invalid_json
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=invalid_json))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Create a CacheManager instance
        cache_manager = CacheManager()
        
        # Get the invalid JSON from cache
        cache_key = "story:invalid"
        cached_result = await cache_manager.get(cache_key)
        
        # Try to deserialize, should raise an exception
        with pytest.raises(Exception):
            Story.model_validate_json(cached_result)
    
    async def test_error_handling_model_validation(self, monkeypatch, redis_mock):
        """Test error handling when JSON is valid but doesn't match model structure."""
        # Configure Redis mock to return JSON that doesn't match Story model
        invalid_model_json = json.dumps({"field_that_doesnt_exist": "value"})
        redis_mock.get.return_value = invalid_model_json
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=invalid_model_json))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Create a CacheManager instance
        cache_manager = CacheManager()
        
        # Get the invalid model from cache
        cache_key = "story:invalid_model"
        cached_result = await cache_manager.get(cache_key)
        
        # Try to deserialize, will either raise an exception or return a model with default values
        # depending on Pydantic validation settings
        try:
            story = Story.model_validate_json(cached_result)
            # If validation succeeds with defaults, id should be None or ""
            assert not story.id or story.id == ""
        except Exception:
            # If validation fails with an exception, that's also acceptable
            pass
    
    @pytest.mark.parametrize("model_class,test_data", [
        (Story, TEST_STORY_DATA),
        (StorySearch, TEST_SEARCH_RESULTS_DATA)
    ])
    async def test_model_roundtrip(self, model_class, test_data, monkeypatch, redis_mock):
        """Test full roundtrip for various model classes."""
        # Create model instance
        model_instance = model_class(**test_data)
        
        # Serialize to JSON
        model_json = model_instance.model_dump_json()
        
        # Configure Redis mock
        redis_mock.get.return_value = model_json
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", AsyncMock(return_value=True))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=model_json))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Create a CacheManager instance
        cache_manager = CacheManager()
        
        # Set in cache
        cache_key = f"test:{model_class.__name__}"
        await cache_manager.set(cache_key, model_json)
        
        # Get from cache
        cached_result = await cache_manager.get(cache_key)
        
        # Deserialize
        deserialized_model = model_class.model_validate_json(cached_result)
        
        # Validate core attributes are preserved
        if model_class == Story:
            assert deserialized_model.id == model_instance.id
            assert deserialized_model.title_english == model_instance.title_english
        elif model_class == StorySearch:
            assert deserialized_model.total == model_instance.total
            assert len(deserialized_model.items) == len(model_instance.items) 