import asyncio
from app.db.neo4j_session import get_db_session
from app.crud.neo4j import theme

async def list_all_themes():
    """Query all themes directly from the database using the application's connection."""
    try:
        async for session in get_db_session():
            try:
                print("Connected to database. Querying themes...")
                # Get all themes with a high limit
                themes = await theme.get_multi(session, limit=100)
                
                if not themes:
                    print("No themes found in the database!")
                else:
                    print(f"Found {len(themes)} themes:")
                    # Group by category for better readability
                    by_category = {}
                    for t in themes:
                        category = t.get("category", "UNCATEGORIZED")
                        if category not in by_category:
                            by_category[category] = []
                        by_category[category].append(t)
                    
                    # Print themes by category
                    for category, category_themes in by_category.items():
                        print(f"\n=== {category} ===")
                        for t in category_themes:
                            print(f"{t.get('name')} (ID: {t.get('id')})")
            finally:
                session.close()
    except Exception as e:
        print(f"Error querying themes: {str(e)}")

# Run the async function
if __name__ == "__main__":
    asyncio.run(list_all_themes()) 