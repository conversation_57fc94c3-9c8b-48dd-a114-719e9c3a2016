"""
Cache Management API Endpoints

This module provides REST API endpoints for managing and refreshing Redis caches in the Tahimoto system.
The endpoints enable manual control over cache refresh operations, invalidation of specific cache entries,
and retrieval of cache health statistics.

Key features:
- Manual refresh of stale theme caches (POST /refresh-themes)
- Targeted invalidation of specific cache entries (DELETE /invalidate/{cache_type}/{id})
- Cache health statistics and monitoring (GET /stats)

These endpoints are primarily intended for administrative use and troubleshooting, 
as the system also includes automated background tasks for regular cache maintenance
through the ScheduledTaskManager.

The cache management system works in conjunction with the ThemeRedisService to ensure
that cached data remains in sync with the Neo4j database for optimal performance
while maintaining data accuracy.
"""
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, status
from app.db.neo4j_session import get_db_session
from app.core.logging import get_logger
from app.services.theme_redis import theme_redis
from neo4j.exceptions import Neo4jError

logger = get_logger("api_cache")

router = APIRouter()

@router.post("/refresh-themes", status_code=status.HTTP_200_OK, response_model=Dict[str, Any])
async def refresh_stale_themes(
    max_themes: int = 50,
    session = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Refresh stale theme caches by checking with Neo4j for updates.
    
    This endpoint triggers a scan of cached theme entries, checking each against
    the staleness criteria (timestamp age and Neo4j comparison). Any themes found
    to be stale will be refreshed from the Neo4j database.
    
    Args:
        max_themes: Maximum number of themes to check for staleness (default: 50)
        
    Returns:
        Dictionary with statistics about the refresh operation, including:
        - checked: Total number of themes checked
        - refreshed: Number of themes refreshed from Neo4j
        - errors: Number of errors encountered during refresh
        - not_found: Number of themes that no longer exist in Neo4j
        
    Raises:
        HTTPException(500): If a database error or other unexpected error occurs
    """
    try:
        logger.info(f"API request to refresh stale theme caches (max: {max_themes})")
        stats = await theme_redis.refresh_stale_themes(session, max_themes)
        return {
            "message": "Stale theme cache refresh completed successfully",
            "stats": stats
        }
    except Neo4jError as e:
        logger.error(f"Neo4j error refreshing stale themes: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error refreshing stale themes: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )

@router.delete("/invalidate/{cache_type}/{id}", status_code=status.HTTP_200_OK, response_model=Dict[str, Any])
async def invalidate_cache(
    cache_type: str,
    id: str,
) -> Dict[str, Any]:
    """
    Invalidate a specific cache entry and its related entries.
    
    This endpoint forcibly removes a cache entry and any related entries from Redis.
    It's useful when data has been updated in Neo4j and you want to ensure that
    subsequent requests will fetch fresh data.
    
    Args:
        cache_type: Type of cache to invalidate ("theme", "story", "mapping")
        id: ID of the entity to invalidate cache for
        
    Returns:
        Dictionary with information about the invalidation operation:
        - message: Success message
        - invalidated_count: Number of cache entries that were invalidated
        
    Raises:
        HTTPException(500): If an error occurs during cache invalidation
    """
    try:
        logger.info(f"API request to invalidate cache for {cache_type}:{id}")
        invalidated = await theme_redis.invalidate_related_caches(cache_type, id)
        return {
            "message": f"Cache for {cache_type}:{id} invalidated successfully",
            "invalidated_count": invalidated
        }
    except Exception as e:
        logger.error(f"Error invalidating cache for {cache_type}:{id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error invalidating cache: {str(e)}"
        )

@router.get("/stats", status_code=status.HTTP_200_OK, response_model=Dict[str, Any])
async def get_cache_stats() -> Dict[str, Any]:
    """
    Get statistics about the Redis cache health and contents.
    
    This endpoint retrieves statistics about the Redis cache, including:
    - Total number of cached themes
    - Distribution of theme types
    - Most frequently accessed themes
    - General cache health status
    
    This information is useful for monitoring and troubleshooting the
    caching system's performance and contents.
    
    Returns:
        Dictionary with cache statistics:
        - theme_stats: Statistics about cached theme data
        - cache_health: Overall health status ("operational" or "error")
        - error: Error message, if cache_health is "error"
    """
    try:
        logger.info("API request for cache statistics")
        stats = await theme_redis.get_theme_stats()
        
        return {
            "theme_stats": stats,
            "cache_health": "operational"
        }
    except Exception as e:
        logger.error(f"Error getting cache statistics: {str(e)}")
        return {
            "theme_stats": {},
            "cache_health": "error",
            "error": str(e)
        } 