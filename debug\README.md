# Debug Scripts

This directory contains debug and troubleshooting scripts for the Tahimoto application.

## Scripts

### Cross-Media Mapping Debug
- **`debug_cross_media_mapper.py`** - Debug the CrossMediaAnimeMapper directly to test theme mapping functionality

### API Response Debug
- **`debug_response_format.py`** - Debug the exact response format from contextual themes API
- **`debug_theme_mapping.py`** - Debug script for theme mapping issues and available themes
- **`debug_themes.py`** - Simple theme lookup debug script

## Usage

All debug scripts are designed to be run from the project root directory:

```bash
# From project root
python debug/debug_cross_media_mapper.py
python debug/debug_response_format.py
python debug/debug_theme_mapping.py
python debug/debug_themes.py
```

## Prerequisites

- Backend server running on `http://localhost:8000`
- Database properly seeded with themes
- Python environment with required dependencies

## Notes

These scripts are for development and debugging purposes only. They should not be used in production environments.
