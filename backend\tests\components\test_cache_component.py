"""
Test cases for the cache component.
This file focuses on testing cache functionality in isolation.
"""
import pytest
import json
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock
from typing import Dict, Any

# Import cache manager
from app.core.cache import CacheManager
from app.core.redis import RedisConnection

@pytest.mark.asyncio
class TestCacheComponent:
    """Test suite for the cache component."""
    
    @pytest.fixture
    def redis_mock(self):
        """Mock Redis client."""
        redis_instance = AsyncMock()
        return redis_instance
    
    async def test_cache_get_hit(self, monkeypatch, redis_mock):
        """Test cache hit scenario."""
        # Configure mock to simulate a cache hit
        test_data = json.dumps({"id": "story_123", "title": "Test Story"})
        redis_mock.get.return_value = test_data
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=test_data))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager directly
        result = await CacheManager.get_json("test:key")
        
        # Verify result matches expected data
        assert result is not None
        assert result["id"] == "story_123"
        assert result["title"] == "Test Story"
    
    async def test_cache_miss(self, monkeypatch, redis_mock):
        """Test cache miss scenario."""
        # Configure mock to simulate a cache miss
        redis_mock.get.return_value = None
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=None))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager directly
        result = await CacheManager.get("test:key")
        
        # Verify result is None
        assert result is None
    
    async def test_cache_set(self, monkeypatch, redis_mock):
        """Test setting a value in cache."""
        # Data to cache
        test_data = {"id": "story_123", "title": "Test Story"}
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", AsyncMock(return_value=True))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager directly
        success = await CacheManager.set_json("test:key", test_data)
        
        # Verify result
        assert success is True
    
    async def test_cache_delete(self, monkeypatch, redis_mock):
        """Test deleting a value from cache."""
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "delete", AsyncMock(return_value=True))
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager directly
        success = await CacheManager.delete("test:key")
        
        # Verify result
        assert success is True
    
    async def test_cache_with_ttl(self, monkeypatch, redis_mock):
        """Test setting cache with TTL."""
        # Data to cache
        test_data = {"id": "story_123", "title": "Test Story"}
        
        # Patch Redis connection to track calls
        set_mock = AsyncMock(return_value=True)
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", set_mock)
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager with TTL
        await CacheManager.set_json("test:key", test_data, ttl=7200)
        
        # Verify TTL was passed
        set_mock.assert_called_once()
        args, kwargs = set_mock.call_args
        assert kwargs.get("ex") == 7200
    
    async def test_cache_json_serialization(self, monkeypatch, redis_mock):
        """Test JSON serialization of complex objects."""
        # Complex data structure
        complex_data = {
            "id": "story_123",
            "title": "Test Story",
            "genres": ["Action", "Adventure"],
            "scores": {
                "user": 8.5,
                "critic": 9.0
            },
            "episodes": [
                {"number": 1, "title": "Episode 1"},
                {"number": 2, "title": "Episode 2"}
            ],
            "airing": False,
            "rating": None
        }
        
        # Patch Redis connection for set
        set_mock = AsyncMock(return_value=True)
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        monkeypatch.setattr(RedisConnection, "set", set_mock)
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(return_value=True))
        
        # Test cache manager directly
        await CacheManager.set_json("complex:key", complex_data)
        
        # Capture the JSON string that was passed
        set_mock.assert_called_once()
        args, kwargs = set_mock.call_args
        key, value = args
        
        # For get, simulate retrieving the serialized data
        serialized_value = json.dumps(complex_data)
        monkeypatch.setattr(RedisConnection, "get_value", AsyncMock(return_value=serialized_value))
        
        # Test the round trip
        result = await CacheManager.get_json("complex:key")
        assert result == complex_data
    
    async def test_cache_error_handling(self, monkeypatch):
        """Test error handling in cache operations."""
        # Patch Redis connection to raise exception
        monkeypatch.setattr(RedisConnection, "ping", AsyncMock(side_effect=Exception("Redis connection error")))
        
        # Verify get returns None on error
        result = await CacheManager.get("test:key")
        assert result is None
        
        # Verify set handles error gracefully
        success = await CacheManager.set("test:key", "value")
        assert success is False 