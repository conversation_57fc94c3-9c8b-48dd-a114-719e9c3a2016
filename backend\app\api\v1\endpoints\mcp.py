"""
MCP API Endpoints

This module provides REST API endpoints for MCP (Model Context Protocol) integration.
These endpoints allow external systems to query theme data in MCP-compatible formats
and can serve as a bridge between the FastAPI backend and MCP clients.
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from app.schemas.theme import (
    MCPThemeQuery,
    MCPThemeResponse, 
    MCPThemeReference,
    MCPMediaAnalysisRequest,
    MCPMediaAnalysisResponse
)
from app.schemas.theme_category import (
    ThemeCategory,
    MediaType,
    get_category_metadata,
    get_categories_for_media_type
)
from app.db.neo4j_session import get_db_session

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/capabilities")
async def get_mcp_capabilities() -> Dict[str, Any]:
    """
    Get MCP server capabilities and metadata.
    
    Returns:
        Dictionary containing server capabilities, supported operations, and metadata
    """
    return {
        "server_info": {
            "name": "tahimoto-theme-server",
            "version": "1.0.0",
            "description": "MCP server for Tahimoto theme system"
        },
        "capabilities": {
            "resources": [
                {
                    "uri_template": "theme://categories",
                    "name": "Theme Categories",
                    "description": "Available theme categories and metadata"
                },
                {
                    "uri_template": "theme://media-types",
                    "name": "Media Types", 
                    "description": "Supported media types"
                },
                {
                    "uri_template": "theme://taxonomy",
                    "name": "Theme Taxonomy",
                    "description": "Complete theme taxonomy with relationships"
                }
            ],
            "tools": [
                {
                    "name": "search_themes",
                    "description": "Search for themes by various criteria"
                },
                {
                    "name": "analyze_media",
                    "description": "Analyze media content to detect themes"
                },
                {
                    "name": "get_theme_relationships", 
                    "description": "Get relationships for a specific theme"
                }
            ]
        },
        "supported_media_types": [mt.value for mt in MediaType],
        "supported_categories": [cat.value for cat in ThemeCategory]
    }

@router.get("/resources/categories")
async def get_theme_categories_resource() -> Dict[str, Any]:
    """
    Get theme categories resource in MCP format.
    
    Returns:
        Theme categories with metadata
    """
    try:
        return {
            "categories": [cat.value for cat in ThemeCategory],
            "metadata": get_category_metadata(),
            "media_type_mappings": {
                mt.value: [cat.value for cat in get_categories_for_media_type(mt)]
                for mt in MediaType
            }
        }
    except Exception as e:
        logger.error(f"Error getting theme categories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/resources/media-types")
async def get_media_types_resource() -> Dict[str, Any]:
    """
    Get media types resource in MCP format.
    
    Returns:
        Supported media types with descriptions
    """
    return {
        "media_types": [mt.value for mt in MediaType],
        "descriptions": {
            "ANIME": "Japanese animation content",
            "TV": "Television series and shows",
            "BOOK": "Literary works and novels", 
            "MUSIC": "Musical compositions and albums",
            "MOVIE": "Cinematic films",
            "GAME": "Video games and interactive media"
        }
    }

@router.post("/tools/search-themes", response_model=MCPThemeResponse)
async def search_themes_tool(
    query: MCPThemeQuery,
    session = Depends(get_db_session)
) -> MCPThemeResponse:
    """
    Search for themes using MCP tool interface.
    
    Args:
        query: Theme search query parameters
        session: Database session
        
    Returns:
        MCP-formatted theme search response
    """
    try:
        # For MVP, return mock data
        # In production, this would query the Neo4j database
        mock_themes = [
            MCPThemeReference(
                id="theme_001",
                name="Coming of Age",
                description="Character growth from youth to maturity",
                category=ThemeCategory.NARRATIVE_STRUCTURE,
                media_types=[MediaType.ANIME, MediaType.BOOK, MediaType.TV],
                llm_context="This theme involves character development, personal growth, and the transition from childhood innocence to adult understanding. Common in bildungsroman narratives.",
                related_themes=["theme_002", "theme_003"],
                confidence=0.95
            ),
            MCPThemeReference(
                id="theme_002", 
                name="School Life",
                description="Stories set in educational environments",
                category=ThemeCategory.SETTING_TYPE,
                media_types=[MediaType.ANIME, MediaType.TV],
                llm_context="School-based narratives often explore social dynamics, academic pressure, and adolescent relationships.",
                confidence=0.88
            )
        ]
        
        # Filter based on query parameters
        filtered_themes = mock_themes
        if query.media_type:
            filtered_themes = [t for t in filtered_themes if query.media_type in t.media_types]
        if query.category:
            filtered_themes = [t for t in filtered_themes if t.category == query.category]
        if query.search_term:
            search_lower = query.search_term.lower()
            filtered_themes = [
                t for t in filtered_themes 
                if search_lower in t.name.lower() or search_lower in t.description.lower()
            ]
        
        # Limit results
        filtered_themes = filtered_themes[:query.max_results]
        
        return MCPThemeResponse(
            themes=filtered_themes,
            total_count=len(filtered_themes),
            query_metadata={
                "media_type": query.media_type.value if query.media_type else None,
                "category": query.category.value if query.category else None,
                "search_term": query.search_term,
                "max_results": query.max_results
            }
        )
        
    except Exception as e:
        logger.error(f"Error searching themes: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tools/analyze-media", response_model=MCPMediaAnalysisResponse)
async def analyze_media_tool(
    request: MCPMediaAnalysisRequest,
    session = Depends(get_db_session)
) -> MCPMediaAnalysisResponse:
    """
    Analyze media content to detect themes using MCP tool interface.
    
    Args:
        request: Media analysis request
        session: Database session
        
    Returns:
        MCP-formatted media analysis response
    """
    try:
        # For MVP, return mock analysis based on simple heuristics
        detected_themes = []
        confidence_scores = {}
        
        # Simple keyword-based theme detection for MVP
        description_lower = (request.description or "").lower()
        title_lower = request.title.lower()
        genres_lower = [g.lower() for g in (request.genres or [])]
        tags_lower = [t.lower() for t in (request.tags or [])]
        
        # Check for common themes
        if any(word in description_lower or word in title_lower for word in ["school", "student", "academy"]):
            theme = MCPThemeReference(
                id="theme_school",
                name="School Life",
                description="Stories set in educational environments",
                category=ThemeCategory.SETTING_TYPE,
                media_types=[request.media_type],
                confidence=0.85
            )
            detected_themes.append(theme)
            confidence_scores["theme_school"] = 0.85
            
        if any(word in description_lower for word in ["grow", "development", "mature", "coming of age"]):
            theme = MCPThemeReference(
                id="theme_coming_of_age",
                name="Coming of Age", 
                description="Character growth from youth to maturity",
                category=ThemeCategory.NARRATIVE_STRUCTURE,
                media_types=[request.media_type],
                confidence=0.90
            )
            detected_themes.append(theme)
            confidence_scores["theme_coming_of_age"] = 0.90
            
        if any(genre in ["comedy", "slice of life"] for genre in genres_lower):
            theme = MCPThemeReference(
                id="theme_slice_of_life",
                name="Slice of Life",
                description="Everyday life and mundane experiences",
                category=ThemeCategory.NARRATIVE_STRUCTURE,
                media_types=[request.media_type],
                confidence=0.80
            )
            detected_themes.append(theme)
            confidence_scores["theme_slice_of_life"] = 0.80
        
        return MCPMediaAnalysisResponse(
            detected_themes=detected_themes,
            confidence_scores=confidence_scores,
            analysis_metadata={
                "media_type": request.media_type.value,
                "title": request.title,
                "analysis_method": "keyword_heuristic_mvp",
                "themes_detected": len(detected_themes)
            }
        )
        
    except Exception as e:
        logger.error(f"Error analyzing media: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tools/theme-relationships/{theme_id}")
async def get_theme_relationships_tool(
    theme_id: str,
    relationship_types: List[str] = Query(default=[]),
    session = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Get relationships for a specific theme using MCP tool interface.
    
    Args:
        theme_id: ID of the theme to get relationships for
        relationship_types: Optional filter for relationship types
        session: Database session
        
    Returns:
        Theme relationships data
    """
    try:
        # For MVP, return mock relationships
        mock_relationships = [
            {
                "target_theme_id": "theme_002",
                "target_theme_name": "Character Development",
                "relationship_type": "COMPLEMENTS",
                "strength": 0.8,
                "description": "Coming of age stories often involve character development"
            },
            {
                "target_theme_id": "theme_003", 
                "target_theme_name": "School Life",
                "relationship_type": "COEXISTS_WITH",
                "strength": 0.7,
                "description": "Many coming of age stories are set in school environments"
            }
        ]
        
        # Filter by relationship types if specified
        if relationship_types:
            mock_relationships = [
                r for r in mock_relationships 
                if r["relationship_type"] in relationship_types
            ]
        
        return {
            "theme_id": theme_id,
            "relationships": mock_relationships,
            "total_relationships": len(mock_relationships)
        }
        
    except Exception as e:
        logger.error(f"Error getting theme relationships: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def mcp_health_check() -> Dict[str, Any]:
    """
    Health check endpoint for MCP service.
    
    Returns:
        Health status and basic metrics
    """
    return {
        "status": "healthy",
        "service": "tahimoto-mcp-server",
        "version": "1.0.0",
        "capabilities": {
            "resources": 3,
            "tools": 3
        }
    }
