#!/usr/bin/env python3
"""
Test script for theme mapping API endpoints.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_create_anime_theme_mappings():
    """Test the create anime theme mappings endpoint that the frontend is calling."""
    print("🎯 Testing Create Anime Theme Mappings API")
    print("=" * 50)
    
    # Test data - The Apothecary Diaries
    anime_id = "21202"  # AniList ID for The Apothecary Diaries
    anime_metadata = {
        "title_romaji": "Kusuriya no Hitorigoto",
        "title_english": "The Apothecary Diaries",
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Female Protagonist", "rank": 95},
            {"name": "Historical", "rank": 90},
            {"name": "Palace", "rank": 85},
            {"name": "Medicine", "rank": 80}
        ],
        "synopsis": "<PERSON><PERSON><PERSON> lived a quiet life as an apothecary in the red-light district. However, she was kidnapped and sold as a servant to work in the rear palace."
    }
    
    try:
        print(f"1. Testing create mappings for anime ID: {anime_id}")
        response = requests.post(
            f"{BASE_URL}/theme-mapping/anime/{anime_id}/create-mappings",
            json=anime_metadata,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success!")
            print(f"   📊 Created {data['created_mappings']} mappings")
            print(f"   🎭 Mappings:")
            for mapping in data['mappings']:
                print(f"      • {mapping['theme_name']}: {mapping['mapping_strength']:.1%} ({mapping['mapping_type']})")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_get_anime_themes():
    """Test getting existing theme mappings for an anime."""
    print("\n2. Testing get anime themes...")
    anime_id = "21202"
    
    try:
        response = requests.get(
            f"{BASE_URL}/theme-mapping/anime/{anime_id}/themes",
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Found {len(data)} existing mappings")
            for mapping in data:
                print(f"      • {mapping['theme_name']}: {mapping['mapping_strength']:.1%}")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_mapping_stats():
    """Test mapping statistics endpoint."""
    print("\n3. Testing mapping statistics...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/theme-mapping/stats",
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Statistics retrieved")
            print(f"   📊 Total themes in DB: {data['total_themes_in_db']}")
            print(f"   📊 Total mappings: {data['total_mappings']}")
            print(f"   📊 Anime with mappings: {data['anime_with_mappings']}")
            print(f"   📊 Average mappings per anime: {data['average_mappings_per_anime']}")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_create_anime_theme_mappings()
    test_get_anime_themes()
    test_mapping_stats()
