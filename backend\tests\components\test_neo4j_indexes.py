"""
Tests for Neo4j index performance and functionality.

This module tests the creation and performance impact of Neo4j indexes
for theme and story queries.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from neo4j.exceptions import ClientError

from app.db.neo4j.indexes import INDEX_STATEMENTS, RELATIONSHIP_INDEXES


@pytest.mark.asyncio
class TestNeo4jIndexes:
    """Test Neo4j index creation and performance."""
    
    @patch("app.db.neo4j.indexes.AsyncGraphDatabase.driver")
    @patch("app.db.neo4j.indexes.settings")
    async def test_create_indexes(self, mock_settings, mock_driver_factory):
        """Test that indexes are created successfully."""
        # Import the function after patching to ensure patches are applied
        from app.db.neo4j.indexes import create_indexes
        
        # Configure settings
        mock_settings.NEO4J_URI = "bolt://test:7687"
        mock_settings.NEO4J_USER = "neo4j"
        mock_settings.NEO4J_PASSWORD = "password"
        
        # Prepare session mock that will be returned by the context manager
        mock_session = AsyncMock()
        
        # Set up driver side effects
        mock_driver = MagicMock()
        mock_driver_factory.return_value = mock_driver
        
        # Important: Make __aenter__ return itself, not a coroutine
        mock_driver.__aenter__ = AsyncMock(return_value=mock_driver)
        mock_driver.__aexit__ = AsyncMock(return_value=None)
        
        # Configure the session method to return a context manager
        mock_session_context = MagicMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=None)
        mock_driver.session.return_value = mock_session_context
        
        # Set up mock run method
        mock_session.run = AsyncMock()
        
        # Create mock records for the SHOW INDEXES query
        mock_records = []
        for i in range(5):
            record = MagicMock()
            record.get.return_value = f"index_{i}"
            mock_records.append(record)
        
        # Instead of using async generator, properly set up the result object
        # with the correct async iteration behavior
        async def run_side_effect(*args, **kwargs):
            result = AsyncMock()
            
            # For SHOW INDEXES query, return records
            if args and "SHOW INDEXES" in args[0]:
                # Set up proper async iteration for records
                result.__aiter__.return_value = mock_records
                return result
            
            # For other queries, return empty result
            result.__aiter__.return_value = []
            return result
        
        mock_session.run = AsyncMock(side_effect=run_side_effect)
        
        # Call the function
        result = await create_indexes()
        
        # Verify driver was created with correct parameters
        mock_driver_factory.assert_called_once_with(
            "bolt://test:7687", 
            auth=("neo4j", "password")
        )
        
        # Verify all index statements were run
        assert mock_session.run.call_count >= len(INDEX_STATEMENTS) + len(RELATIONSHIP_INDEXES)
        
        # Verify the result is the number of indexes processed
        assert result > 0

    @patch("app.db.neo4j.indexes.AsyncGraphDatabase.driver")
    @patch("app.db.neo4j.indexes.settings")
    async def test_drop_all_indexes(self, mock_settings, mock_driver_factory):
        """Test that all indexes can be dropped."""
        # Import the function after patching to ensure patches are applied
        from app.db.neo4j.indexes import drop_all_indexes
        
        # Configure settings
        mock_settings.NEO4J_URI = "bolt://test:7687"
        mock_settings.NEO4J_USER = "neo4j"
        mock_settings.NEO4J_PASSWORD = "password"
        
        # Prepare session mock that will be returned by the context manager
        mock_session = AsyncMock()
        
        # Set up driver side effects
        mock_driver = MagicMock()
        mock_driver_factory.return_value = mock_driver
        
        # Make __aenter__ return itself, not a coroutine
        mock_driver.__aenter__ = AsyncMock(return_value=mock_driver)
        mock_driver.__aexit__ = AsyncMock(return_value=None)
        
        # Configure the session method to return a context manager
        mock_session_context = MagicMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=None)
        mock_driver.session.return_value = mock_session_context
        
        # Create mock records for the SHOW INDEXES query
        mock_records = []
        for i in range(5):
            record = MagicMock()
            record.get.return_value = f"index_{i}"
            mock_records.append(record)
        
        # Set up run method with different responses for different queries
        async def run_side_effect(*args, **kwargs):
            result = AsyncMock()
            
            # For SHOW INDEXES query, return records
            if args and "SHOW INDEXES" in args[0]:
                result.__aiter__.return_value = mock_records
                return result
            
            # For DROP INDEX queries, return empty result
            result.__aiter__.return_value = []
            return result
        
        mock_session.run = AsyncMock(side_effect=run_side_effect)
        
        # Call the function
        result = await drop_all_indexes()
        
        # Verify the show indexes query was called
        mock_session.run.assert_any_call("SHOW INDEXES")
        
        # Verify each index was dropped
        assert mock_session.run.call_count >= len(mock_records) + 1  # +1 for SHOW INDEXES
        
        # Verify the result is the number of indexes dropped
        assert result == len(mock_records)

    @patch("app.db.neo4j.indexes.AsyncGraphDatabase.driver")
    @patch("app.db.neo4j.indexes.settings")
    async def test_index_error_handling(self, mock_settings, mock_driver_factory):
        """Test that errors during index creation are handled gracefully."""
        # Import the function after patching to ensure patches are applied
        from app.db.neo4j.indexes import create_indexes
        
        # Configure settings
        mock_settings.NEO4J_URI = "bolt://test:7687"
        mock_settings.NEO4J_USER = "neo4j"
        mock_settings.NEO4J_PASSWORD = "password"
        
        # Prepare session mock that will be returned by the context manager
        mock_session = AsyncMock()
        
        # Set up driver side effects
        mock_driver = MagicMock()
        mock_driver_factory.return_value = mock_driver
        
        # Make __aenter__ return itself, not a coroutine
        mock_driver.__aenter__ = AsyncMock(return_value=mock_driver)
        mock_driver.__aexit__ = AsyncMock(return_value=None)
        
        # Configure the session method to return a context manager
        mock_session_context = MagicMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=None)
        mock_driver.session.return_value = mock_session_context
        
        # Set up a side effect to raise an error on the 3rd call to run
        call_count = [0]  # Use a list for nonlocal access
        
        # Create mock records for the SHOW INDEXES query
        mock_records = []
        for i in range(5):
            record = MagicMock()
            record.get.return_value = f"index_{i}"
            mock_records.append(record)
        
        async def run_side_effect(*args, **kwargs):
            call_count[0] += 1
            
            if call_count[0] == 3:
                raise ClientError("Test error")
            
            result = AsyncMock()
            
            # For the SHOW INDEXES query, return records
            if args and "SHOW INDEXES" in args[0]:
                result.__aiter__.return_value = mock_records
                return result
            
            # For other queries, return empty result
            result.__aiter__.return_value = []
            return result
        
        mock_session.run = AsyncMock(side_effect=run_side_effect)
        
        # Call the function
        result = await create_indexes()
        
        # Verify an error was raised on the 3rd call
        assert call_count[0] > 3  # Should have continued past the error
        
        # Verify the function completed without raising an exception
        assert result > 0
    
    @pytest.mark.integration
    async def test_index_performance_improvement(self):
        """
        Test that indexes improve query performance.
        
        This test requires a real Neo4j connection and is marked as integration.
        Skip in unit test runs.
        """
        # Skip if not running integration tests
        pytest.skip("Integration test - requires Neo4j connection") 