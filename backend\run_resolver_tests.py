#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to run the GraphQL resolver tests.

This script sets up the environment and runs the GraphQL resolver tests
to validate the integration with Neo4j.

Usage:
    python run_resolver_tests.py
"""
import os
import sys
import logging
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up the environment for testing."""
    # Add the current directory to the path
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    
    # Set environment variables for testing
    os.environ["ENVIRONMENT"] = "test"
    os.environ["NEO4J_URI"] = os.environ.get("NEO4J_URI", "bolt://localhost:7687")
    os.environ["NEO4J_USER"] = os.environ.get("NEO4J_USER", "neo4j")
    os.environ["NEO4J_PASSWORD"] = os.environ.get("NEO4J_PASSWORD", "password")
    
    logger.info("Environment set up for testing")

async def run_tests():
    """Run the GraphQL resolver tests."""
    try:
        # Import the test module
        from tests.infrastructure.test_graphql_resolvers import run_tests
        
        # Run the tests
        logger.info("Running GraphQL resolver tests...")
        result = await run_tests()
        
        if result:
            logger.info("All tests passed!")
            return 0
        else:
            logger.error("Some tests failed")
            return 1
    except Exception as e:
        logger.exception(f"Error running tests: {e}")
        return 1

if __name__ == "__main__":
    # Set up the environment
    setup_environment()
    
    # Run the tests
    exit_code = asyncio.run(run_tests())
    
    # Exit with the appropriate code
    sys.exit(exit_code) 