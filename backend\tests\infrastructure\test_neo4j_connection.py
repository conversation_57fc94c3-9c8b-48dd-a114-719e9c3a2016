"""
Test script to verify Neo4j connection and GraphQL setup.
Run this script to test the connection to Neo4j and validate GraphQL queries.
"""
import asyncio
import logging
from app.db.neo4j_session import init_db, verify_connectivity, get_db_session, close_db_connection, driver
from app.core.logging import get_logger

logger = get_logger("neo4j_test")

async def test_neo4j_connection():
    """Test connection to Neo4j database."""
    logger.info("Testing Neo4j connection...")
    
    try:
        # Initialize database
        await init_db()
        logger.info("Neo4j schema initialized successfully")
        
        # Verify connectivity
        connected = await verify_connectivity()
        if not connected:
            logger.error("Failed to verify Neo4j connectivity")
            return False
        
        # Test basic operations
        async with driver.session() as session:
            # Count nodes
            result = await session.run("MATCH (n) RETURN count(n) as count")
            record = await result.single()
            count = record["count"]
            logger.info(f"Found {count} nodes in database")
            
            # Create a test node
            logger.info("Creating test node...")
            result = await session.run(
                "CREATE (n:TestNode {id: $id, name: $name}) RETURN n",
                {"id": "test-node", "name": "Test Node"}
            )
            record = await result.single()
            if record:
                logger.info("Test node created successfully")
            else:
                logger.error("Failed to create test node")
                return False
            
            # Read the test node
            logger.info("Reading test node...")
            result = await session.run(
                "MATCH (n:TestNode {id: $id}) RETURN n",
                {"id": "test-node"}
            )
            record = await result.single()
            if record:
                node = dict(record["n"])
                logger.info(f"Retrieved test node: {node['name']}")
            else:
                logger.error("Failed to retrieve test node")
                return False
            
            # Delete the test node
            logger.info("Deleting test node...")
            result = await session.run(
                "MATCH (n:TestNode {id: $id}) DELETE n",
                {"id": "test-node"}
            )
            
            # Verify deletion
            result = await session.run(
                "MATCH (n:TestNode {id: $id}) RETURN count(n) as count",
                {"id": "test-node"}
            )
            record = await result.single()
            count = record["count"]
            if count == 0:
                logger.info("Test node deleted successfully")
            else:
                logger.error("Failed to delete test node")
                return False
        
        logger.info("Neo4j connection test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error testing Neo4j connection: {str(e)}")
        return False

async def verify_connectivity():
    """Verify connectivity to Neo4j database."""
    logger.info("Verifying Neo4j connectivity...")
    
    try:
        # Get a session
        async with driver.session() as session:
            # Execute a simple query
            result = await session.run("MATCH (n) RETURN count(n) as count")
            record = await result.single()
            count = record["count"]
            logger.info(f"Connected to Neo4j database. Found {count} nodes.")
            return True
    except Exception as e:
        logger.error(f"Failed to connect to Neo4j: {str(e)}")
        return False

async def main():
    """Main entry point."""
    logging.basicConfig(level=logging.INFO)
    success = await test_neo4j_connection()
    if success:
        logger.info("Neo4j connection test completed successfully")
    else:
        logger.error("Neo4j connection test failed")

if __name__ == "__main__":
    asyncio.run(main()) 