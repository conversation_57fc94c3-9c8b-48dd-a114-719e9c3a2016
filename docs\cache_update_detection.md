# Cache Update Detection System

## Overview

The Cache Update Detection System is a crucial component of the Tahimoto backend that ensures data consistency between Redis caches and the Neo4j database. It intelligently identifies when cached data becomes stale and needs refreshing, optimizing performance while maintaining data accuracy.

## Key Components

### 1. ThemeRedisService

The `ThemeRedisService` handles the core cache detection logic:

- **Cache Timestamping**: Every cached theme includes a `cache_timestamp` field that records when it was last cached.
- **Staleness Detection**: The `check_if_stale` method determines if a cached theme needs updating.
- **Fallback Mechanism**: The `get_theme_with_fallback` method retrieves themes from cache when fresh, or from Neo4j when stale.
- **Bulk Refresh**: The `refresh_stale_themes` method scans and refreshes multiple stale themes at once.

### 2. Scheduled Tasks

The `ScheduledTaskManager` in `backend/app/core/scheduled_tasks.py` provides:

- **Periodic Refresh**: Automatic refreshing of stale themes every hour.
- **Task Management**: Controlled execution of background tasks.
- **Error Handling**: Robust error handling to prevent task failures from affecting the application.

### 3. API Endpoints

REST endpoints in `backend/app/api/v1/endpoints/cache.py` provide:

- **Manual Refresh**: `POST /api/v1/cache/refresh-themes` endpoint for manually triggering cache refresh.
- **Cache Invalidation**: `DELETE /api/v1/cache/invalidate/{cache_type}/{id}` for forcing cache invalidation.
- **Monitoring**: `GET /api/v1/cache/stats` for obtaining cache health statistics.

## How It Works

### Staleness Detection

```python
async def check_if_stale(self, session, theme_id: str, cached_theme: Dict) -> bool:
    """Check if a cached theme is stale and needs refreshing."""
    # No timestamp means it's stale
    if "cache_timestamp" not in cached_theme:
        return True
        
    # Parse the timestamp
    cache_time = datetime.fromisoformat(cached_theme["cache_timestamp"])
    now = datetime.utcnow()
    
    # If cache is recent (< 24h), consider it fresh
    if (now - cache_time).total_seconds() < self.max_stale_time:
        return False
        
    # Check Neo4j to see if the theme has been updated
    neo4j_theme = await self.theme_neo4j.get(session, id=theme_id)
    
    # If theme doesn't exist in Neo4j, it's stale
    if not neo4j_theme:
        return True
        
    # Check if Neo4j has a newer version
    if neo4j_theme.get("updated_at") != cached_theme.get("updated_at"):
        return True
        
    # No changes detected
    return False
```

The staleness check follows this logic:
1. If no timestamp exists, consider the cache stale.
2. If the cache is less than 24 hours old, consider it fresh.
3. If older than 24 hours, check with Neo4j for updates.
4. If the theme has been updated in Neo4j, consider it stale.

### Fallback Retrieval

```python
async def get_theme_with_fallback(self, session, theme_id: str) -> Optional[Dict]:
    """Get a theme from Redis cache or Neo4j if not in cache or stale."""
    # Try to get from cache first
    theme = await self.get_theme(theme_id)
    
    # If not in cache, get from Neo4j
    if not theme:
        return await self._fetch_from_neo4j(session, theme_id)
    
    # Check if the cached theme is stale
    is_stale = await self.check_if_stale(session, theme_id, theme)
    if is_stale:
        # Refresh from Neo4j
        fresh_theme = await self._fetch_from_neo4j(session, theme_id)
        if fresh_theme:
            # Update the cache with fresh data
            await self.set_theme(theme_id, fresh_theme)
            return fresh_theme
    
    # Return cached theme if not stale
    return theme
```

The fallback mechanism:
1. First tries the Redis cache
2. Checks if the cached data is stale
3. Refreshes from Neo4j if necessary
4. Updates the cache with fresh data

### Bulk Refresh Process

```python
async def refresh_stale_themes(self, session, max_themes: int = 50) -> Dict[str, int]:
    """Refresh stale theme caches by checking with Neo4j for updates."""
    stats = {
        "checked": 0,
        "refreshed": 0,
        "errors": 0,
        "not_found": 0
    }
    
    # Scan Redis for theme keys
    pattern = f"{self.theme_prefix}:*"
    redis = await RedisConnection.get()
    theme_keys = []
    
    # Scan for keys up to max_themes
    async for key in redis.scan_iter(match=pattern):
        if len(theme_keys) >= max_themes:
            break
        theme_keys.append(key.decode() if isinstance(key, bytes) else key)
    
    # Process each theme
    for key in theme_keys:
        stats["checked"] += 1
        theme_id = key.replace(f"{self.theme_prefix}:", "")
        
        try:
            # Get cached theme
            cached_theme = await self.get_theme(theme_id)
            if not cached_theme:
                continue
                
            # Check if stale
            is_stale = await self.check_if_stale(session, theme_id, cached_theme)
            if is_stale:
                # Refresh from Neo4j
                db_theme = await self.theme_neo4j.get(session, id=theme_id)
                
                if db_theme:
                    # Update cache
                    await self.set_theme(theme_id, db_theme)
                    stats["refreshed"] += 1
                else:
                    # Theme no longer exists in Neo4j
                    await self.redis.delete(key)
                    stats["not_found"] += 1
        except Exception as e:
            stats["errors"] += 1
    
    return stats
```

The bulk refresh process:
1. Scans Redis for theme keys up to a maximum count
2. Checks each theme for staleness
3. Refreshes stale themes from Neo4j
4. Updates statistics about the operation

## Scheduled Task Implementation

The `ScheduledTaskManager` runs the refresh task periodically:

```python
# Register the refresh task to run every hour
task_manager.register_task(
    name="refresh_stale_themes",
    interval_seconds=3600,  # 1 hour
    func=refresh_stale_themes_task,
    max_themes=100
)
```

## Benefits

- **Performance Optimization**: Minimizes Neo4j database load by using cached data when it's known to be fresh.
- **Data Consistency**: Ensures cached data stays in sync with the database.
- **Proactive Maintenance**: Scheduled tasks refresh caches without user intervention.
- **Configurable Thresholds**: Easy adjustment of staleness thresholds based on data volatility.
- **Graceful Degradation**: Fallback mechanisms ensure the system works even if Redis is unavailable.

## Testing

The cache update detection system has comprehensive test coverage in `tests/api/stories/components/test_theme_cache_staleness.py`:

- Tests for checking various staleness conditions
- Tests for the fallback retrieval process
- Tests for the bulk refresh functionality
- Tests for proper async Redis iteration

## Future Enhancements

- Add adaptive staleness thresholds based on data access patterns
- Implement priority-based refresh for frequently accessed items
- Add distributed locking to prevent duplicate refresh operations in cluster deployments
- Extend the system to cover other entity types beyond themes 