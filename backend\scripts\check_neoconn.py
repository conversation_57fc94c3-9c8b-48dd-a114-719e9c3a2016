import asyncio
from neo4j import AsyncGraphDatabase

async def check_neo4j():
    # These should match your Docker environment variables
    uri = "neo4j://neo4j:7687"
    username = "neo4j"
    password = "tahimoto123"
    
    try:
        print("Connecting to Neo4j...")
        driver = AsyncGraphDatabase.driver(uri, auth=(username, password))
        
        async with driver.session() as session:
            print("Connected. Running query...")
            result = await session.run("MATCH (t:Theme) RETURN t.id, t.name, t.category ORDER BY t.category, t.name LIMIT 100")
            data = await result.data()
            
            if not data:
                print("No themes found in database!")
            else:
                print(f"Found {len(data)} themes:")
                for theme in data:
                    print(f"{theme['t.name']} ({theme['t.category']})")
        
        await driver.close()
        print("Connection closed.")
    except Exception as e:
        print(f"Error connecting to Neo4j: {str(e)}")

# Run the async function
asyncio.run(check_neo4j()) 