# Core dependencies
fastapi==0.115.6
uvicorn==0.32.1
python-dotenv==1.0.1
pydantic==2.10.3
pydantic-settings==2.7.0
pydantic-core==2.27.1

# GraphQL with Ariadne
ariadne==0.21.0
graphql-core==3.2.5

# Neo4j 
neo4j==5.24.0
aiohappyeyeballs==2.4.4

# Redis
redis==5.2.1

# HTTP clients
aiohttp==3.11.10
httpx==0.28.1

# Utilities
python-multipart==0.0.19
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.2.1
email_validator==2.2.0
prometheus-client==0.20.0
prometheus-fastapi-instrumentator==7.0.0
sentry-sdk==2.22.0

# MCP (Model Context Protocol)
# Note: Using a lightweight JSON-RPC implementation for MCP-like functionality
# until official MCP Python package is available
jsonrpc-base==2.2.0
websockets==12.0

# Testing
pytest==8.3.4
pytest-asyncio==0.25.0
pytest-cov==6.0.0
pytest-env==1.1.5
pytest-mock==3.14.0
coverage==7.6.9