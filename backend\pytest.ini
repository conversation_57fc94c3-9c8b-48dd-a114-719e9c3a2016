[pytest]
asyncio_mode = auto
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --cov=app --cov-report=term-missing

# Markers for different test types
markers =
    unit: Mark test as a unit test (no external dependencies)
    component: Mark test as a component test (mocked dependencies)
    infrastructure: Mark test as an infrastructure test (real infrastructure with test data)
    integration: Mark test as requiring actual services working together
    container: Mark test as requiring a test container
    slow: Mark test as slow-running (excluded from default runs) 