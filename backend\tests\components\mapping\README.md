# Mapping Component Tests

This directory contains tests for the mapping components of the Tahimoto backend. These components are responsible for transforming media-specific metadata into universal themes that work across different media types.

## Components Tested

- **TagCombinationAnalyzer**: Tests the analyzer that identifies theme patterns from combinations of tags and genres
- **ThemeMapper implementations**: Tests for various media-specific theme mappers (anime, books, movies)
- **Factory functionality**: Tests for the theme mapper factory

## Testing Approach

The mapping components are tested with the following strategies:

1. **Input Normalization**: Tests that various input formats are properly normalized
2. **Pattern Matching**: Tests that combination rules are correctly applied
3. **Confidence Scoring**: Tests that confidence scores are appropriate
4. **Integration Testing**: Tests that mappers properly use the tag analyzer

## Running the Tests

To run just the mapping component tests:

```powershell
.\tests\scripts\run_tests.ps1 -TestType component -TestPattern "mapping"
```

Or run them as part of all component tests:

```powershell
.\tests\scripts\run_tests.ps1 -TestType component
``` 