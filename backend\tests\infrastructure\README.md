# Infrastructure Testing

This directory contains tests for the infrastructure layers of the Tahimoto backend, ensuring that fundamental systems are working correctly.

## Test Components

### 1. Neo4j Connection (`test_neo4j_connection.py`)

Tests the connection to the Neo4j database, including:
- Basic connectivity
- Session handling
- Transaction management
- Schema initialization

### 2. GraphQL Resolvers (`test_graphql_resolvers.py`)

Tests the GraphQL resolvers with the Neo4j backend, including:
- Introspection queries
- Theme queries and mutations
- Theme mapping functionality
- Error handling

### 3. GraphQL API (`test_graphql_api.py`)

Tests the GraphQL API using Ariadne, including:
- Theme queries
- Media analysis
- Response validation

### 4. AniList Integration (`test_anilist_integration.py`)

Tests the integration between the AniList service and Neo4j, including:
- Fetching anime data from AniList
- Storing data in Neo4j
- Search functionality
- Recommendation functionality

## Running the Tests

These tests are run as part of the Docker test suite:

```powershell
# Run all tests including infrastructure tests
.\backend\run_docker_tests.ps1
```

You can also run individual tests:

```powershell
# Run Neo4j connection test
docker exec tahimoto-backend python -m tests.infrastructure.test_neo4j_connection

# Run GraphQL resolver tests
docker exec tahimoto-backend python run_resolver_tests.py

# Run AniList integration tests
docker exec tahimoto-backend python -m tests.infrastructure.test_anilist_integration
```

## Test Dependencies

These tests depend on:

- **Neo4j**: For database operations
- **Ariadne**: For GraphQL schema and resolvers
- **AniList API**: For external data fetching

## Infrastructure vs. API Tests

The infrastructure tests focus on the foundational components of the system, while the API tests in `/tests/api/` focus on the functionality exposed through the REST API. This separation ensures that infrastructure issues can be identified separately from API implementation issues. 