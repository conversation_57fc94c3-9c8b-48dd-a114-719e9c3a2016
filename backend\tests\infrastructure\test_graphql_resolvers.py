"""
Test script to validate GraphQL resolvers with Neo4j backend.

This script tests various GraphQL queries and mutations to ensure
that the resolvers are correctly integrated with the Neo4j CRUD operations.

Usage:
    python -m tests.infrastructure.test_graphql_resolvers
"""
import asyncio
import json
import logging
import sys
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Import Neo4j session and driver
from app.db.neo4j_session import get_db_session, driver
from app.graphql.schema import schema
from ariadne import graphql

# Test queries and mutations
INTROSPECTION_QUERY = """
{
  __schema {
    types {
      name
      kind
    }
  }
}
"""

GET_THEME_QUERY = """
query GetTheme($id: ID!) {
  theme(id: $id) {
    id
    name
    description
    status
  }
}
"""

GET_THEMES_QUERY = """
query GetThemes {
  themes(limit: 5) {
    id
    name
    description
    status
  }
}
"""

THEME_STATS_QUERY = """
query GetThemeStats {
  themeStats {
    totalThemes
    mappedCount
    pendingCount
    needsReviewCount
    cacheHitRate
  }
}
"""

ANALYZE_MEDIA_QUERY = """
query AnalyzeMedia($sourceType: String!, $sourceId: String!) {
  analyzeMedia(sourceType: $sourceType, sourceId: $sourceId) {
    primaryThemes {
      theme {
        name
      }
      mappingStrength
      mappingType
    }
    secondaryThemes {
      theme {
        name
      }
      mappingStrength
    }
  }
}
"""

CREATE_THEME_MUTATION = """
mutation CreateTheme($input: ThemeInput!) {
  createTheme(input: $input) {
    id
    name
    description
    status
  }
}
"""

CREATE_THEME_MAPPING_MUTATION = """
mutation CreateThemeMapping($input: ThemeMappingInput!) {
  createThemeMapping(input: $input) {
    id
    sourceType
    sourceId
    themeId
    mappingStrength
    mappingType
  }
}
"""

async def execute_query(query: str, variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Execute a GraphQL query using the schema directly.
    
    Args:
        query: The GraphQL query string
        variables: Optional variables for the query
        
    Returns:
        The query result
    """
    # Get a Neo4j session
    session = None
    try:
        # Create a session directly from the driver
        session = driver.session()
        
        # Create a context with the session
        context = {"request": type("Request", (), {"state": type("State", (), {"db_session": session})})}
        
        # Execute the query using Ariadne's graphql (async version)
        success, result = await graphql(
            schema,
            {"query": query, "variables": variables or {}},
            context_value=context
        )
        
        # Check for errors
        if not success:
            logger.error(f"Query execution errors: {result.get('errors', [])}")
            return {"errors": [str(error.get('message', 'Unknown error')) for error in result.get('errors', [])]}
        
        return {"data": result.get("data", {})}
    except Exception as e:
        logger.error(f"Error executing GraphQL query: {str(e)}")
        return {"errors": [str(e)]}
    finally:
        if session:
            await session.close()

async def test_introspection():
    """Test the GraphQL schema via introspection."""
    logger.info("Testing GraphQL schema via introspection...")
    result = await execute_query(INTROSPECTION_QUERY)
    
    if "errors" in result:
        logger.error("Introspection query failed")
        return False
    
    types = result["data"]["__schema"]["types"]
    logger.info(f"Schema contains {len(types)} types")
    
    # Check for expected types
    expected_types = ["Theme", "ThemeMapping", "Query", "Mutation"]
    found_types = [t["name"] for t in types]
    
    for expected in expected_types:
        if expected in found_types:
            logger.info(f"Found expected type: {expected}")
        else:
            logger.error(f"Missing expected type: {expected}")
            return False
    
    return True

async def test_theme_queries():
    """Test theme-related queries."""
    logger.info("Testing theme queries...")
    
    # First create a test theme
    theme_input = {
        "name": "Test Theme",
        "description": "A theme created for testing",
        "confidence": 0.9
    }
    
    create_result = await execute_query(
        CREATE_THEME_MUTATION,
        {"input": theme_input}
    )
    
    if "errors" in create_result:
        logger.error(f"Failed to create test theme: {create_result['errors']}")
        return False
    
    # Check if createTheme data exists
    if not create_result.get("data") or not create_result["data"].get("createTheme"):
        logger.error("No createTheme data returned from mutation")
        logger.error(f"Full result: {create_result}")
        return False
    
    theme_id = create_result["data"]["createTheme"]["id"]
    logger.info(f"Created test theme with ID: {theme_id}")
    
    # Test get_theme query
    get_theme_result = await execute_query(
        GET_THEME_QUERY,
        {"id": theme_id}
    )
    
    if "errors" in get_theme_result:
        logger.error(f"Failed to get theme: {get_theme_result['errors']}")
        return False
    
    # Check if theme data exists
    if not get_theme_result.get("data") or not get_theme_result["data"].get("theme"):
        logger.error("No theme data returned from query")
        logger.error(f"Full result: {get_theme_result}")
        return False
    
    theme_data = get_theme_result["data"]["theme"]
    logger.info(f"Retrieved theme: {theme_data['name']}")
    
    # Test get_themes query
    get_themes_result = await execute_query(GET_THEMES_QUERY)
    
    if "errors" in get_themes_result:
        logger.error(f"Failed to get themes: {get_themes_result['errors']}")
        return False
    
    # Check if themes data exists
    if not get_themes_result.get("data") or not get_themes_result["data"].get("themes"):
        logger.error("No themes data returned from query")
        logger.error(f"Full result: {get_themes_result}")
        return False
    
    themes = get_themes_result["data"]["themes"]
    logger.info(f"Retrieved {len(themes)} themes")
    
    # Test theme stats query
    stats_result = await execute_query(THEME_STATS_QUERY)
    
    if "errors" in stats_result:
        logger.error(f"Failed to get theme stats: {stats_result['errors']}")
        return False
    
    # Check if themeStats data exists
    if not stats_result.get("data") or not stats_result["data"].get("themeStats"):
        logger.error("No themeStats data returned from query")
        logger.error(f"Full result: {stats_result}")
        return False
    
    stats = stats_result["data"]["themeStats"]
    logger.info(f"Theme stats: {stats['totalThemes']} total themes")
    
    return True

async def test_theme_mapping():
    """Test theme mapping functionality."""
    logger.info("Testing theme mapping...")
    
    # First create a test theme if it doesn't exist
    theme_input = {
        "name": "Adventure",
        "description": "Stories involving journeys, quests, or exploration",
        "confidence": 0.9
    }
    
    create_theme_result = await execute_query(
        CREATE_THEME_MUTATION,
        {"input": theme_input}
    )
    
    if "errors" in create_theme_result:
        logger.error(f"Failed to create test theme: {create_theme_result['errors']}")
        return False
    
    theme_id = create_theme_result["data"]["createTheme"]["id"]
    logger.info(f"Created/retrieved test theme with ID: {theme_id}")
    
    # Create a theme mapping
    mapping_input = {
        "sourceType": "Anime",
        "sourceId": "test_anime_1",
        "themeId": theme_id,
        "mappingStrength": 0.8,
        "mappingType": "PRIMARY",
        "context": "Test mapping for validation"
    }
    
    create_mapping_result = await execute_query(
        CREATE_THEME_MAPPING_MUTATION,
        {"input": mapping_input}
    )
    
    if "errors" in create_mapping_result:
        logger.error(f"Failed to create theme mapping: {create_mapping_result['errors']}")
        return False
    
    mapping_id = create_mapping_result["data"]["createThemeMapping"]["id"]
    logger.info(f"Created theme mapping with ID: {mapping_id}")
    
    # Test analyze media
    analyze_result = await execute_query(
        ANALYZE_MEDIA_QUERY,
        {"sourceType": "Anime", "sourceId": "test_anime_1"}
    )
    
    if "errors" in analyze_result:
        logger.error(f"Failed to analyze media: {analyze_result['errors']}")
        return False
    
    analysis = analyze_result["data"]["analyzeMedia"]
    primary_themes = analysis["primaryThemes"]
    logger.info(f"Media analysis found {len(primary_themes)} primary themes")
    
    # Check if our theme is in the results
    found_theme = False
    for theme_mapping in primary_themes:
        if theme_mapping["theme"]["name"] == "Adventure":
            found_theme = True
            logger.info(f"Found expected theme 'Adventure' with strength {theme_mapping['mappingStrength']}")
    
    if not found_theme:
        logger.warning("Did not find the expected 'Adventure' theme in the analysis results")
    
    return True

async def run_tests():
    """Run all tests."""
    logger.info("Starting GraphQL resolver tests...")
    
    # Test introspection
    if not await test_introspection():
        logger.error("Introspection test failed")
        return False
    
    # Test theme queries
    if not await test_theme_queries():
        logger.error("Theme queries test failed")
        return False
    
    # Test theme mapping
    if not await test_theme_mapping():
        logger.error("Theme mapping test failed")
        return False
    
    logger.info("All tests completed successfully!")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(run_tests())
        if not result:
            sys.exit(1)
    except Exception as e:
        logger.exception(f"Test execution failed: {e}")
        sys.exit(1) 