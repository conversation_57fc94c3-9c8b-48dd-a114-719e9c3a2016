{"$schema": "https://raw.githubusercontent.com/oxc-project/oxc/main/crates/oxc_linter/src/options/schema.json", "rules": {"typescript": "warn", "unicorn": "warn", "react": "off", "jsx-a11y": "off", "nextjs": "off"}, "env": {"browser": true, "es2022": true, "node": true}, "globals": {"describe": "readonly", "it": "readonly", "expect": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "vi": "readonly"}, "plugins": [], "settings": {}, "ignore_patterns": ["**/node_modules/**", "**/dist/**", "**/.vite/**", "**/coverage/**", "**/*.min.js"]}