"""
Test cases for the Neo4j query component.
This file focuses on testing Neo4j-specific query logic in isolation.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

# Import Neo4j query functions - adjust import paths as needed
from app.crud.neo4j.story import CRUDStory

class MockNode:
    """Mock class for Neo4j Node objects"""
    def __init__(self, data):
        self.data = data
        
    def __getitem__(self, key):
        # Support both string keys and numeric indices
        if isinstance(key, int):
            # For numeric indices, just return the data itself
            # This is consistent with how Neo4j Nodes work in py2neo
            return self.data
        return self.data[key]
        
    def items(self):
        return self.data.items()
        
    def get(self, key, default=None):
        return self.data.get(key, default)
        
    def __contains__(self, key):
        return key in self.data
    
    def __iter__(self):
        """Return an iterator over the keys of the data."""
        return iter(self.data)
    
    # The key fix: make the node behave like a sequence of key-value pairs
    # This is what the actual Neo4j Node objects do to support dict() conversion
    def keys(self):
        return self.data.keys()
        
    def values(self):
        return self.data.values()
    
    def __len__(self):
        return len(self.data)

class MockRecord:
    """Mock class for Neo4j Record objects"""
    def __init__(self, data_dict, node_key="n"):
        self.node = MockNode(data_dict)
        self.data = {node_key: self.node, 0: self.node}  # Support both string and numeric indices
        
    def __getitem__(self, key):
        return self.data[key]
        
    def keys(self):
        return [k for k in self.data.keys() if not isinstance(k, int)]

@pytest.mark.asyncio
class TestNeo4jQueries:
    """Test suite for Neo4j query components."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock session that returns controlled results."""
        mock = AsyncMock()
        # Configure the run method to return an AsyncResult
        mock_result = AsyncMock()
        mock.run.return_value = mock_result
        return mock
    
    @pytest.fixture
    def story_crud(self):
        """Return a CRUDStory instance for testing."""
        return CRUDStory()
    
    async def test_get_story_by_id(self, mock_db_session, story_crud):
        """Test retrieving a story by ID from Neo4j."""
        # Create a dictionary for the expected story
        expected_story = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "title_romaji": "Test Story",
            "title_native": "テストストーリー",
            "synopsis": "Test synopsis",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large.jpg",
            "cover_image_medium": "https://example.com/medium.jpg",
            "banner_image": "https://example.com/banner.jpg",
            "status": "FINISHED",
            "average_score": 85,
            "popularity": 1000,
            "source": "ORIGINAL",
            "updated_at": "2023-01-01T00:00:00"
        }
        
        # Create a mock Neo4j Record with the node
        mock_record = MockRecord(expected_story)
        
        # Configure the result to return the record
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = mock_record
        
        # Call the function
        result = await story_crud.get(mock_db_session, id="story_123")
        
        # Verify query execution
        mock_db_session.run.assert_called_once()
        
        # Verify the result directly instead of checking the Cypher query parameters
        assert result is not None
        assert result == expected_story
        assert result["id"] == "story_123"
        assert result["title_english"] == "Test Story"
        assert result["media_type"] == "ANIME"
        assert result["average_score"] == 85
    
    async def test_get_story_not_found(self, mock_db_session, story_crud):
        """Test retrieving a non-existent story."""
        # Configure mock to return no story
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = None
        
        # Call the function
        result = await story_crud.get(mock_db_session, id="story_nonexistent")
        
        # Verify query execution
        mock_db_session.run.assert_called_once()
        
        # Verify result is None
        assert result is None
    
    async def test_get_by_external_id(self, mock_db_session, story_crud):
        """Test retrieving a story by external ID."""
        # Create a dictionary for the expected story
        expected_story = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "media_type": "ANIME"
        }
        
        # Create a mock Neo4j Record with the node
        mock_record = MockRecord(expected_story, node_key="s")
        
        # Configure the result to return the record
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = mock_record
        
        # Call the function
        result = await story_crud.get_by_external_id(mock_db_session, external_id="123")
        
        # Verify query execution
        mock_db_session.run.assert_called_once()
        
        # Verify the result directly instead of checking Cypher query parameters
        assert result is not None
        assert result == expected_story
        assert result["id"] == "story_123"
        assert result["external_id"] == "123"
    
    async def test_create_story(self, mock_db_session, story_crud):
        """Test creating a story in Neo4j."""
        # Story data to create
        story_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "New Test Story",
            "title_romaji": "New Test Story",
            "title_native": "新しいテストストーリー",
            "synopsis": "A new test story",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large.jpg",
            "cover_image_medium": "https://example.com/medium.jpg",
            "banner_image": "https://example.com/banner.jpg",
            "status": "FINISHED",
            "average_score": 85,
            "popularity": 1000,
            "source": "ORIGINAL",
            "story_metadata": {
                "genres": ["Action", "Adventure"],
                "tags": ["Shounen", "Fighting"],
                "studios": ["Test Studio"],
                "relations": []
            }
        }

        # Create a mock Neo4j Record with the node
        mock_record = MockRecord(story_data, node_key="s")

        # Configure the result to return the record
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = mock_record

        # Call the function
        result = await story_crud.create(mock_db_session, obj_in=story_data)

        # Verify query execution
        mock_db_session.run.assert_called_once()

        # Verify result
        assert result is not None
        assert result == story_data
        assert result["id"] == "story_123"
        assert result["title_english"] == "New Test Story"
    
    async def test_update_story(self, mock_db_session, story_crud):
        """Test updating a story in Neo4j."""
        # Existing story in database
        db_story = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "title_romaji": "Test Story",
            "media_type": "ANIME",
            "average_score": 80
        }

        # Story data to update
        story_update = {
            "title_english": "Updated Test Story",
            "average_score": 90
        }

        # Expected story after update - add updated fields to a copy of db_story
        expected_story = db_story.copy()
        expected_story.update(story_update)

        # Create a mock Neo4j Record with the node
        mock_record = MockRecord(expected_story, node_key="s")

        # Configure the result to return the record
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = mock_record

        # Call the function
        result = await story_crud.update(mock_db_session, db_obj=db_story, obj_in=story_update)

        # Verify query execution
        mock_db_session.run.assert_called_once()

        # Verify result
        assert result is not None
        assert result == expected_story
        assert result["id"] == "story_123"
        assert result["title_english"] == "Updated Test Story"
        assert result["average_score"] == 90
    
    async def test_create_or_update_story(self, mock_db_session, story_crud):
        """Test create_or_update functionality."""
        # Story data
        story_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "media_type": "ANIME"
        }

        # Create a mock Neo4j Record with the node
        mock_record = MockRecord(story_data, node_key="s")

        # Configure the result to return the record
        mock_result = mock_db_session.run.return_value
        mock_result.single.return_value = mock_record

        # Call the function
        result = await story_crud.create_or_update(mock_db_session, obj_in=story_data)

        # Verify query execution
        mock_db_session.run.assert_called_once()

        # Verify Cypher query uses MERGE
        args, kwargs = mock_db_session.run.call_args
        assert "MERGE" in args[0]

        # Verify result
        assert result is not None
        assert result == story_data
        assert result["id"] == "story_123"
        assert result["title_english"] == "Test Story"
    
    async def test_get_multi(self, mock_db_session, story_crud):
        """Test retrieving multiple stories."""
        # Configure mock to return multiple stories
        mock_result = mock_db_session.run.return_value

        # Create a list of stories for testing
        stories = []
        for i in range(1, 4):
            stories.append({
                "id": f"story_{i}",
                "external_id": str(i),
                "title_english": f"Test Story {i}",
                "media_type": "ANIME"
            })

        # Create mock records
        mock_records = []
        for story in stories:
            mock_record = MockRecord(story)
            mock_records.append(mock_record)

        # Set up async iteration for the mock result
        mock_result.__aiter__.return_value = mock_records

        # Call the function
        result = await story_crud.get_multi(mock_db_session, skip=0, limit=10)

        # Verify query execution
        mock_db_session.run.assert_called_once()

        # Verify result
        assert len(result) == 3
        assert result[0]["id"] == "story_1"
        assert result[1]["id"] == "story_2"
        assert result[2]["id"] == "story_3"
    
    async def test_is_stale(self, story_crud):
        """Test stale data detection logic."""
        from datetime import datetime, timedelta
        import pytz

        # Patch the datetime.utcnow to return a fixed date for testing
        fixed_now = datetime(2023, 1, 10, 12, 0, 0, tzinfo=pytz.UTC)
        
        with patch('app.crud.neo4j.story.datetime') as mock_datetime:
            # Configure the mock to return our fixed date
            mock_datetime.utcnow.return_value = fixed_now
            mock_datetime.fromisoformat = datetime.fromisoformat  # Keep the real implementation
            
            # Create a story with old updated_at (8 days ago, definitely stale)
            old_date = (fixed_now - timedelta(days=8)).isoformat()
            stale_story = {
                "id": "story_123",
                "updated_at": old_date,
                "title_english": "Test Story"  # Add title to prevent staleness due to missing title
            }

            # Create a story with very recent updated_at (1 hour ago, definitely not stale)
            # CRUDStory.STALE_THRESHOLD_HOURS is 24 hours, so 1 hour is well within the threshold
            recent_date = (fixed_now - timedelta(hours=1)).isoformat()
            fresh_story = {
                "id": "story_123",
                "updated_at": recent_date,
                "title_english": "Test Story"  # Add title to prevent staleness due to missing title
            }

            # Create a story with no updated_at field (should be stale)
            no_date_story = {
                "id": "story_123"
            }

            # Test stale story
            assert story_crud.is_stale(stale_story) is True

            # Test fresh story 
            assert story_crud.is_stale(fresh_story) is False

            # Test story with missing updated_at
            assert story_crud.is_stale(no_date_story) is True 