from datetime import datetime
from typing import Optional, Dict, List
from sqlalchemy import String, DateTime, Integer, Float, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from app.db.base_class import Base
from enum import Enum as PyEnum
import uuid

class MediaType(PyEnum):
    TV = "TV"
    MOVIE = "MOVIE"
    OVA = "OVA"
    ONA = "ONA"
    SPECIAL = "SPECIAL"
    MANGA = "MANGA"
    NOVEL = "NOVEL"

    @classmethod
    def _missing_(cls, value: str) -> Optional["MediaType"]:
        """Handle missing values by trying to normalize them."""
        try:
            if value is None:
                return cls.TV  # Default to TV
                
            # Try to normalize the value by converting to uppercase
            normalized = str(value).upper().strip().replace(" ", "_")
            
            # First try direct mapping
            for member in cls:
                if member.value.upper() == normalized:
                    return member
                if member.name.upper() == normalized:
                    return member
                    
            # Then try mapping variants
            if any(t in normalized for t in ["MANGA", "<PERSON>NH<PERSON>", "MANHUA", "ONE_SHOT", "DOUJIN", "COMIC"]):
                return cls.MANGA
            if any(t in normalized for t in ["NOVEL", "LIGHT_NOVEL", "LIGHTNOVEL", "LN"]):
                return cls.NOVEL
            if any(t in normalized for t in ["MOVIE", "FILM", "THEATRICAL", "MOVIES"]):
                return cls.MOVIE
            if any(t in normalized for t in ["SPECIAL", "EXTRA", "EXTRAS"]):
                return cls.SPECIAL
            if any(t in normalized for t in ["TV", "TELEVISION", "SERIES", "TV_SERIES", "TV_SHOW"]):
                return cls.TV
            if any(t in normalized for t in ["OVA", "ORIGINAL_VIDEO_ANIMATION"]):
                return cls.OVA
            if any(t in normalized for t in ["ONA", "ORIGINAL_NET_ANIMATION", "WEB"]):
                return cls.ONA
                
            # If unknown, default to TV for safety
            return cls.TV
        except Exception:
            return cls.TV  # Default to TV on any error

    def __str__(self) -> str:
        """String representation of the enum value."""
        return self.value

class MediaStatus(PyEnum):
    FINISHED = "FINISHED"
    RELEASING = "RELEASING"
    NOT_YET_RELEASED = "NOT_YET_RELEASED"
    CANCELLED = "CANCELLED"
    HIATUS = "HIATUS"

    @classmethod
    def _missing_(cls, value: str) -> Optional["MediaStatus"]:
        """Handle missing values by trying to normalize them."""
        try:
            if value is None:
                return cls.RELEASING  # Default to RELEASING
                
            # Try to normalize the value by converting to uppercase
            normalized = str(value).upper().strip().replace(" ", "_")
            
            # First try direct mapping
            for member in cls:
                if member.value.upper() == normalized:
                    return member
                if member.name.upper() == normalized:
                    return member
                    
            # Then try mapping variants
            if any(t in normalized for t in ["FINISHED", "COMPLETED", "DONE", "ENDED"]):
                return cls.FINISHED
            if any(t in normalized for t in ["RELEASING", "ONGOING", "AIRING", "CURRENT"]):
                return cls.RELEASING
            if any(t in normalized for t in ["NOT_YET_RELEASED", "UPCOMING", "UNRELEASED", "ANNOUNCED"]):
                return cls.NOT_YET_RELEASED
            if any(t in normalized for t in ["CANCELLED", "CANCELED", "DISCONTINUED"]):
                return cls.CANCELLED
            if any(t in normalized for t in ["HIATUS", "ON_HOLD", "PAUSED"]):
                return cls.HIATUS
                
            # If unknown, default to RELEASING for safety
            return cls.RELEASING
        except Exception:
            return cls.RELEASING  # Default to RELEASING on any error

    def __str__(self) -> str:
        """String representation of the enum value."""
        return self.value

class MediaSeason(PyEnum):
    WINTER = "WINTER"
    SPRING = "SPRING"
    SUMMER = "SUMMER"
    FALL = "FALL"

    def __str__(self) -> str:
        """String representation of the enum value."""
        return self.value

class Story(Base):
    """
    Story model for anime/manga/etc.
    Represents a media item with its metadata and relationships.
    """
    
    __tablename__ = "stories"
    
    id: Mapped[str] = mapped_column(
        String(100), 
        primary_key=True,
        default=lambda: f"story_{uuid.uuid4().hex}"
    )
    
    external_id: Mapped[str] = mapped_column(
        String(100), 
        unique=True, 
        index=True,
        nullable=False
    )
    
    # Basic Info
    title_english: Mapped[Optional[str]] = mapped_column(String(255))
    title_romaji: Mapped[str] = mapped_column(String(255))
    title_native: Mapped[Optional[str]] = mapped_column(String(255))
    synopsis: Mapped[Optional[str]] = mapped_column(String)
    media_type: Mapped[MediaType] = mapped_column(
        String(50),  # Store as string instead of enum
        nullable=False
    )
    source: Mapped[str] = mapped_column(String(50))
    
    # Images
    cover_image_large: Mapped[Optional[str]] = mapped_column(String(500))
    cover_image_medium: Mapped[Optional[str]] = mapped_column(String(500))
    banner_image: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Stats
    status: Mapped[Optional[MediaStatus]] = mapped_column(
        String(50),  # Store as string instead of enum
        nullable=True
    )
    popularity: Mapped[Optional[int]] = mapped_column(Integer)
    average_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Content Details
    episode_count: Mapped[Optional[int]] = mapped_column(Integer)
    episode_duration: Mapped[Optional[int]] = mapped_column(Integer)
    start_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    season: Mapped[Optional[MediaSeason]] = mapped_column(
        String(50),  # Store as string instead of enum
        nullable=True
    )
    
    # Extended Metadata
    story_metadata: Mapped[Dict] = mapped_column(
        JSONB,
        default={
            "genres": [],
            "tags": [],
            "studios": [],
            "relations": [],
            "recommendations": []
        },
        nullable=False
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )

    def __repr__(self) -> str:
        """String representation of the story."""
        return f"<Story {self.title_english or self.title_romaji}>"
    
    def get_genres(self) -> List[str]:
        """Get the list of genres for this story."""
        return self.story_metadata.get("genres", [])
    
    def get_tags(self) -> List[Dict]:
        """Get the list of tags with their metadata."""
        return self.story_metadata.get("tags", [])
    
    def get_relations(self) -> List[Dict]:
        """Get the list of related stories."""
        return self.story_metadata.get("relations", [])
    
    def get_recommendations(self) -> List[Dict]:
        """Get the list of recommended stories."""
        return self.story_metadata.get("recommendations", [])