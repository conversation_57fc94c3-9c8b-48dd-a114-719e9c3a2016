"""
ID standardization utilities for GraphQL resolvers.

This module provides utilities for ID format standardization, validation,
generation, and parsing across different entity types.
"""
import re
import uuid
from typing import Dict, Any, Tuple, Optional, NamedTuple, Union

# Common ID prefixes
THEME_PREFIX = "theme_"
STORY_PREFIX = "story_"
MAPPING_PREFIX = "mapping_"
RELATIONSHIP_PREFIX = "rel_"

class RelationshipIdComponents(NamedTuple):
    """Components of a parsed relationship ID."""
    source_id: str  # With prefix
    relationship_type: str
    target_id: str  # With prefix

def ensure_theme_id_prefix(id_value: str) -> str:
    """
    Ensure a theme ID has the correct theme_ prefix.
    
    Args:
        id_value: The ID to standardize
        
    Returns:
        Standardized ID with theme_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(THEME_PREFIX):
        return id_value
    return f"{THEME_PREFIX}{id_value}"

def ensure_story_id_prefix(id_value: str) -> str:
    """
    Ensure a story ID has the correct story_ prefix.
    
    Args:
        id_value: The ID to standardize
        
    Returns:
        Standardized ID with story_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(STORY_PREFIX):
        return id_value
    return f"{STORY_PREFIX}{id_value}"

def ensure_mapping_id_prefix(id_value: str) -> str:
    """
    Ensure a mapping ID has the correct mapping_ prefix.
    
    Args:
        id_value: The ID to standardize
        
    Returns:
        Standardized ID with mapping_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(MAPPING_PREFIX):
        return id_value
    return f"{MAPPING_PREFIX}{id_value}"

def remove_theme_id_prefix(id_value: str) -> str:
    """
    Remove the theme_ prefix from a theme ID.
    
    Args:
        id_value: The ID to clean
        
    Returns:
        ID without the theme_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(THEME_PREFIX):
        return id_value[len(THEME_PREFIX):]
    return id_value

def remove_story_id_prefix(id_value: str) -> str:
    """
    Remove the story_ prefix from a story ID.
    
    Args:
        id_value: The ID to clean
        
    Returns:
        ID without the story_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(STORY_PREFIX):
        return id_value[len(STORY_PREFIX):]
    return id_value

def remove_mapping_id_prefix(id_value: str) -> str:
    """
    Remove the mapping_ prefix from a mapping ID.
    
    Args:
        id_value: The ID to clean
        
    Returns:
        ID without the mapping_ prefix
    """
    if not id_value:
        return id_value
        
    if id_value.startswith(MAPPING_PREFIX):
        return id_value[len(MAPPING_PREFIX):]
    return id_value

def parse_mapping_id(id_value: str) -> Tuple[str, str]:
    """
    Parse a mapping ID into source ID and theme ID components.
    
    Args:
        id_value: The mapping ID to parse
        
    Returns:
        Tuple of (source_id, theme_id)
        
    Raises:
        ValueError: If the ID format is invalid
    """
    if "_" not in id_value:
        raise ValueError(f"Invalid mapping ID format: {id_value}")
        
    parts = id_value.split("_", 1)
    if len(parts) != 2:
        raise ValueError(f"Invalid mapping ID format: {id_value}")
        
    return parts[0], parts[1]

def parse_relationship_id(id_value: str) -> RelationshipIdComponents:
    """
    Parse a relationship ID into its components.
    
    Args:
        id_value: The relationship ID to parse (format: rel_source_id_REL_TYPE_target_id)
        
    Returns:
        RelationshipIdComponents with parsed components
        
    Raises:
        ValueError: If the ID format is invalid
    """
    if not id_value.startswith(RELATIONSHIP_PREFIX):
        raise ValueError(f"Invalid relationship ID format (missing 'rel_' prefix): {id_value}")
    
    # Format is rel_source_id_RELATIONSHIP_TYPE_target_id
    try:
        # Remove the 'rel_' prefix and split into source_id and the rest
        parts = id_value[len(RELATIONSHIP_PREFIX):].split("_", 1)
        if len(parts) < 2:
            raise ValueError(f"Invalid relationship ID format: {id_value}")
            
        source_id = parts[0]
        
        # Find where the relationship type ends (relationship types are UPPERCASE)
        remaining = parts[1]
        rel_type_end = -1
        
        # Start with the first underscore after the first part
        # Relationship types are all uppercase
        uppercase_pattern = re.compile(r'^[A-Z_]+')
        match = uppercase_pattern.match(remaining)
        
        if not match:
            raise ValueError(f"Invalid relationship ID format (no valid relationship type): {id_value}")
            
        relationship_type = match.group(0)
        
        # Get target ID (everything after the relationship type and an underscore)
        if len(remaining) <= len(relationship_type):
            raise ValueError(f"Invalid relationship ID format (missing target ID): {id_value}")
            
        target_id = remaining[len(relationship_type) + 1:]
        
        # Add prefixes if needed
        source_id = ensure_theme_id_prefix(source_id)
        target_id = ensure_theme_id_prefix(target_id)
        
        return RelationshipIdComponents(source_id, relationship_type, target_id)
    except Exception as e:
        raise ValueError(f"Invalid relationship ID format: {id_value}. Error: {str(e)}")

def create_relationship_id(source_id: str, relationship_type: str, target_id: str) -> str:
    """
    Create a standardized relationship ID.
    
    Args:
        source_id: The source theme ID
        relationship_type: The relationship type
        target_id: The target theme ID
        
    Returns:
        Standardized relationship ID
    """
    # Ensure IDs have correct prefixes
    source_id = ensure_theme_id_prefix(source_id)
    target_id = ensure_theme_id_prefix(target_id)
    
    # Create the relationship ID
    return f"{RELATIONSHIP_PREFIX}{source_id}_{relationship_type}_{target_id}"

def generate_theme_id() -> str:
    """
    Generate a new theme ID.
    
    Returns:
        New theme ID with prefix
    """
    return f"{THEME_PREFIX}{uuid.uuid4()}"

def generate_mapping_id() -> str:
    """
    Generate a new mapping ID.
    
    Returns:
        New mapping ID with prefix
    """
    return f"{MAPPING_PREFIX}{uuid.uuid4()}"

def is_valid_theme_id(id_value: str) -> bool:
    """
    Check if a theme ID is valid.
    
    Args:
        id_value: The ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not id_value:
        return False
        
    if not id_value.startswith(THEME_PREFIX):
        return False
        
    # Additional validation could be added here
    return True

def is_valid_story_id(id_value: str) -> bool:
    """
    Check if a story ID is valid.
    
    Args:
        id_value: The ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not id_value:
        return False
        
    if not id_value.startswith(STORY_PREFIX):
        return False
        
    # Additional validation could be added here
    return True

def is_valid_mapping_id(id_value: str) -> bool:
    """
    Check if a mapping ID is valid.
    
    Args:
        id_value: The ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        parse_mapping_id(id_value)
        return True
    except ValueError:
        return False

def is_valid_relationship_id(id_value: str) -> bool:
    """
    Check if a relationship ID is valid.
    
    Args:
        id_value: The ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        parse_relationship_id(id_value)
        return True
    except ValueError:
        return False 