<template>
  <div>
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-900">Theme-Story Connector</h2>
      <p class="mt-1 text-sm text-gray-600">
        Connect themes to stories and analyze relationships between different media.
      </p>
    </div>

    <!-- Theme Relationship Explorer -->
    <div class="card mb-8">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Explore Theme Relationships</h3>
      </div>
      <div class="card-body">
        <form @submit.prevent="handleGetRelationships" class="space-y-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label for="theme-id" class="block text-sm font-medium text-gray-700">
                Theme ID
              </label>
              <input
                id="theme-id"
                v-model="selectedThemeId"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter theme ID..."
              />
            </div>
            <div class="flex items-end">
              <button
                type="submit"
                :disabled="themeStore.loading"
                class="btn-primary"
              >
                <span v-if="themeStore.loading">Loading...</span>
                <span v-else>Get Relationships</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Quick Theme Search for ID -->
    <div class="card mb-8">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Find Theme ID</h3>
      </div>
      <div class="card-body">
        <form @submit.prevent="handleQuickSearch" class="space-y-4">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div class="sm:col-span-2">
              <label for="quick-search" class="block text-sm font-medium text-gray-700">
                Search for Theme
              </label>
              <input
                id="quick-search"
                v-model="quickSearchQuery"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Enter theme name or keywords..."
              />
            </div>
            <div class="flex items-end">
              <button
                type="submit"
                :disabled="themeStore.loading"
                class="btn-secondary w-full"
              >
                <span v-if="themeStore.loading">Searching...</span>
                <span v-else>Quick Search</span>
              </button>
            </div>
          </div>
        </form>

        <!-- Quick Search Results -->
        <div v-if="quickSearchResults.length > 0" class="mt-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">Select a theme:</h4>
          <div class="space-y-2">
            <button
              v-for="theme in quickSearchResults"
              :key="theme.id"
              @click="selectTheme(theme.id)"
              class="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <div class="font-medium text-gray-900">{{ theme.name }}</div>
              <div class="text-sm text-gray-600">{{ theme.description }}</div>
              <div class="text-xs text-gray-500 mt-1">ID: {{ theme.id }}</div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="themeStore.hasError" class="mb-6">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
              {{ themeStore.error }}
            </div>
            <div class="mt-4">
              <button
                @click="themeStore.clearError"
                class="text-sm bg-red-100 text-red-800 hover:bg-red-200 px-3 py-1 rounded-md"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Relationship Results -->
    <div v-if="relationshipResults" class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Theme Relationships</h3>
      </div>
      <div class="card-body">
        <div class="mb-4">
          <h4 class="font-medium text-gray-900">Theme: {{ relationshipResults.theme_name || selectedThemeId }}</h4>
          <p class="text-sm text-gray-600 mt-1">{{ relationshipResults.description || 'No description available' }}</p>
        </div>

        <div v-if="relationshipResults.related_themes && relationshipResults.related_themes.length > 0" class="mb-6">
          <h4 class="font-medium text-gray-900 mb-3">Related Themes:</h4>
          <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div
              v-for="relatedTheme in relationshipResults.related_themes"
              :key="relatedTheme.id"
              class="border border-gray-200 rounded-lg p-3"
            >
              <h5 class="font-medium text-gray-900">{{ relatedTheme.name }}</h5>
              <p class="text-sm text-gray-600 mt-1">{{ relatedTheme.description }}</p>
              <div class="mt-2">
                <span class="text-xs text-gray-500">Relationship: {{ relatedTheme.relationship_type || 'Related' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="relationshipResults.media_examples && relationshipResults.media_examples.length > 0">
          <h4 class="font-medium text-gray-900 mb-3">Media Examples:</h4>
          <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
            <div
              v-for="example in relationshipResults.media_examples"
              :key="example.id"
              class="border border-gray-200 rounded-lg p-3"
            >
              <h5 class="font-medium text-gray-900">{{ example.title }}</h5>
              <p class="text-sm text-gray-600 mt-1">{{ example.description }}</p>
              <div class="mt-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ example.media_type }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-if="!relationshipResults.related_themes?.length && !relationshipResults.media_examples?.length" class="text-center py-8">
          <p class="text-gray-500">No relationships found for this theme.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/stores/theme'
import type { Theme } from '@/types/theme'

const themeStore = useThemeStore()

// Form data
const selectedThemeId = ref('')
const quickSearchQuery = ref('')

// Results
const relationshipResults = ref<any>(null)
const quickSearchResults = ref<Theme[]>([])

// Methods
async function handleGetRelationships() {
  try {
    relationshipResults.value = await themeStore.getThemeRelationships(selectedThemeId.value)
  } catch (error) {
    console.error('Failed to get relationships:', error)
  }
}

async function handleQuickSearch() {
  try {
    const results = await themeStore.searchThemes({
      query: quickSearchQuery.value,
      limit: 10,
    })
    quickSearchResults.value = results.themes
  } catch (error) {
    console.error('Quick search failed:', error)
  }
}

function selectTheme(themeId: string) {
  selectedThemeId.value = themeId
  quickSearchResults.value = []
  quickSearchQuery.value = ''
}
</script>
