"""
Example demonstrating practical use of Redis pipelines in the application.
"""
from typing import Dict, List, Optional
import asyncio
from app.core.redis import RedisConnection
from app.core.logging import get_logger

logger = get_logger("redis_pipeline_example")

# Example cache prefix
CACHE_PREFIX = "user:profile:"
CACHE_EXPIRY = 3600  # 1 hour


async def cache_user_profiles(user_profiles: List[Dict]) -> bool:
    """
    Cache multiple user profiles efficiently using Redis pipeline.
    
    Args:
        user_profiles: List of user profile dictionaries with at least an 'id' field
        
    Returns:
        True if caching was successful, False otherwise
    """
    if not user_profiles:
        return True
        
    # Prepare data for bulk insertion
    cache_data = {}
    for profile in user_profiles:
        user_id = profile.get('id')
        if not user_id:
            continue
            
        # In a real app, you would serialize the profile to JSON or another format
        # For this example, we'll just use a simple string
        cache_key = f"{CACHE_PREFIX}{user_id}"
        cache_value = f"Name: {profile.get('name', 'Unknown')}, Email: {profile.get('email', 'N/A')}"
        cache_data[cache_key] = cache_value
    
    # Use bulk_set with pipeline for efficient multi-key insertion
    success = await RedisConnection.bulk_set(cache_data, ex=CACHE_EXPIRY)
    if success:
        logger.info(f"Successfully cached {len(cache_data)} user profiles")
    else:
        logger.error("Failed to cache user profiles")
    
    return success


async def get_cached_user_profiles(user_ids: List[str]) -> Dict[str, Optional[str]]:
    """
    Retrieve multiple user profiles efficiently using Redis pipeline.
    
    Args:
        user_ids: List of user IDs to retrieve
        
    Returns:
        Dictionary mapping user IDs to their profile data (or None if not found)
    """
    if not user_ids:
        return {}
    
    # Create cache keys for each user ID
    cache_keys = [f"{CACHE_PREFIX}{user_id}" for user_id in user_ids]
    
    # Use bulk_get with pipeline for efficient multi-key retrieval
    cached_data = await RedisConnection.bulk_get(cache_keys)
    
    # Convert cache keys back to user IDs in the result
    result = {}
    for i, user_id in enumerate(user_ids):
        cache_key = cache_keys[i]
        result[user_id] = cached_data.get(cache_key)
    
    # Log cache hit/miss statistics
    hits = sum(1 for v in result.values() if v is not None)
    logger.info(f"Cache hit rate: {hits}/{len(user_ids)} ({hits/len(user_ids)*100:.1f}%)")
    
    return result


async def invalidate_user_profiles(user_ids: List[str]) -> int:
    """
    Invalidate multiple user profile caches efficiently using Redis pipeline.
    
    Args:
        user_ids: List of user IDs whose caches should be invalidated
        
    Returns:
        Number of cache entries successfully invalidated
    """
    if not user_ids:
        return 0
    
    # Create cache keys for each user ID
    cache_keys = [f"{CACHE_PREFIX}{user_id}" for user_id in user_ids]
    
    # Use bulk_delete with pipeline for efficient multi-key deletion
    deleted_count = await RedisConnection.bulk_delete(cache_keys)
    logger.info(f"Invalidated {deleted_count}/{len(user_ids)} user profile caches")
    
    return deleted_count


async def example_usage():
    """Example demonstrating the pipeline usage with user profiles."""
    # Sample user data
    users = [
        {"id": "1001", "name": "Alice Smith", "email": "<EMAIL>"},
        {"id": "1002", "name": "Bob Johnson", "email": "<EMAIL>"},
        {"id": "1003", "name": "Carol Williams", "email": "<EMAIL>"},
        {"id": "1004", "name": "Dave Brown", "email": "<EMAIL>"},
        {"id": "1005", "name": "Eve Davis", "email": "<EMAIL>"},
    ]
    
    # Cache the user profiles
    await cache_user_profiles(users)
    
    # Retrieve them from cache
    user_ids = [user["id"] for user in users]
    cached_profiles = await get_cached_user_profiles(user_ids)
    
    # Print the cached profiles
    print("\nCached User Profiles:")
    for user_id, profile_data in cached_profiles.items():
        print(f"User {user_id}: {profile_data}")
    
    # Invalidate some profiles
    await invalidate_user_profiles(user_ids[2:4])  # Invalidate users 1003 and 1004
    
    # Retrieve again to show some are missing
    cached_profiles = await get_cached_user_profiles(user_ids)
    
    # Print the cached profiles after invalidation
    print("\nCached User Profiles After Partial Invalidation:")
    for user_id, profile_data in cached_profiles.items():
        print(f"User {user_id}: {profile_data}")
    
    # Clean up by invalidating all profiles
    await invalidate_user_profiles(user_ids)


if __name__ == "__main__":
    # This won't run directly from imports, but shows how to use it in a script
    asyncio.run(example_usage()) 