"""
Tests for the TagCombinationAnalyzer component.

This test suite verifies the functionality of the tag combination analyzer
that identifies theme patterns from combinations of tags and genres.
"""
import pytest
from app.services.mapping.tag_combination_analyzer import Tag<PERSON>ombinationAnalyzer


@pytest.fixture
def analyzer():
    """Create a TagCombinationAnalyzer instance for testing."""
    return TagCombinationAnalyzer()


@pytest.mark.component
class TestTagCombinationAnalyzer:
    """Test suite for TagCombinationAnalyzer component."""

    def test_normalize_tags(self, analyzer):
        """Test that tags are correctly normalized to a consistent format."""
        # Test with string tags
        string_tags = ["Action", "Comedy"]
        normalized = analyzer._normalize_tags(string_tags)
        
        assert len(normalized) == 2
        assert normalized[0]["name"] == "Action"
        assert normalized[0]["rank"] == 50
        
        # Test with dictionary tags
        dict_tags = [
            {"name": "School", "category": "Setting", "rank": 80},
            {"name": "Romance", "category": "Genre", "rank": 70}
        ]
        normalized = analyzer._normalize_tags(dict_tags)
        
        assert len(normalized) == 2
        assert normalized[0]["name"] == "School"
        assert normalized[0]["category"] == "Setting"
        assert normalized[0]["rank"] == 80

    def test_match_patterns(self, analyzer):
        """Test pattern matching against predefined rules."""
        # Test with matching pattern
        elements = ["Thriller", "Psychological", "Mystery"]
        matches = analyzer._match_patterns(elements)
        
        assert len(matches) > 0
        assert any(match["theme_id"] == "psychological_thriller" for match in matches)
        
        # Test with non-matching pattern
        elements = ["Action", "Comedy"]
        matches = analyzer._match_patterns(elements)
        
        assert len(matches) == 0

    def test_analyze_combinations(self, analyzer):
        """Test the full tag combination analysis workflow."""
        genres = ["Romance", "Comedy"]
        tags = [
            {"name": "School", "category": "Setting", "rank": 80},
            {"name": "Slice of Life", "category": "Genre", "rank": 70}
        ]
        
        results = analyzer.analyze_combinations(genres, tags)
        
        # Should match the "School Romantic Comedy" pattern
        assert len(results) > 0
        assert any(result["theme_id"] == "romantic_comedy_school_life" for result in results)
        
        # Check confidence scores
        confidence_scores = analyzer.get_confidence_scores(results)
        assert "romantic_comedy_school_life" in confidence_scores
        assert confidence_scores["romantic_comedy_school_life"] > 0.7
        
        # Check mapping types
        mapping_types = analyzer.get_mapping_types(results)
        assert "romantic_comedy_school_life" in mapping_types
        assert mapping_types["romantic_comedy_school_life"] == "genre_combination"

    def test_individual_tag_analysis(self, analyzer):
        """Test analysis of individual tags with their weights."""
        tags = [
            {"name": "Psychological", "category": "Theme", "rank": 90},
        ]
        
        tag_weights = analyzer._calculate_tag_weights(tags)
        assert "Psychological" in tag_weights
        assert tag_weights["Psychological"] > 1.0
        
        results = analyzer._analyze_individual_tags(tags, tag_weights)
        assert len(results) == 1
        assert results[0]["theme_id"] == "psychological"
        assert results[0]["confidence"] > 0.5

    def test_combine_results(self, analyzer):
        """Test combination and deduplication of results from different sources."""
        pattern_themes = [
            {
                "theme_id": "psychological_thriller",
                "name": "Psychological Thriller",
                "confidence": 0.85,
                "mapping_type": "mood"
            }
        ]
        
        individual_themes = [
            {
                "theme_id": "psychological",
                "name": "Psychological",
                "confidence": 0.7,
                "mapping_type": "theme"
            },
            {
                "theme_id": "thriller",
                "name": "Thriller",
                "confidence": 0.65,
                "mapping_type": "genre"
            }
        ]
        
        combined = analyzer._combine_results(pattern_themes, individual_themes)
        
        # Should include all three themes (no duplicates)
        assert len(combined) == 3
        
        # Pattern theme should be included
        assert any(theme["theme_id"] == "psychological_thriller" for theme in combined)
        
        # Individual themes should be included
        assert any(theme["theme_id"] == "psychological" for theme in combined)
        assert any(theme["theme_id"] == "thriller" for theme in combined)
        
    def test_confidence_scoring(self, analyzer):
        """Test that confidence scores are accurately calculated."""
        # Create test themes with confidence values
        themes = [
            {
                "theme_id": "theme1",
                "confidence": 0.75,
                "mapping_type": "primary"
            },
            {
                "theme_id": "theme2",
                "confidence": 0.60,
                "mapping_type": "secondary"
            }
        ]
        
        # Get confidence scores
        scores = analyzer.get_confidence_scores(themes)
        
        # Verify scores are extracted correctly
        assert len(scores) == 2
        assert scores["theme1"] == 0.75
        assert scores["theme2"] == 0.60
        
    def test_mapping_types(self, analyzer):
        """Test that mapping types are accurately determined."""
        # Create test themes with mapping types
        themes = [
            {
                "theme_id": "theme1",
                "confidence": 0.75,
                "mapping_type": "mood"
            },
            {
                "theme_id": "theme2",
                "confidence": 0.60,
                "mapping_type": "plot"
            }
        ]
        
        # Get mapping types
        types = analyzer.get_mapping_types(themes)
        
        # Verify types are extracted correctly
        assert len(types) == 2
        assert types["theme1"] == "mood"
        assert types["theme2"] == "plot" 