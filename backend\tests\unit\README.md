# Unit Tests

This directory contains unit tests for the <PERSON><PERSON><PERSON> backend, focusing on pure functions with no external dependencies.

## What are Unit Tests?

Unit tests are the most basic level of testing, focusing on testing individual functions and methods in isolation. Key characteristics:

- Test a single function or method at a time
- Use mocks to replace all external dependencies
- Fast execution (milliseconds per test)
- High isolation (failures pinpoint exact issues)

## Test Categories

### 1. Pydantic Serialization (`test_pydantic_serialization.py`)

Tests that Pydantic models correctly serialize and deserialize data:
- Story model serialization
- Theme model serialization
- Recommendation model serialization
- Error handling for invalid data

### 2. Pure Functions

Tests for utility functions and pure business logic:
- Format conversion functions
- Validation functions
- Calculation functions

## Common Fixtures

The `conftest.py` in this directory provides fixtures specifically for unit testing:

- `mock_anilist_service`: Mock implementation of the AniList service
- `mock_redis`: Mock implementation of Redis client
- `sample_story_data`: Sample data for testing story-related functionality

## When to Write Unit Tests

Write unit tests when:

1. The function has clearly defined inputs and outputs
2. The function contains complex logic or edge cases
3. The function doesn't heavily depend on external services
4. You want to test business logic in isolation

## Best Practices

1. **Test Isolation**: Each test should be completely independent
2. **Mock External Dependencies**: Always mock external services and databases
3. **Test Edge Cases**: Include tests for boundary conditions and error cases
4. **Fast Execution**: Unit tests should run very quickly
5. **Test One Thing**: Each test should verify one specific behavior

## Running Unit Tests

```powershell
# Run all unit tests
.\tests\scripts\run_tests.ps1 -TestType unit

# Run specific unit tests
.\tests\scripts\run_tests.ps1 -TestType unit -TestPattern "serialization"

# Run with coverage
.\tests\scripts\run_tests.ps1 -TestType unit -Coverage
```

Or manually:

```powershell
# Run directly with pytest
python -m pytest tests/unit/ -v

# Run in Docker
docker exec tahimoto-backend python -m pytest tests/unit/ -v
``` 