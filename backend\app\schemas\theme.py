from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
from app.schemas.theme_category import ThemeCategory, MediaType

class ThemeStatus(str, Enum):
    PENDING = "PENDING"
    VERIFIED = "VERIFIED"
    NEEDS_REVIEW = "NEEDS_REVIEW"

class ThemeMappingType(str, Enum):
    PRIMARY = "primary"
    SECONDARY = "secondary"
    MOOD = "mood"
    NARRATIVE = "narrative"
    CHARACTER = "character"
    SETTING = "setting"

class ThemeSubCategory(str, Enum):
    # Mood sub-categories
    COZY = "COZY"
    TENSE = "TENSE"
    MELANCHOLIC = "MELANCHOLIC"
    UPLIFTING = "UPLIFTING"
    CONTEMPLATIVE = "CONTEMPLATIVE"
    CHAOTIC = "CHAOTIC"
    
    # Narrative structure sub-categories
    HERO_JOURNEY = "HERO_JOURNEY"
    SLICE_OF_LIFE = "SLICE_OF_LIFE"
    EPISODIC = "EPISODIC"
    LINEAR = "LINEAR"
    NONLINEAR = "NONLINEAR"
    INSTRUCTIONAL = "INSTRUCTIONAL"
    MYSTERY = "MYSTERY"
    
    # Character dynamic sub-categories
    RIVALRY = "RIVALRY"
    MENTORSHIP = "MENTORSHIP"
    FRIENDSHIP = "FRIENDSHIP"
    ROMANCE = "ROMANCE"
    FAMILY = "FAMILY"
    INTERNAL_CONFLICT = "INTERNAL_CONFLICT"
    ENSEMBLE = "ENSEMBLE"
    
    # Setting type sub-categories
    URBAN = "URBAN"
    RURAL = "RURAL"
    FANTASY = "FANTASY"
    HISTORICAL = "HISTORICAL"
    FUTURISTIC = "FUTURISTIC"
    WORKPLACE = "WORKPLACE"
    SCHOOL = "SCHOOL"
    DOMESTIC = "DOMESTIC"
    
    # General sub-categories
    OTHER = "OTHER"

class ThemeDimension(str, Enum):
    NARRATIVE = "NARRATIVE"
    EMOTIONAL = "EMOTIONAL"
    VISUAL = "VISUAL"
    CULTURAL = "CULTURAL"
    PHILOSOPHICAL = "PHILOSOPHICAL"
    TECHNICAL = "TECHNICAL"
    TEMPORAL = "TEMPORAL"
    PSYCHOLOGICAL = "PSYCHOLOGICAL"

class EvolutionStage(str, Enum):
    FOUNDATION = "FOUNDATION"
    GUIDED = "GUIDED"
    ADAPTIVE = "ADAPTIVE"
    EMERGENT = "EMERGENT"

class RelationshipType(str, Enum):
    COMPLEMENTS = "COMPLEMENTS"
    CONTRASTS = "CONTRASTS"
    REQUIRES = "REQUIRES"
    SPECIALIZES = "SPECIALIZES"
    PARENT_OF = "PARENT_OF"
    CHILD_OF = "CHILD_OF"
    EVOLVES_INTO = "EVOLVES_INTO"
    EVOLVED_FROM = "EVOLVED_FROM"
    SUBVERTS = "SUBVERTS"
    COEXISTS_WITH = "COEXISTS_WITH"
    TENSIONS_WITH = "TENSIONS_WITH"
    CULTURALLY_RELATED = "CULTURALLY_RELATED"
    VISUALLY_SIMILAR = "VISUALLY_SIMILAR"
    NARRATIVELY_LINKED = "NARRATIVELY_LINKED"

class ThemeBase(BaseModel):
    name: str
    description: Optional[str] = None
    parent_theme_id: Optional[str] = None
    category: Optional[ThemeCategory] = None
    sub_category: Optional[ThemeSubCategory] = None
    dimensions: Optional[List[ThemeDimension]] = None
    cultural_context: Optional[List[str]] = None
    evolution_stage: Optional[EvolutionStage] = None
    implicit_tags: Optional[List[str]] = None
    # Enhanced fields for broader media support
    media_types: Optional[List[MediaType]] = None
    cross_media_mappings: Optional[Dict[str, str]] = None  # Maps media type to equivalent theme
    llm_context: Optional[str] = None  # Rich context for LLM consumption
    usage_examples: Optional[List[str]] = None  # Examples of usage across media

class ThemeCreate(ThemeBase):
    pass

class ThemeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    parent_theme_id: Optional[str] = None
    category: Optional[ThemeCategory] = None
    sub_category: Optional[ThemeSubCategory] = None
    dimensions: Optional[List[ThemeDimension]] = None
    confidence: Optional[float] = None
    status: Optional[ThemeStatus] = None
    version: Optional[int] = None
    deprecated: Optional[bool] = None
    replaced_by: Optional[str] = None
    cultural_context: Optional[List[str]] = None
    evolution_stage: Optional[EvolutionStage] = None
    implicit_tags: Optional[List[str]] = None

class ThemeMappingBase(BaseModel):
    source_type: str
    source_id: str
    theme_id: str
    mapping_strength: float = Field(ge=0.0, le=1.0)
    mapping_type: ThemeMappingType
    context: Optional[str] = None
    llm_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    needs_review: bool = True

class ThemeMappingCreate(ThemeMappingBase):
    pass

class ThemeMappingUpdate(BaseModel):
    mapping_strength: Optional[float] = Field(None, ge=0.0, le=1.0)
    context: Optional[str] = None
    llm_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    needs_review: Optional[bool] = None
    mapping_type: Optional[ThemeMappingType] = None

class Theme(ThemeBase):
    id: str
    confidence: float
    status: ThemeStatus
    created_at: datetime
    updated_at: datetime
    version: Optional[int] = None
    deprecated: Optional[bool] = None
    replaced_by: Optional[str] = None

    class Config:
        orm_mode = True

class ThemeMapping(ThemeMappingBase):
    id: str
    theme: Theme
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ThemeAnalysis(BaseModel):
    themes: List[ThemeMapping]
    primary_themes: List[ThemeMapping]
    secondary_themes: List[ThemeMapping]
    mood_themes: List[ThemeMapping]
    character_themes: List[ThemeMapping]
    plot_themes: List[ThemeMapping]
    confidence: float = Field(ge=0.0, le=1.0)
    last_analyzed: datetime

    class Config:
        orm_mode = True

class ThemeRelationship(BaseModel):
    id: str
    source_id: str
    source_name: str
    target_id: str
    target_name: str
    type: RelationshipType
    strength: Optional[float] = None
    tension: Optional[float] = None
    description: Optional[str] = None
    dimensions: Optional[List[ThemeDimension]] = None
    cultural_context: Optional[List[str]] = None
    version: Optional[int] = None
    evidence: Optional[str] = None
    confidence: Optional[float] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ThemeRelationshipCreate(BaseModel):
    source_id: str
    target_id: str
    type: RelationshipType
    strength: Optional[float] = 0.5
    tension: Optional[float] = 0.0
    description: Optional[str] = None
    dimensions: Optional[List[ThemeDimension]] = None
    cultural_context: Optional[List[str]] = None
    evidence: Optional[str] = None
    confidence: Optional[float] = None

class ThemeRelationshipUpdate(BaseModel):
    type: Optional[RelationshipType] = None
    strength: Optional[float] = None
    tension: Optional[float] = None
    description: Optional[str] = None
    dimensions: Optional[List[ThemeDimension]] = None
    cultural_context: Optional[List[str]] = None
    version: Optional[int] = None
    evidence: Optional[str] = None
    confidence: Optional[float] = None

# MCP-specific schemas for LLM integration
class MCPThemeReference(BaseModel):
    """Simplified theme reference optimized for MCP/LLM consumption."""
    id: str
    name: str
    description: str
    category: ThemeCategory
    media_types: List[MediaType]
    llm_context: Optional[str] = None
    related_themes: Optional[List[str]] = None  # Theme IDs
    confidence: float = Field(ge=0.0, le=1.0)

    class Config:
        schema_extra = {
            "example": {
                "id": "theme_001",
                "name": "Coming of Age",
                "description": "Character growth from youth to maturity",
                "category": "NARRATIVE_STRUCTURE",
                "media_types": ["ANIME", "BOOK", "TV"],
                "llm_context": "This theme involves character development, personal growth, and the transition from childhood innocence to adult understanding.",
                "related_themes": ["theme_002", "theme_003"],
                "confidence": 0.95
            }
        }

class MCPThemeQuery(BaseModel):
    """Query parameters for MCP theme requests."""
    media_type: Optional[MediaType] = None
    category: Optional[ThemeCategory] = None
    search_term: Optional[str] = None
    include_relationships: bool = False
    max_results: int = Field(default=50, ge=1, le=1000)

class MCPThemeResponse(BaseModel):
    """Response format for MCP theme queries."""
    themes: List[MCPThemeReference]
    total_count: int
    query_metadata: Dict[str, Any]

class MCPMediaAnalysisRequest(BaseModel):
    """Request for analyzing media content via MCP."""
    media_type: MediaType
    title: str
    description: Optional[str] = None
    genres: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    additional_context: Optional[Dict[str, Any]] = None

class MCPMediaAnalysisResponse(BaseModel):
    """Response for media analysis via MCP."""
    detected_themes: List[MCPThemeReference]
    confidence_scores: Dict[str, float]
    analysis_metadata: Dict[str, Any]
    suggested_relationships: Optional[List[Dict[str, Any]]] = None