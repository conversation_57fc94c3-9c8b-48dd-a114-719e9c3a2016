"""
Tests for the IdService class.
"""
import unittest
import uuid
import re
from app.services.id_service import IdService

class TestIdService(unittest.TestCase):
    """Test cases for the IdService class."""
    
    def test_standardize_id_theme(self):
        """Test standardizing theme IDs."""
        # Test without prefix
        self.assertEqual(IdService.standardize_id("adventure", "theme"), "theme_adventure")
        
        # Test with prefix already
        self.assertEqual(IdService.standardize_id("theme_adventure", "theme"), "theme_adventure")
        
        # Test with UUID
        test_uuid = str(uuid.uuid4())
        self.assertEqual(IdService.standardize_id(test_uuid, "theme"), f"theme_{test_uuid}")
        
        # Test with UUID and prefix
        self.assertEqual(IdService.standardize_id(f"theme_{test_uuid}", "theme"), f"theme_{test_uuid}")
    
    def test_standardize_id_story(self):
        """Test standardizing story IDs."""
        # Test numeric ID
        self.assertEqual(IdService.standardize_id("123", "story"), "story_123")
        
        # Test with prefix already
        self.assertEqual(IdService.standardize_id("story_123", "story"), "story_123")
    
    def test_to_database_id(self):
        """Test converting to database ID format."""
        # Test theme ID
        self.assertEqual(IdService.to_database_id("theme_adventure", "theme"), "adventure")
        
        # Test story ID
        self.assertEqual(IdService.to_database_id("story_123", "story"), "123")
        
        # Test mapping ID
        test_uuid = str(uuid.uuid4())
        self.assertEqual(IdService.to_database_id(f"mapping_{test_uuid}", "mapping"), test_uuid)
        
        # Test already in database format
        self.assertEqual(IdService.to_database_id("adventure", "theme"), "adventure")
    
    def test_is_uuid(self):
        """Test UUID detection."""
        # Valid UUID
        valid_uuid = str(uuid.uuid4())
        self.assertTrue(IdService.is_uuid(valid_uuid))
        
        # Valid UUID with prefix
        self.assertTrue(IdService.is_uuid(f"theme_{valid_uuid}"))
        
        # Invalid UUID
        self.assertFalse(IdService.is_uuid("not-a-uuid"))
        self.assertFalse(IdService.is_uuid("theme_adventure"))
    
    def test_is_semantic_id(self):
        """Test semantic ID detection."""
        # Semantic ID
        self.assertTrue(IdService.is_semantic_id("adventure"))
        self.assertTrue(IdService.is_semantic_id("theme_adventure"))
        
        # UUID is not a semantic ID
        valid_uuid = str(uuid.uuid4())
        self.assertFalse(IdService.is_semantic_id(valid_uuid))
        self.assertFalse(IdService.is_semantic_id(f"theme_{valid_uuid}"))
    
    def test_validate_id(self):
        """Test ID validation."""
        # Valid theme ID
        self.assertTrue(IdService.validate_id("theme_adventure", "theme"))
        
        # Valid story ID
        self.assertTrue(IdService.validate_id("story_123", "story"))
        
        # Valid mapping ID with UUID
        test_uuid = str(uuid.uuid4())
        self.assertTrue(IdService.validate_id(f"mapping_{test_uuid}", "mapping"))
        
        # Invalid theme ID (missing prefix)
        self.assertFalse(IdService.validate_id("adventure", "theme"))
        
        # Invalid story ID (missing prefix)
        self.assertFalse(IdService.validate_id("123", "story"))
    
    def test_generate_id(self):
        """Test ID generation."""
        # Theme ID
        theme_id = IdService.generate_id("theme")
        self.assertTrue(theme_id.startswith("theme_"))
        self.assertTrue(IdService.is_uuid(theme_id[6:]))
        
        # Story ID (this assumes a simple implementation)
        story_id = IdService.generate_id("story")
        self.assertTrue(story_id.startswith("story_"))
        
        # Mapping ID
        mapping_id = IdService.generate_id("mapping")
        self.assertTrue(mapping_id.startswith("mapping_"))
        self.assertTrue(IdService.is_uuid(mapping_id[8:]))
    
    def test_get_cache_key(self):
        """Test cache key generation."""
        # Basic theme key
        self.assertEqual(IdService.get_cache_key("adventure", "theme"), "theme:theme_adventure")
        
        # With suffix
        self.assertEqual(
            IdService.get_cache_key("adventure", "theme", "stats"), 
            "theme:theme_adventure:stats"
        )
        
        # Story key
        self.assertEqual(IdService.get_cache_key("123", "story"), "story:story_123")
    
    def test_create_relationship_id(self):
        """Test relationship ID creation."""
        # Test with non-prefixed IDs
        rel_id = IdService.create_relationship_id("adventure", "PARENT_OF", "journey")
        self.assertEqual(rel_id, "theme_adventure-PARENT_OF-theme_journey")
        
        # Test with already prefixed IDs
        rel_id = IdService.create_relationship_id("theme_adventure", "PARENT_OF", "theme_journey")
        self.assertEqual(rel_id, "theme_adventure-PARENT_OF-theme_journey")
    
    def test_parse_relationship_id(self):
        """Test relationship ID parsing."""
        # Create and parse relationship ID
        rel_id = "theme_adventure-PARENT_OF-theme_journey"
        source_id, rel_type, target_id = IdService.parse_relationship_id(rel_id)
        
        self.assertEqual(source_id, "theme_adventure")
        self.assertEqual(rel_type, "PARENT_OF")
        self.assertEqual(target_id, "theme_journey")
    
    def test_find_by_id_or_name(self):
        """Test finding by ID or name."""
        # Find by UUID
        test_uuid = str(uuid.uuid4())
        result = IdService.find_by_id_or_name(test_uuid, "theme")
        self.assertEqual(result["param_name"], "id")
        self.assertEqual(result["param_value"], test_uuid)
        self.assertEqual(result["condition"], "n.id = $id")
        
        # Find by name
        result = IdService.find_by_id_or_name("adventure", "theme")
        self.assertEqual(result["param_name"], "name")
        self.assertEqual(result["param_value"], "adventure")
        self.assertTrue("name" in result["condition"])
        
        # Find by prefixed name
        result = IdService.find_by_id_or_name("theme_adventure", "theme")
        self.assertEqual(result["param_name"], "name")
        self.assertEqual(result["param_value"], "adventure")

if __name__ == "__main__":
    unittest.main() 