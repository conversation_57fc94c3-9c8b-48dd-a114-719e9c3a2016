#!/usr/bin/env python3
"""
Debug the CrossMediaAnimeMapper directly.
"""
import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.mapping.cross_media_anime_mapper import CrossMediaAnimeMapper

async def test_cross_media_mapper():
    """Test the CrossMediaAnimeMapper directly."""
    print("🎯 Testing CrossMediaAnimeMapper Directly")
    print("=" * 50)
    
    mapper = CrossMediaAnimeMapper()
    
    # Test data - The Apothecary Diaries
    anime_metadata = {
        "title_romaji": "Kusuriya no Hitorigoto",
        "title_english": "The Apothecary Diaries",
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Female Protagonist", "rank": 95},
            {"name": "Historical", "rank": 90},
            {"name": "Palace", "rank": 85},
            {"name": "Medicine", "rank": 80}
        ],
        "synopsis": "<PERSON><PERSON><PERSON> lived a quiet life as an apothecary in the red-light district."
    }
    
    print(f"1. Testing with metadata:")
    print(f"   Title: {anime_metadata['title_english']}")
    print(f"   Genres: {anime_metadata['genres']}")
    print(f"   Tags: {[tag['name'] for tag in anime_metadata['tags']]}")
    
    try:
        # Test the mapping
        mappings = await mapper.map_anime_to_themes(anime_metadata)
        
        print(f"\n2. Mapping Results:")
        print(f"   Found {len(mappings)} mappings")
        
        if mappings:
            for i, mapping in enumerate(mappings, 1):
                theme = mapping.get('theme', {})
                print(f"   {i}. {theme.get('name', 'Unknown')} ({theme.get('category', 'Unknown')})")
                print(f"      Strength: {mapping.get('mapping_strength', 0):.1%}")
                print(f"      Type: {mapping.get('mapping_type', 'Unknown')}")
                print(f"      Source: {mapping.get('source', 'Unknown')}")
                print(f"      Context: {mapping.get('context', 'Unknown')}")
        else:
            print("   No mappings found!")
            
            # Debug: Check individual genre mappings
            print(f"\n3. Debug: Checking individual genre mappings...")
            for genre in anime_metadata['genres']:
                if genre in mapper.genre_to_theme_map:
                    theme_name = mapper.genre_to_theme_map[genre]
                    print(f"   Genre '{genre}' maps to theme '{theme_name}'")
                    
                    # Try to get the theme from database
                    theme = await mapper.get_theme_by_name(theme_name)
                    if theme:
                        print(f"   ✅ Theme '{theme_name}' found in database")
                    else:
                        print(f"   ❌ Theme '{theme_name}' NOT found in database")
                else:
                    print(f"   Genre '{genre}' has no mapping")
            
            # Debug: Check individual tag mappings
            print(f"\n4. Debug: Checking individual tag mappings...")
            for tag in anime_metadata['tags']:
                tag_name = tag['name']
                if tag_name in mapper.tag_to_theme_map:
                    theme_name = mapper.tag_to_theme_map[tag_name]
                    print(f"   Tag '{tag_name}' maps to theme '{theme_name}'")
                    
                    # Try to get the theme from database
                    theme = await mapper.get_theme_by_name(theme_name)
                    if theme:
                        print(f"   ✅ Theme '{theme_name}' found in database")
                    else:
                        print(f"   ❌ Theme '{theme_name}' NOT found in database")
                else:
                    print(f"   Tag '{tag_name}' has no mapping")
                    
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_theme_lookup():
    """Test theme lookup directly."""
    print(f"\n🎯 Testing Theme Lookup")
    print("=" * 30)
    
    mapper = CrossMediaAnimeMapper()
    
    # Test looking up specific themes
    test_themes = ["Tragedy", "Mystery", "Historical", "Dark"]
    
    for theme_name in test_themes:
        try:
            theme = await mapper.get_theme_by_name(theme_name)
            if theme:
                print(f"   ✅ '{theme_name}': {theme.get('id', 'no-id')} - {theme.get('category', 'no-category')}")
            else:
                print(f"   ❌ '{theme_name}': Not found")
        except Exception as e:
            print(f"   ❌ '{theme_name}': Exception - {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_cross_media_mapper())
    asyncio.run(test_theme_lookup())
