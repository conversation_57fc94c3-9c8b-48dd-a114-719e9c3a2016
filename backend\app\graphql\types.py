from __future__ import annotations
from typing import List, Optional, Dict, Any
import strawberry
from datetime import datetime
from enum import Enum

@strawberry.enum
class MappingStatus(Enum):
    PENDING = "PENDING"
    MAPPED = "MAPPED"
    NEEDS_REVIEW = "NEEDS_REVIEW"
    VERIFIED = "VERIFIED"

@strawberry.enum
class ThemeMappingType(Enum):
    CORE = "core"
    SECONDARY = "secondary"
    MOOD = "mood"
    CHARACTER = "character"
    PLOT = "plot"
    SETTING = "setting"
    TECHNICAL = "technical"
    DEMOGRAPHIC = "demographic"

@strawberry.enum
class CorrelationType(Enum):
    EQUIVALENT = "equivalent"  # Themes that mean the same thing across media
    COMPOSITE = "composite"    # Theme that is made up of other themes (e.g., grimdark)
    RELATED = "related"       # Themes that often appear together
    OPPOSITE = "opposite"     # Themes that contrast each other

@strawberry.type
class Theme:
    id: str
    name: str
    description: str
    parent_theme_id: Optional[str] = None
    confidence: float
    created_at: datetime
    updated_at: datetime
    status: MappingStatus = MappingStatus.PENDING

@strawberry.type
class ThemeCorrelation:
    id: str
    themes: List[str]
    type: CorrelationType
    confidence: float
    created_at: datetime
    evidence: Optional[List[str]] = None

@strawberry.type
class CrossMediaMapping:
    id: str
    source_theme: str
    target_theme: str
    media_type: str
    confidence: float
    created_at: datetime
    evidence: Optional[List[str]] = None

@strawberry.type
class ThemeSuggestion:
    id: str
    name: str
    description: str
    confidence: float
    evidence: List[str]
    created_at: datetime
    status: MappingStatus = MappingStatus.PENDING

@strawberry.scalar(
    name="JSON",
    description="The `JSON` scalar type represents JSON values as specified by ECMA-404",
    serialize=lambda v: v,
    parse_value=lambda v: v,
)
class JSON:
    @staticmethod
    def serialize(value: Any) -> Any:
        return value

    @staticmethod
    def parse_value(value: Any) -> Any:
        return value

@strawberry.type
class ConfidenceScores:
    overall: float
    theme_mapping: float
    correlation: float
    cross_media: float
    suggestions: float

@strawberry.type
class ThemeMapping:
    id: str
    theme: Theme
    mapping_type: ThemeMappingType
    mapping_strength: float = 1.0
    context: Optional[str] = None
    needs_review: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    status: MappingStatus = MappingStatus.PENDING

@strawberry.type
class ThemeAnalysisSession:
    session_id: str
    source_id: str
    source_type: str
    status: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    correlations: List['ThemeCorrelation']
    cross_media_mappings: List['CrossMediaMapping']
    theme_suggestions: List['ThemeSuggestion']
    confidence_scores: ConfidenceScores

@strawberry.type
class ThemeAnalysis:
    themes: List[ThemeMapping]
    core_themes: List[ThemeMapping]
    secondary_themes: List[ThemeMapping]
    mood_themes: List[ThemeMapping]
    character_themes: List[ThemeMapping]
    plot_themes: List[ThemeMapping]
    confidence: float
    last_analyzed: datetime
    suggestions: List[ThemeSuggestion] = strawberry.field(default_factory=list)
    confidence_scores: Optional[ConfidenceScores] = None
    session: Optional[ThemeAnalysisSession] = None

@strawberry.type
class ThemeStats:
    total_themes: int
    mapped_count: int
    pending_count: int
    needs_review_count: int
    cache_hit_rate: float

@strawberry.input
class TagInput:
    name: str
    category: str
    rank: Optional[int] = None

@strawberry.input
class ThemeInput:
    name: str
    description: str
    parent_theme_id: Optional[str] = None
    confidence: Optional[float] = 1.0

@strawberry.input
class ThemeMappingInput:
    source_type: str
    source_id: str
    theme_id: str
    mapping_strength: float = 1.0
    mapping_type: ThemeMappingType
    context: Optional[str] = None
    llm_confidence: Optional[float] = None
    needs_review: bool = True

@strawberry.input
class ThemeUpdateInput:
    name: Optional[str] = None
    description: Optional[str] = None
    parent_theme_id: Optional[str] = None
    confidence: Optional[float] = None
    status: Optional[MappingStatus] = None 

@strawberry.input
class CorrelationInput:
    theme1: str
    theme2: str
    correlation_type: CorrelationType
    confidence: float = 1.0
    evidence: Optional[List[str]] = None

@strawberry.input
class CrossMediaMappingInput:
    source_theme: str
    target_theme: str
    media_type: str
    confidence: float = 1.0
    evidence: Optional[List[str]] = None

@strawberry.input
class ThemeSuggestionInput:
    name: str
    description: str
    confidence: float = 1.0
    evidence: List[str]

@strawberry.type
class Mutation:
    @strawberry.mutation
    async def analyze_themes(
        self,
        source_type: str,
        source_id: str,
        genres: List[str],
        tags: List[TagInput]
    ) -> ThemeAnalysis:
        """Analyze themes for a source."""
        raise NotImplementedError("This is just a type definition")