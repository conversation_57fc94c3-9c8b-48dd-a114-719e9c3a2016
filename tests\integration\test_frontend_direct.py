#!/usr/bin/env python3
"""
Test what the frontend is actually receiving by simulating the exact API call.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_frontend_api_call():
    """Test the exact API call the frontend makes."""
    print("🎯 Testing Frontend API Call Simulation")
    print("=" * 60)
    
    # Test with a specific anime that should have Female Empowerment themes
    anime_id = "21202"  # The Apothecary Diaries
    
    print(f"1. Testing contextual themes API for anime {anime_id}...")
    
    try:
        # This is the exact call the frontend makes
        request_data = {
            "anime_id": anime_id,
            "include_reasoning": True
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Contextual API Success!")
            print(f"   📊 Found {len(data['theme_matches'])} themes")
            
            # Show what the frontend should receive
            for i, match in enumerate(data['theme_matches'], 1):
                print(f"   {i}. {match['theme_name']}: {match.get('confidence_percent', 'N/A')}")
            
            return True, data
        else:
            print(f"   ❌ Contextual API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Contextual API Exception: {str(e)}")
        return False, None

def test_fallback_api():
    """Test the fallback API that might be getting called."""
    print(f"\n2. Testing fallback theme mapping API...")
    
    anime_id = "21202"
    anime_metadata = {
        "title_romaji": "Kusuriya no Hitorigoto",
        "title_english": "The Apothecary Diaries",
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Female Protagonist", "rank": 95},
            {"name": "Historical", "rank": 90}
        ],
        "synopsis": "Maomao lived a quiet life as an apothecary."
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/theme-mapping/anime/{anime_id}/create-mappings",
            json=anime_metadata,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Fallback API Success!")
            print(f"   📊 Found {data.get('created_mappings', 0)} mappings")
            
            mappings = data.get('mappings', [])
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i}. {mapping.get('theme_name', 'Unknown')}: {mapping.get('mapping_strength', 0):.1%}")
            
            return True, data
        else:
            print(f"   ❌ Fallback API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Fallback API Exception: {str(e)}")
        return False, None

def test_simple_themes_api():
    """Test the simple themes API that might be getting called."""
    print(f"\n3. Testing simple themes API...")
    
    anime_metadata = {
        "title_romaji": "Kusuriya no Hitorigoto",
        "title_english": "The Apothecary Diaries",
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Female Protagonist", "rank": 95},
            {"name": "Historical", "rank": 90}
        ],
        "synopsis": "Maomao lived a quiet life as an apothecary."
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/simple-themes/anime/analyze",
            json=anime_metadata,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            mappings = response.json()
            print(f"   ✅ Simple Themes API Success!")
            print(f"   📊 Found {len(mappings)} themes")
            
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i}. {mapping.get('theme_name', 'Unknown')}: {mapping.get('mapping_strength', 0):.1%} ({mapping.get('source', 'Unknown')})")
            
            return True, mappings
        else:
            print(f"   ❌ Simple Themes API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Simple Themes API Exception: {str(e)}")
        return False, None

if __name__ == "__main__":
    contextual_success, contextual_data = test_frontend_api_call()
    fallback_success, fallback_data = test_fallback_api()
    simple_success, simple_data = test_simple_themes_api()
    
    print(f"\n🎯 Summary")
    print("=" * 30)
    print(f"Contextual API: {'✅ Working' if contextual_success else '❌ Failed'}")
    print(f"Fallback API: {'✅ Working' if fallback_success else '❌ Failed'}")
    print(f"Simple Themes API: {'✅ Working' if simple_success else '❌ Failed'}")
    
    if simple_success and simple_data:
        print(f"\n⚠️  The frontend might be getting Simple Themes results:")
        print(f"   These match what you saw: Tragedy, Emotional Growth, Human Condition")
        print(f"   All from 'Drama' genre with 80% confidence")
