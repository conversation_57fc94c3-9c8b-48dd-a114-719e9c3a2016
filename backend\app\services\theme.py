from typing import List, Optional
from datetime import datetime
from app.core.logging import get_logger
from app.schemas.theme import (
    ThemeAnalysis, ThemeMapping, Theme, ThemeMappingType,
    MappingStatus, ThemeSuggestion, ConfidenceScores, TagInput
)
from app.core.cache import CacheManager
from .theme_redis import theme_redis, ThemeAnalysisSession
from .theme_analysis import theme_analysis_service

logger = get_logger("theme_service")

# Initialize cache for theme analysis
theme_cache = CacheManager("theme_analysis", 3600)  # 1 hour TTL

class ThemeService:
    def __init__(self):
        self.logger = logger

    async def analyze_story(
        self,
        source_type: str,
        source_id: str,
        genres: List[str],
        tags: List[TagInput]
    ) -> ThemeAnalysis:
        """
        Analyze themes for a story based on its genres and tags.
        Uses the existing theme analysis system and Redis caching.
        """
        self.logger.info(f"[Theme Analysis] Starting analysis for {source_type}:{source_id}")
        self.logger.debug(f"[Theme Analysis] Input: genres={genres}, tags={tags}")
        
        cache_key = f"theme_analysis:{source_type}:{source_id}"
        
        # Check cache first
        cached = await theme_cache.get(cache_key)
        if cached:
            self.logger.debug(f"Cache hit for theme analysis: {cache_key}")
            return ThemeAnalysis(**cached)

        try:
            # Create analysis session
            session = ThemeAnalysisSession(source_id, source_type)
            await session.start({
                "genres": genres,
                "tags": [tag.dict() for tag in tags]
            })
            
            # Get existing analysis if any
            existing = await theme_redis.get_analysis(source_type, source_id)
            if existing:
                self.logger.info(f"Found existing analysis in Redis for {source_id}")
                
                # Update the analysis with new data
                updated = await theme_analysis_service.update_analysis(
                    existing=existing,
                    genres=genres,
                    tags=tags,
                    session=session
                )
                
                if updated:
                    self.logger.info("Successfully updated existing analysis")
                    return ThemeAnalysis(**updated)
            
            # Perform new analysis
            self.logger.info("Performing new theme analysis")
            analysis = await theme_analysis_service.analyze_story_themes(
                source_type=source_type,
                source_id=source_id,
                genres=genres,
                tags=tags,
                session=session
            )
            
            # Store in Redis
            await theme_redis.set_analysis(
                source_type=source_type,
                source_id=source_id,
                analysis_data=analysis.dict()
            )
            
            # Cache the result
            await theme_cache.set(cache_key, analysis.dict())
            
            # Complete the session
            await session.complete()
            
            self.logger.info(f"Completed theme analysis for {source_id}")
            return analysis

        except Exception as e:
            self.logger.error(f"Error in theme analysis: {str(e)}", exc_info=True)
            raise

theme_service = ThemeService() 