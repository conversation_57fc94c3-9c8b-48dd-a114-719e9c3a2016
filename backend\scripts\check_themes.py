#!/usr/bin/env python
"""
Script to check the themes in Neo4j
"""
import asyncio
from neo4j import AsyncGraphDatabase
from datetime import datetime

# Helper function to format theme data (same as in resolver)
def format_theme(theme_data):
    """Format theme data for GraphQL response."""
    # Ensure category is properly set - if it's missing or empty, use UNCATEGORIZED
    category = theme_data.get("category")
    if not category:
        category = "UNCATEGORIZED"
    
    return {
        "id": theme_data["id"],
        "name": theme_data["name"],
        "description": theme_data.get("description", ""),
        "parentThemeId": theme_data.get("parent_theme_id"),
        "category": category,
        "confidence": theme_data.get("confidence", 1.0),
        "createdAt": theme_data.get("created_at", datetime.now().isoformat()),
        "updatedAt": theme_data.get("updated_at", datetime.now().isoformat()),
        "status": theme_data.get("status", "MAPPED")
    }

# Script to check themes in Neo4j database
async def main():
    # Connect to Neo4j
    uri = "neo4j://neo4j:7687"
    driver = AsyncGraphDatabase.driver(uri, auth=("neo4j", "password"))
    
    try:
        async with driver.session() as session:
            # Count total themes
            result = await session.run("MATCH (n:Theme) RETURN count(n) as count")
            record = await result.single()
            total_themes = record["count"]
            print(f"Total themes in database: {total_themes}")
            
            # Get the exact query used in the resolver
            query = """
            MATCH (n:Theme)
            RETURN n
            ORDER BY n.name ASC
            SKIP $skip
            LIMIT $limit
            """
            
            print(f"\nExecuting query: {query}")
            
            # Execute the query with the same parameters as the resolver
            result = await session.run(query, {"skip": 0, "limit": 10})
            records = await result.fetch(10)
            
            print(f"\nQuery returned {len(records)} records")
            
            # Transform records to expected format (same as in resolver)
            themes = []
            for i, record in enumerate(records):
                theme_node = record["n"]
                theme_data = dict(theme_node)
                
                print(f"\nTheme {i+1}:")
                print(f"  Original data: {theme_data}")
                
                # Ensure ID format with theme_ prefix
                if "id" in theme_data:
                    # Strip any existing theme_ prefix to avoid double prefixing
                    theme_id = theme_data["id"]
                    if theme_id.startswith("theme_"):
                        theme_id = theme_id[6:]  # Remove the "theme_" prefix
                    
                    # Add the theme_ prefix
                    theme_data["id"] = f"theme_{theme_id}"
                    print(f"  Normalized ID: {theme_data['id']}")
                
                # Format the theme data
                formatted_theme = format_theme(theme_data)
                themes.append(formatted_theme)
                print(f"  Formatted theme: {formatted_theme}")
            
            print(f"\nTotal themes after processing: {len(themes)}")
            
            # Check if there are any Theme nodes without required properties
            missing_props_query = """
            MATCH (n:Theme) 
            WHERE n.id IS NULL OR n.name IS NULL
            RETURN count(n) as count
            """
            result = await session.run(missing_props_query)
            record = await result.single()
            missing_props_count = record["count"]
            print(f"\nThemes missing required properties (id or name): {missing_props_count}")
            
            if missing_props_count > 0:
                # Get examples of themes with missing properties
                result = await session.run("""
                MATCH (n:Theme) 
                WHERE n.id IS NULL OR n.name IS NULL
                RETURN n LIMIT 5
                """)
                records = await result.fetch(5)
                print("\nExamples of themes with missing properties:")
                for i, record in enumerate(records):
                    theme_data = dict(record["n"])
                    print(f"  Theme {i+1}: {theme_data}")
    
    finally:
        await driver.close()

# Run the async main function
if __name__ == "__main__":
    asyncio.run(main()) 