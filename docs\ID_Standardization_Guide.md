# ID Standardization Guide

## Overview

This document provides a comprehensive guide to ID standardization in the Tahimoto system. Consistent ID formats are crucial for maintaining data integrity, enabling reliable lookups, and ensuring clear relationships between different entity types.

## The Problem

As Tahimoto has grown, we've developed several different ID formats:

1. **Raw UUIDs** in Neo4j database (e.g., "7fc75255-6e7f-400f-a74c-2ec78668aaf3")
2. **Prefixed IDs** in application logic (e.g., "theme_lost_civilization")
3. **Semantic slugs** without prefixes (e.g., "lost_civilization")

This inconsistency has led to:
- Data retrieval issues
- Incorrect relationship mapping
- Caching problems
- Hard-to-debug errors

## Solution: IdService

We've implemented a centralized `IdService` to standardize IDs across the entire application. This service provides methods for:

1. **Standardizing IDs** to consistent formats
2. **Converting** between application and database ID formats
3. **Validating** ID formats
4. **Generating** new IDs
5. **Intelligent handling** of UUID vs semantic IDs

## Core ID Format Principles

1. **Prefixing**: All IDs include a prefix indicating the entity type.
2. **Separation**: Underscores (`_`) are used to separate the prefix from the unique identifier.
3. **Consistency**: The format for each entity type is strictly enforced across the entire system.
4. **Readability**: IDs are designed to be human-readable while maintaining uniqueness.

## Standard ID Formats

| Entity Type | Internal ID Format | Database ID Format | Example Internal | Example Database |
|-------------|-------------------|-------------------|-----------------|-----------------|
| Theme | `theme_<uuid>` | `<uuid>` | `theme_123e4567-e89b-12d3-a456-************` | `123e4567-e89b-12d3-a456-************` |
| Story | `story_<id>` | `<id>` | `story_21` (for One Piece) | `21` |
| Mapping | `mapping_<uuid>` | `<uuid>` | `mapping_123e4567-e89b-12d3-a456-************` | `123e4567-e89b-12d3-a456-************` |
| ThemeRelationship | `rel_<source_id>_<type>_<target_id>` or source-type-target format | N/A | `theme_adventure-PARENT_OF-theme_journey` | N/A |

## Entity-Specific ID Details

### Theme IDs

Themes use a UUID-based approach to ensure global uniqueness:

```
theme_<uuid>
```

**Example**: `theme_123e4567-e89b-12d3-a456-************`

**Usage Guidelines**:
- When creating new themes, always generate a proper UUID
- Do not reuse theme IDs, even for similar themes
- Deprecated themes maintain their original ID but get marked with `deprecated=true`
- Semantic IDs like `theme_adventure` may also be used for human readability

### Story IDs

Stories typically use their source database ID:

```
story_<id>
```

**Example**: `story_21` (for One Piece)

**Usage Guidelines**:
- When importing from external sources, maintain original IDs when possible
- For stories without external IDs, generate a sequential internal ID
- Always include source metadata to track the story's origin

### Mapping IDs

Mappings between themes and stories use UUID-based IDs:

```
mapping_<uuid>
```

**Example**: `mapping_123e4567-e89b-12d3-a456-************`

**Usage Guidelines**:
- Generate a new UUID for each mapping
- Do not encode relationship details in the mapping ID itself
- Use mapping properties to store relationship metadata

### Theme Relationship IDs

Theme relationships use a composite ID structure:

```
source_id-relationship_type-target_id
```

**Example**: `theme_adventure-PARENT_OF-theme_journey`

**Usage Guidelines**:
- The relationship type should be uppercase and match valid RelationshipType values
- Source and target IDs should be valid theme IDs
- When querying, always validate all three components

## Usage Guide

### Basic ID Standardization

```python
from app.services.id_service import IdService

# Standardize IDs to internal format
theme_id = IdService.standardize_id("adventure", "theme")  # Returns "theme_adventure"
story_id = IdService.standardize_id("21", "story")  # Returns "story_21"

# Convert to database format
db_theme_id = IdService.to_database_id("theme_adventure", "theme")  # Returns "adventure"
db_story_id = IdService.to_database_id("story_21", "story")  # Returns "21"
```

### Smart ID Detection

```python
# Determine if an ID is a UUID or semantic ID
is_uuid = IdService.is_uuid("7fc75255-6e7f-400f-a74c-2ec78668aaf3")  # Returns True
is_semantic = IdService.is_semantic_id("adventure")  # Returns True

# Get query parameters based on ID type
query_params = IdService.find_by_id_or_name("adventure", "theme")
# Returns {"param_name": "name", "param_value": "adventure", "condition": "..."}
```

### Cache Keys

```python
# Generate standardized cache keys
cache_key = IdService.get_cache_key("adventure", "theme")  # Returns "theme:theme_adventure"
stats_key = IdService.get_cache_key("21", "story", "stats")  # Returns "story:story_21:stats"
```

### Relationship IDs

```python
# Create standardized relationship ID
rel_id = IdService.create_relationship_id("adventure", "PARENT_OF", "journey")
# Returns "theme_adventure-PARENT_OF-theme_journey"

# Parse relationship ID
source_id, rel_type, target_id = IdService.parse_relationship_id(rel_id)
# Returns ("theme_adventure", "PARENT_OF", "theme_journey")
```

## ID Generation and Validation

### Generation

The system provides utility functions for ID generation through the IdService:

```python
# Example ID generation code
theme_id = IdService.generate_id("theme")  # Returns "theme_<uuid>"
mapping_id = IdService.generate_id("mapping")  # Returns "mapping_<uuid>"
relationship_id = IdService.create_relationship_id(source_id, relationship_type, target_id)
```

### Validation

The IdService provides validation methods:

```python
# Validate IDs
is_valid_theme = IdService.validate_id("theme_123e4567-e89b-12d3-a456-************", "theme")
is_valid_story = IdService.validate_id("story_21", "story")
```

Additionally, the `check_id_standardization.py` script in the `backend/scripts` directory can be used to scan the codebase for potential ID standardization issues.

Regular expressions used for validation:

```python
THEME_ID_PATTERN = re.compile(r'["\'](theme_[a-zA-Z0-9\-_]+)["\']')
STORY_ID_PATTERN = re.compile(r'["\'](story_[a-zA-Z0-9\-_]+)["\']')
MAPPING_ID_PATTERN = re.compile(r'["\'](mapping_[a-zA-Z0-9\-_]+)["\']')
RELATIONSHIP_ID_PATTERN = re.compile(r'["\'](rel_[a-zA-Z0-9\-_]+_[A-Z_]+_[a-zA-Z0-9\-_]+)["\']')
```

## Best Practices

1. **Always use IdService**: Use the IdService for all ID operations to ensure consistency
2. **Standardize at system boundaries**: Apply standardization at API, database, and cache boundaries
3. **Never hardcode IDs**: Always use ID generation functions
4. **Validate IDs**: When accepting external IDs, validate them against the expected format
5. **Error handling**: Provide clear error messages for malformed IDs
6. **Handle UUID and semantic IDs differently**: Use appropriate methods for each ID type
7. **Log ID standardization issues**: Enable debugging through consistent logging
8. **Documentation**: When creating new entity types, update this document with their ID format
9. **Cross-references**: When referencing entities across systems, always use the full standardized ID

## Implementation Status

The IdService has been integrated with:

- Theme relationship service
- GraphQL resolvers for theme relationships
- *[other components to be added as implementation progresses]*

## Extending to New Entity Types

To add support for a new entity type:

1. Add constants and utilities to `id_standardization.py`
2. Add the entity type configuration to `IdService.ENTITY_TYPES`
3. Create test cases for the new entity type
4. Update this documentation with the new entity type details

## Troubleshooting

Common ID-related issues:

1. **Invalid entity type prefix**: Ensure the prefix matches the entity type
2. **Missing separators**: Check that underscores are used to separate components
3. **Malformed UUIDs**: Verify that UUIDs follow the standard format
4. **Capitalization**: Relationship types should be uppercase in IDs
5. **ID format mismatch**: Check logs for ID standardization warnings
6. **Inconsistent ID usage**: Verify that IdService is being used consistently
7. **Special characters**: Check for special characters or formatting issues in IDs
8. **Wrong entity type**: Ensure entity types are correct in IdService calls

## Future Considerations

As the system grows, we may need to standardize IDs for additional entity types:
- User IDs
- Agent IDs
- Analysis IDs
- Pattern IDs

When introducing new entity types, follow these guidelines for creating ID standards:
1. Choose a descriptive prefix
2. Determine uniqueness requirements (UUID vs. sequential)
3. Document the format in this guide
4. Update validation scripts
5. Create utility functions for generation and validation

## Contact

For questions or issues with ID standardization, contact the platform team. 