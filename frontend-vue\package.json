{"name": "ta<PERSON><PERSON>-frontend-vue", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vue-tsc && vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "oxlint .", "lint:fix": "oxlint . --fix", "format": "oxlint . --fix"}, "dependencies": {"axios": "^1.6.2", "d3": "^7.9.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "@vue/test-utils": "^2.4.3", "autoprefixer": "^10.4.16", "jsdom": "^23.0.1", "oxlint": "^1.5.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^5.0.8", "vitest": "^1.1.0", "vue-tsc": "^3.0.1"}}