{"name": "ta<PERSON><PERSON>-frontend-vue", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vue-tsc && vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.6.2", "d3": "^7.9.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.12.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8", "vitest": "^1.1.0", "vue-tsc": "^1.8.25"}}