"""
Formatting utilities for GraphQL resolvers.
"""
from typing import Dict, Any
from datetime import datetime

def format_theme(theme_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format theme data for GraphQL response.
    
    Args:
        theme_data: Raw theme data from the database
        
    Returns:
        Formatted theme data matching the GraphQL schema
    """
    # Ensure category is properly set - if it's missing or empty, use UNCATEGORIZED
    category = theme_data.get("category")
    if not category:
        category = "UNCATEGORIZED"
    
    return {
        "id": theme_data["id"],
        "name": theme_data["name"],
        "description": theme_data.get("description", ""),
        "parentThemeId": theme_data.get("parent_theme_id"),
        "category": category,  # Use the properly handled category
        "subCategory": theme_data.get("sub_category"),
        "dimensions": theme_data.get("dimensions", []),
        "confidence": theme_data.get("confidence", 1.0),
        "createdAt": theme_data.get("created_at", datetime.now().isoformat()),
        "updatedAt": theme_data.get("updated_at", datetime.now().isoformat()),
        "status": theme_data.get("status", "MAPPED"),
        "version": theme_data.get("version"),
        "deprecated": theme_data.get("deprecated", False),
        "replacedBy": theme_data.get("replaced_by"),
        "culturalContext": theme_data.get("cultural_context", []),
        "evolutionStage": theme_data.get("evolution_stage"),
        "implicitTags": theme_data.get("implicit_tags", [])
    }

def format_theme_mapping(mapping_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format theme mapping data for GraphQL response.
    
    Args:
        mapping_data: Raw mapping data from the database
        
    Returns:
        Formatted mapping data matching the GraphQL schema
    """
    source = mapping_data["source"]
    theme_data = mapping_data["theme"]
    relationship = mapping_data["relationship"]
    
    return {
        "id": f"{source['id']}_{theme_data['id']}",  # Generate a unique ID for the mapping
        "sourceType": source.get("label", "Unknown"),
        "sourceId": source["id"],
        "themeId": theme_data["id"],
        "theme": format_theme(theme_data),
        "mappingStrength": relationship.get("mapping_strength", 1.0),
        "mappingType": relationship.get("mapping_type", "PRIMARY").upper(),
        "context": relationship.get("notes"),
        "createdAt": relationship.get("created_at", datetime.now().isoformat()),
        "updatedAt": relationship.get("updated_at", datetime.now().isoformat()),
        "llmConfidence": relationship.get("llm_confidence"),
        "needsReview": relationship.get("needs_review", False)
    }
