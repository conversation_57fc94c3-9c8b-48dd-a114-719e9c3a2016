#!/usr/bin/env python
"""
ID Standards Verification Script.

This script scans the codebase to identify places where IDs might not be properly standardized,
helping to ensure consistent ID handling throughout the application.

Usage:
    python verify_id_standards.py [--directory PATH] [--fix]

Options:
    --directory PATH     Directory to scan (default: app)
    --fix                Create automatic fixes where possible (default: False, only report)
"""

import os
import re
import sys
import argparse
from typing import Dict, List, Tuple, Set
import asyncio

# Add the parent directory to sys.path to import app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

try:
    from app.services.id_service import IdService
except ImportError:
    print("Error: Cannot import IdService. Make sure you're running this script from the backend directory.")
    sys.exit(1)


# Patterns to detect potential ID standardization issues
PATTERN_DICT = {
    # Direct ID assignments
    "direct_assignment": [
        r'id\s*=\s*["\']([^"\']*)["\']',                     # id = "value"
        r'([a-zA-Z_]+_id)\s*=\s*["\']([^"\']*)["\']',        # theme_id = "value"
        r'(?:"|\'|\()([a-zA-Z_]+)_([0-9a-f\-]+)(?:"|\'|\))', # "theme_uuid" or "story_12345"
    ],
    
    # ID handling in queries
    "queries": [
        r'WHERE\s+(\w+\.[a-zA-Z_]+)\s*=\s*[\$\@\:](\w+)',   # WHERE id = $id
        r'(?:MATCH|CREATE)\s*\([a-zA-Z]+:(\w+)\s*\{\s*id\s*:\s*[\$\@\:](\w+)',  # MATCH (t:Theme {id: $id})
    ],
    
    # Cache key generation
    "cache_keys": [
        r'([a-zA-Z_]+):([^:"\'\s]+)',                        # "theme:adventure"
        r'build_(\w+)_cache_key',                            # build_theme_cache_key
    ],
    
    # Frontend normalization
    "frontend": [
        r'\.id\.startsWith\(["\'](\w+)_',                    # id.startsWith("theme_")
        r'`(\w+)_\$\{([^}]+)\}`',                            # `theme_${id}`
        r'(\w+)Id\s*=\s*.*\?.id\s*:\s*`(\w+)_\$\{',          # themeId = theme?.id : `theme_${
    ]
}

# Entity types to check
ENTITY_TYPES = ["theme", "story", "mapping", "relationship"]


class IdStandardsChecker:
    """Checks for ID standardization issues in code files."""
    
    def __init__(self, directory="app", fix=False):
        """Initialize with target directory and fix flag."""
        self.directory = directory
        self.fix = fix
        self.issues = []
        self.checked_files = 0
        self.skipped_files = 0
        self.entity_patterns = {
            entity_type: re.compile(f"{entity_type}_([a-zA-Z0-9_-]+)", re.IGNORECASE)
            for entity_type in ENTITY_TYPES
        }
        
    async def scan_codebase(self):
        """Scan the codebase for ID standardization issues."""
        print(f"Scanning directory: {self.directory}")
        
        for root, _, files in os.walk(self.directory):
            for file in files:
                file_path = os.path.join(root, file)
                
                # Skip non-source files
                if file.endswith((".py", ".ts", ".tsx", ".js", ".jsx")):
                    await self.check_file(file_path)
                else:
                    self.skipped_files += 1
        
        print(f"\nScan complete! Checked {self.checked_files} files, skipped {self.skipped_files} files.")
        print(f"Found {len(self.issues)} potential ID standardization issues.")
        
        if self.issues:
            self.report_issues()
            
            if self.fix:
                await self.fix_issues()
    
    async def check_file(self, file_path):
        """Check a single file for ID standardization issues."""
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                content = f.read()
                self.checked_files += 1
            except UnicodeDecodeError:
                self.skipped_files += 1
                return
        
        # Skip files that already import and use IdService properly
        if "IdService.standardize_id" in content and "from app.services.id_service import IdService" in content:
            # Quick check to see if the file uses IdService consistently
            # Count occurrences of pattern-matching ID operations vs IdService calls
            raw_id_ops = len(re.findall(r'([a-zA-Z_]+)_id\s*=\s*["\']([^"\']*)["\']', content))
            id_service_calls = len(re.findall(r'IdService\.standardize_id', content))
            
            # If there are more IdService calls than raw operations, likely properly using IdService
            if id_service_calls >= raw_id_ops:
                return
        
        # Check for patterns
        for category, patterns in PATTERN_DICT.items():
            for pattern in patterns:
                for match in re.finditer(pattern, content):
                    # Extract matched groups
                    if len(match.groups()) == 1:
                        # Single group - likely direct ID assignment
                        potential_id = match.group(1)
                        context = content[max(0, match.start() - 40):match.end() + 40]
                        
                        if self._is_id_value(potential_id):
                            self.issues.append({
                                "file": file_path,
                                "line": self._get_line_number(content, match.start()),
                                "pattern": pattern,
                                "match": match.group(0),
                                "id_value": potential_id,
                                "category": category,
                                "context": context
                            })
                    elif len(match.groups()) == 2:
                        # Two groups - likely id variable and value
                        var_name = match.group(1)
                        value = match.group(2)
                        context = content[max(0, match.start() - 40):match.end() + 40]
                        
                        if self._is_id_variable(var_name) and self._is_id_value(value):
                            entity_type = self._detect_entity_type(var_name, value)
                            
                            self.issues.append({
                                "file": file_path,
                                "line": self._get_line_number(content, match.start()),
                                "pattern": pattern,
                                "match": match.group(0),
                                "id_variable": var_name,
                                "id_value": value,
                                "entity_type": entity_type,
                                "category": category,
                                "context": context
                            })
    
    def _is_id_variable(self, var_name):
        """Check if a variable name is likely to be an ID variable."""
        id_var_patterns = [
            r'^id$',
            r'^[a-zA-Z]+_id$',
            r'^[a-zA-Z]+Id$',
            r'^source_id$',
            r'^target_id$',
            r'^relationship_id$'
        ]
        
        return any(re.match(pattern, var_name) for pattern in id_var_patterns)
    
    def _is_id_value(self, value):
        """Check if a value is likely to be an ID."""
        if not value or len(value) < 2:
            return False
            
        # Check for known entity prefixes
        for entity_type in ENTITY_TYPES:
            if value.startswith(f"{entity_type}_"):
                return True
        
        # Check for numeric IDs (common for story IDs)
        if value.isdigit():
            return True
            
        # Check for UUID-like strings
        if re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', value):
            return True
            
        # Check for semantic IDs (lowercase with underscores)
        if re.match(r'^[a-z][a-z0-9_]+$', value):
            return True
            
        return False
    
    def _detect_entity_type(self, var_name, value):
        """Detect entity type from variable name or value."""
        # First check the variable name
        if "theme" in var_name.lower():
            return "theme"
        elif "story" in var_name.lower():
            return "story"
        elif "mapping" in var_name.lower():
            return "mapping"
        elif "relationship" in var_name.lower():
            return "relationship"
            
        # Then check the value
        for entity_type in ENTITY_TYPES:
            if value.startswith(f"{entity_type}_"):
                return entity_type
        
        # Default to "theme" as it's the most common
        return "theme"
    
    def _get_line_number(self, content, position):
        """Get line number for a position in the content."""
        return content[:position].count('\n') + 1
    
    def report_issues(self):
        """Report found issues."""
        print("\nID Standardization Issues:")
        print("=========================\n")
        
        for i, issue in enumerate(self.issues):
            print(f"Issue #{i+1}:")
            print(f"  File: {issue['file']}")
            print(f"  Line: {issue['line']}")
            print(f"  Category: {issue['category']}")
            
            if 'id_variable' in issue:
                print(f"  ID Variable: {issue['id_variable']}")
            
            print(f"  ID Value: {issue['id_value']}")
            
            if 'entity_type' in issue:
                print(f"  Entity Type: {issue['entity_type']}")
            
            print(f"  Context: {issue['context'].strip()}")
            print(f"  Suggested Fix: Use IdService.standardize_id(\"{issue['id_value']}\", \"{issue.get('entity_type', 'theme')}\")")
            print()
    
    async def fix_issues(self):
        """Apply fixes for common issues."""
        if not self.fix:
            return
            
        print("\nApplying fixes...")
        
        # Group issues by file
        issues_by_file = {}
        for issue in self.issues:
            file_path = issue['file']
            if file_path not in issues_by_file:
                issues_by_file[file_path] = []
            issues_by_file[file_path].append(issue)
        
        # Process each file
        for file_path, file_issues in issues_by_file.items():
            await self._fix_file(file_path, file_issues)
    
    async def _fix_file(self, file_path, issues):
        """Apply fixes to a single file."""
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # First check if IdService is imported
        has_import = "from app.services.id_service import IdService" in content
        
        # Add import if needed
        if not has_import and file_path.endswith(".py"):
            import_pos = content.find("import ")
            if import_pos >= 0:
                # Find the last import statement
                import_lines = re.findall(r'^.*import.*$', content, re.MULTILINE)
                if import_lines:
                    last_import = import_lines[-1]
                    last_import_pos = content.rfind(last_import) + len(last_import)
                    content = (
                        content[:last_import_pos] + 
                        "\nfrom app.services.id_service import IdService" +
                        content[last_import_pos:]
                    )
            
        # Apply fixes for each issue
        for issue in issues:
            if issue['category'] == 'direct_assignment' and 'id_value' in issue:
                # Replace direct ID assignment with IdService call
                entity_type = issue.get('entity_type', 'theme')
                old_code = issue['match']
                id_value = issue['id_value']
                
                # Handle different patterns
                if '=' in old_code:
                    # This is an assignment
                    var_part = old_code.split('=')[0].strip()
                    new_code = f"{var_part} = IdService.standardize_id(\"{id_value}\", \"{entity_type}\")"
                    content = content.replace(old_code, new_code)
        
        # Write changes back to file
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        print(f"Fixed {len(issues)} issues in {file_path}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="ID Standards Verification Script")
    parser.add_argument("--directory", default="app", help="Directory to scan")
    parser.add_argument("--fix", action="store_true", help="Create automatic fixes where possible")
    
    args = parser.parse_args()
    
    checker = IdStandardsChecker(args.directory, args.fix)
    await checker.scan_codebase()


if __name__ == "__main__":
    asyncio.run(main()) 