"""
Media Query Service for handling media data retrieval and recommendations.

This service abstracts and encapsulates operations related to media entities (anime, books, movies, etc.)
including fetching details, formatting responses, and generating recommendations.
It provides a clean interface for resolver functions to work with Neo4j operations
without needing to directly construct Cypher queries.
"""
from typing import Dict, List, Any, Optional, Union
import logging
from datetime import datetime
import neo4j
from neo4j import AsyncSession

from app.crud.neo4j import story
from app.core.logging import get_logger
from app.graphql.resolvers.utils.id_standardization import (
    ensure_story_id_prefix,
    remove_story_id_prefix
)
from .anilist import AniListService

logger = get_logger(__name__)

class MediaQueryService:
    """
    Service for querying and formatting media entities.
    
    This service provides methods for:
    1. Fetching media details from the database
    2. Formatting media data for GraphQL responses
    3. Finding similar media based on themes
    4. Cross-media recommendations
    5. Accessing external APIs for additional data
    
    It abstracts the complexity of Neo4j operations and provides a clean interface
    for resolver functions.
    """
    
    def __init__(self):
        self.anilist_service = AniListService()
    
    async def get_media(
        self, 
        session: AsyncSession, 
        media_id: str, 
        media_type: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get media details by ID.
        
        Args:
            session: Neo4j session
            media_id: Media ID (with or without prefix)
            media_type: Optional media type for filtering
            
        Returns:
            Formatted media data or None if not found
        """
        # Ensure ID has proper prefix
        media_id = ensure_story_id_prefix(media_id)
        
        # Use Neo4j CRUD operation to get the story
        media_data = await story.get(session, media_id)
        
        if not media_data:
            logger.warning(f"Media not found: {media_id}")
            return None
        
        # If media_type is provided, verify it matches
        if media_type and media_data.get("type", "").upper() != media_type.upper():
            logger.warning(f"Media type mismatch: expected {media_type}, got {media_data.get('type')}")
            return None
        
        # Format the data based on media type
        formatter = self._get_formatter_for_type(media_data.get("type", "UNKNOWN"))
        return formatter(media_data)
    
    def _get_formatter_for_type(self, media_type: str) -> callable:
        """
        Get the appropriate formatter function for a media type.
        
        Args:
            media_type: Type of media (ANIME, BOOK, MOVIE, etc.)
            
        Returns:
            Formatter function for the media type
        """
        formatters = {
            "ANIME": self._format_anime,
            "BOOK": self._format_book,
            "MOVIE": self._format_movie,
            # Add more formatters as needed
        }
        
        # Default to a generic formatter if type not found
        return formatters.get(media_type.upper(), self._format_generic)
    
    def _format_anime(self, media_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format anime data for GraphQL response."""
        return {
            "id": media_data["id"],
            "title": media_data.get("title", ""),
            "description": media_data.get("description", ""),
            "genres": media_data.get("genres", []),
            "tags": media_data.get("tags", []),
            "coverImage": media_data.get("cover_image", ""),
            "format": media_data.get("format", "TV"),
            "episodes": media_data.get("episodes", 0),
            "duration": media_data.get("duration", 0),
            "status": media_data.get("status", "FINISHED"),
            "startDate": media_data.get("start_date", ""),
            "endDate": media_data.get("end_date", ""),
            "season": media_data.get("season", ""),
            "seasonYear": media_data.get("season_year", 0),
            "source": media_data.get("source", "ORIGINAL"),
            "averageScore": media_data.get("average_score", 0),
            "popularity": media_data.get("popularity", 0),
            "anilistId": media_data.get("anilist_id", ""),
            "externalLinks": media_data.get("external_links", [])
        }
    
    def _format_book(self, media_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format book data for GraphQL response."""
        return {
            "id": media_data["id"],
            "title": media_data.get("title", ""),
            "description": media_data.get("description", ""),
            "genres": media_data.get("genres", []),
            "tags": media_data.get("tags", []),
            "coverImage": media_data.get("cover_image", ""),
            "author": media_data.get("author", ""),
            "publisher": media_data.get("publisher", ""),
            "publishDate": media_data.get("publish_date", ""),
            "pages": media_data.get("pages", 0),
            "status": media_data.get("status", "PUBLISHED"),
            "averageScore": media_data.get("average_score", 0),
            "popularity": media_data.get("popularity", 0),
            "externalLinks": media_data.get("external_links", [])
        }
    
    def _format_movie(self, media_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format movie data for GraphQL response."""
        return {
            "id": media_data["id"],
            "title": media_data.get("title", ""),
            "description": media_data.get("description", ""),
            "genres": media_data.get("genres", []),
            "tags": media_data.get("tags", []),
            "coverImage": media_data.get("cover_image", ""),
            "director": media_data.get("director", ""),
            "studio": media_data.get("studio", ""),
            "releaseDate": media_data.get("release_date", ""),
            "runtime": media_data.get("runtime", 0),
            "status": media_data.get("status", "RELEASED"),
            "averageScore": media_data.get("average_score", 0),
            "popularity": media_data.get("popularity", 0),
            "externalLinks": media_data.get("external_links", [])
        }
    
    def _format_generic(self, media_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format generic media data for GraphQL response."""
        return {
            "id": media_data["id"],
            "title": media_data.get("title", ""),
            "description": media_data.get("description", ""),
            "mediaType": media_data.get("type", "UNKNOWN"),
            "genres": media_data.get("genres", []),
            "tags": media_data.get("tags", []),
            "coverImage": media_data.get("cover_image", ""),
            "status": media_data.get("status", ""),
            "releaseDate": media_data.get("release_date", "") or media_data.get("start_date", ""),
            "averageScore": media_data.get("average_score", 0),
            "popularity": media_data.get("popularity", 0),
            "externalLinks": media_data.get("external_links", [])
        }
    
    async def find_similar_media(
        self, 
        session: AsyncSession, 
        source_id: str, 
        source_type: Optional[str] = None,
        target_type: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find media similar to the source based on shared themes.
        
        Args:
            session: Neo4j session
            source_id: Source media ID
            source_type: Optional source media type
            target_type: Optional target media type filter
            limit: Maximum number of results
            
        Returns:
            List of similar media items
        """
        # Ensure ID has proper prefix
        source_id = ensure_story_id_prefix(source_id)
        source_id_clean = remove_story_id_prefix(source_id)
        
        # Build type filter
        type_filter = ""
        if target_type:
            type_filter = f"AND similar.type = '{target_type}'"
        elif source_type:
            type_filter = f"AND similar.type = '{source_type}'"
        
        # Create query for finding similar media
        query = f"""
        MATCH (source:Story {{id: $source_id}})-[:HAS_THEME]->(theme:Theme)<-[:HAS_THEME]-(similar:Story)
        WHERE similar.id <> $source_id {type_filter}
        WITH similar, count(theme) as sharedThemes, collect(theme.name) as sharedThemeNames
        ORDER BY sharedThemes DESC
        LIMIT $limit
        RETURN similar, sharedThemes, sharedThemeNames
        """
        
        try:
            # Execute query
            result = await session.run(query, {"source_id": source_id_clean, "limit": limit})
            records = await result.fetch(limit)
            
            # Process results
            similar_items = []
            for record in records:
                media_data = dict(record["similar"])
                shared_themes_count = record["sharedThemes"]
                shared_theme_names = record["sharedThemeNames"]
                
                # Format based on media type
                media_type = media_data.get("type", "UNKNOWN")
                formatter = self._get_formatter_for_type(media_type)
                formatted_media = formatter(media_data)
                
                # Add similarity information
                formatted_media["similarityScore"] = shared_themes_count
                formatted_media["sharedThemes"] = shared_theme_names
                
                similar_items.append(formatted_media)
            
            return similar_items
        except Exception as e:
            logger.error(f"Error finding similar media: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    
    async def recommend_crossmedia(
        self, 
        session: AsyncSession, 
        source_id: str, 
        source_type: str,
        target_types: Optional[List[str]] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find cross-media recommendations based on shared themes.
        
        Args:
            session: Neo4j session
            source_id: Source media ID
            source_type: Source media type
            target_types: Optional list of target media types
            limit: Maximum number of results
            
        Returns:
            List of recommendations from different media types
        """
        # Ensure ID has proper prefix
        source_id = ensure_story_id_prefix(source_id)
        source_id_clean = remove_story_id_prefix(source_id)
        
        # Create type filter string
        type_filter = ""
        if target_types and len(target_types) > 0:
            type_list = [f"'{t}'" for t in target_types]
            type_filter = f"AND similar.type IN [{', '.join(type_list)}]"
        else:
            # Exclude the same type as source
            type_filter = f"AND similar.type <> '{source_type}'"
        
        # Create query for finding cross-media recommendations
        query = f"""
        MATCH (source:Story {{id: $source_id}})-[:HAS_THEME]->(theme:Theme)<-[:HAS_THEME]-(similar:Story)
        WHERE similar.id <> $source_id {type_filter}
        WITH similar, similar.type as mediaType, count(theme) as sharedThemes, 
             collect(theme.name) as sharedThemeNames
        ORDER BY sharedThemes DESC
        LIMIT $limit
        RETURN similar, mediaType, sharedThemes, sharedThemeNames
        """
        
        try:
            # Execute query
            result = await session.run(query, {"source_id": source_id_clean, "limit": limit})
            records = await result.fetch(limit)
            
            # Process results
            recommendations = []
            for record in records:
                media_data = dict(record["similar"])
                media_type = record["mediaType"]
                shared_themes_count = record["sharedThemes"]
                shared_theme_names = record["sharedThemeNames"]
                
                # Format based on media type
                formatter = self._get_formatter_for_type(media_type)
                formatted_media = formatter(media_data)
                
                # Add recommendation information
                formatted_media["mediaType"] = media_type
                formatted_media["similarityScore"] = shared_themes_count
                formatted_media["sharedThemes"] = shared_theme_names
                
                recommendations.append(formatted_media)
            
            return recommendations
        except Exception as e:
            logger.error(f"Error finding cross-media recommendations: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    
    async def fetch_external_data(
        self, 
        media_id: str, 
        media_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Fetch data from external API based on media type.
        
        Args:
            media_id: External ID (e.g., AniList ID)
            media_type: Media type (ANIME, BOOK, MOVIE)
            
        Returns:
            Media data from external source or None if not found
        """
        if media_type.upper() == "ANIME":
            # Use AniList service for anime
            try:
                anime_data = await self.anilist_service.get_anime_details(media_id)
                story_data = await self.anilist_service.transform_to_story(anime_data)
                return story_data
            except Exception as e:
                logger.error(f"Error fetching anime data from AniList: {str(e)}")
                return None
        else:
            # TODO: Implement other external data sources
            logger.warning(f"External data fetch not implemented for media type: {media_type}")
            return None 