import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Union
import copy

import neo4j
from fastapi import APIRouter, Depends, HTTPException, Query, status, Body
from pydantic import ValidationError
from neo4j import AsyncSession
import json
from enum import Enum

from app.db.neo4j_session import get_db_session, get_write_session
from app.services.anilist import AniListService
from app.services.dependencies import get_anilist_service
from app.schemas.story import (
    Story, 
    StoryCreate, 
    StoryUpdate, 
    StorySearch, 
    StorySearchInput,
    StorySearchResults, 
    StoryId, 
    RecommendationResponse, 
    RecommendationFilters,
    StoryRecommendation,
    StorySearchResult
)
from app.core.logging import get_logger
from app.core.cache import CacheManager
from app.core.redis import RedisConnection
from app.services.id_service import IdService

# Import Neo4j CRUD modules
from app.crud.neo4j.story import CRUDStory

logger = get_logger("stories_endpoint")

# Initialize cache managers with reasonable TTLs
def build_story_cache_key(key: str) -> str:
    """Build a cache key with the story prefix."""
    standard_id = IdService.standardize_id(key, "story")
    return IdService.get_cache_key(standard_id, "story")

def build_search_cache_key(query: str, page: int, per_page: int) -> str:
    """Build a cache key with the search prefix."""
    return f"search:{query}:{page}:{per_page}"

async def cache_story_data(cache_manager, cache_key, story_data):
    """
    Helper to cache story data with proper JSON serialization.
    Ensures consistent handling of all nested JSON structures.
    """
    try:
        # Deep copy to avoid modifying the original
        data_to_cache = copy.deepcopy(story_data)
        
        # Convert Pydantic model to dict if needed
        if hasattr(data_to_cache, "dict"):
            data_to_cache = data_to_cache.dict()
            
        # Ensure story_metadata is JSON serializable
        if "story_metadata" in data_to_cache and isinstance(data_to_cache["story_metadata"], dict):
            # Ensure nested structures in metadata are also serializable
            for field, value in data_to_cache["story_metadata"].items():
                # Handle any complex objects that might not be directly serializable
                if not isinstance(value, (str, int, float, bool, type(None))) and not isinstance(value, (list, dict)):
                    data_to_cache["story_metadata"][field] = str(value)
        
        # Cache the serialized data
        await cache_manager.set(cache_key, json.dumps(data_to_cache))
        logger.debug(f"Successfully cached data for key: {cache_key}")
    except Exception as e:
        logger.error(f"Error caching story data: {e}")

# Initialize cache managers for stories and search
story_cache = CacheManager()
search_cache = CacheManager()

# Initialize CRUD operations
story_crud = CRUDStory()

def parse_datetime(date_string: Optional[str]) -> Optional[datetime]:
    """
    Parse a date string to a datetime object using various formats.
    
    Args:
        date_string: Date string to parse
        
    Returns:
        Datetime object if parsing is successful, None otherwise
    """
    if not date_string:
        return None
        
    # Try different date formats
    formats_to_try = [
        # With replace to handle 'Z' timezone marker
        lambda s: datetime.fromisoformat(s.replace('Z', '+00:00')),
        # ISO format with timezone
        lambda s: datetime.fromisoformat(s),
        # Common datetime format
        lambda s: datetime.strptime(s, "%Y-%m-%dT%H:%M:%S"),
        # Date only format
        lambda s: datetime.strptime(s, "%Y-%m-%d"),
    ]
    
    for format_parser in formats_to_try:
        try:
            dt = format_parser(date_string)
            # Ensure the datetime has timezone info
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt
        except (ValueError, TypeError):
            continue
            
    # If all parsing attempts fail, log warning and return None
    logger.warning(f"Unable to parse date string: {date_string}")
    return None

# Helper function to convert Neo4j datetime objects to Python datetime objects
def convert_neo4j_datetime(data):
    """Convert Neo4j datetime objects to Python datetime objects and deserialize JSON strings."""
    if data is None:
        return None
        
    if hasattr(data, 'year') and hasattr(data, 'month') and hasattr(data, 'day'):
        # Convert Neo4j DateTime directly to Python datetime
        return datetime(
            year=getattr(data, 'year', 1970),
            month=getattr(data, 'month', 1),
            day=getattr(data, 'day', 1),
            hour=getattr(data, 'hour', 0),
            minute=getattr(data, 'minute', 0),
            second=getattr(data, 'second', 0),
            tzinfo=timezone.utc if getattr(data, 'tzinfo', None) else timezone.utc  # Always use UTC
        )
    
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            # Deserialize JSON strings, particularly for story_metadata
            if key == "story_metadata" and isinstance(value, str):
                try:
                    # Attempt to parse the JSON string
                    metadata_dict = json.loads(value)
                    
                    # Handle nested JSON strings within the metadata
                    for metadata_key, metadata_value in metadata_dict.items():
                        if isinstance(metadata_value, str):
                            try:
                                if metadata_value.startswith('[') or metadata_value.startswith('{'):
                                    parsed_value = json.loads(metadata_value)
                                    metadata_dict[metadata_key] = parsed_value
                            except json.JSONDecodeError:
                                # Keep as string if it's not valid JSON
                                pass
                    
                    result[key] = metadata_dict
                except json.JSONDecodeError as e:
                    # Log the error for debugging
                    logger.warning(f"Error parsing story_metadata JSON: {e}, raw value: {value[:100]}...")
                    result[key] = {} # Use empty dict to avoid validation errors
            elif key in ["created_at", "updated_at"] and isinstance(value, str):
                # Parse date strings to datetime objects with timezone
                dt = parse_datetime(value)
                if dt and dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                result[key] = dt or value
            else:
                result[key] = convert_neo4j_datetime(value)
        return result
    
    if isinstance(data, list):
        return [convert_neo4j_datetime(item) for item in data]
    
    # Try to deserialize any JSON string
    if isinstance(data, str) and (data.startswith('{') or data.startswith('[')):
        try:
            return json.loads(data)
        except json.JSONDecodeError:
            # If it's not valid JSON, keep the original string
            pass
    
    # Try to parse date strings
    if isinstance(data, str) and ('T' in data or '-' in data):
        parsed_date = parse_datetime(data)
        if parsed_date:
            # Ensure parsed date has timezone info
            if parsed_date.tzinfo is None:
                parsed_date = parsed_date.replace(tzinfo=timezone.utc)
            return parsed_date
    
    # If it's already a datetime, ensure it has timezone info
    if isinstance(data, datetime) and data.tzinfo is None:
        return data.replace(tzinfo=timezone.utc)
    
    return data

def is_data_stale(updated_at: Optional[Union[str, datetime]], days: int = 7) -> bool:
    """
    Check if data is stale based on the updated_at timestamp.
    
    Args:
        updated_at: Timestamp when data was last updated
        days: Number of days after which data is considered stale
        
    Returns:
        True if data is stale, False otherwise
    """
    if not updated_at:
        return True
        
    # Parse string to datetime if needed
    if isinstance(updated_at, str):
        updated_at = parse_datetime(updated_at)
        
    # If parsing failed, consider it stale
    if not updated_at:
        return True
    
    # Get current time with timezone info
    now = datetime.now(timezone.utc)
    
    # Ensure updated_at has timezone info
    if updated_at.tzinfo is None:
        # If updated_at doesn't have timezone info, assume UTC
        try:
            updated_at = updated_at.replace(tzinfo=timezone.utc)
        except Exception as e:
            logger.warning(f"Error adding timezone to date: {e}")
            return True
    
    # Now both datetimes have timezone info, safe to compare
    try:
        return (now - updated_at) > timedelta(days=days)
    except Exception as e:
        logger.warning(f"Error comparing dates: {e}")
        return True

# Dependency to get story cache
def get_story_cache() -> CacheManager:
    """Get story cache manager."""
    return story_cache

# Dependency to get search cache
def get_search_cache() -> CacheManager:
    """Get search cache manager."""
    return search_cache

"""
REST API endpoints for story operations.
Note: For theme-related operations, please use the GraphQL API endpoint at /api/v1/graphql.
The GraphQL API provides comprehensive theme analysis, mapping, and management functionality.
"""
router = APIRouter(
    tags=["stories"],
    responses={404: {"description": "Story not found"}},
)

# Simple cache decorator
async def get_cached_or_execute(cache_manager: CacheManager, key: str, func, *args, **kwargs):
    """Get result from cache or execute function and cache result."""
    try:
        # Early validation of Redis connection
        redis_available = await RedisConnection.ping()
        
        if not redis_available:
            logger.warning(f"Redis not available when trying to use cache for key: {key}. Executing function directly.")
            # Execute function directly as fallback
            return await func(*args, **kwargs)
        
        cached = await cache_manager.get(key)
        if cached is not None:
            logger.debug(f"Cache hit for key: {key}")
            return cached
        
        logger.debug(f"Cache miss for key: {key}, executing function")
        result = await func(*args, **kwargs)
        
        # Try to cache the result, but don't fail if caching fails
        try:
            await cache_manager.set(key, result)
            logger.debug(f"Cached result for key: {key}")
        except Exception as e:
            logger.error(f"Failed to cache result for key: {key}: {e}")
        
        return result
    except Exception as e:
        logger.error(f"Error in get_cached_or_execute for key {key}: {e}")
        # Fallback to direct execution if anything fails
        return await func(*args, **kwargs)

@router.get("/search", response_model=StorySearchResults,
    summary="Search for stories/anime",
    description="""
    Search for stories/anime by title or other criteria.
    
    The endpoint first checks the cache for results. If not found or expired,
    it performs a search using the AniList API.
    
    Results are paginated with configurable page size.
    """
)
async def search_stories_get(
    query: str = Query(..., description="Search query string"),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    per_page: int = Query(10, ge=1, le=50, description="Number of results per page"),
    db: AsyncSession = Depends(get_write_session),
    search_cache: CacheManager = Depends(get_search_cache),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> StorySearchResults:
    """Search for stories/anime by title or other criteria."""
    
    logger.info(f"GET search for stories with query: {query}, page: {page}, per_page: {per_page}")
    
    # Explicitly check for empty query and return 400
    if not query or query.strip() == "":
        raise HTTPException(
            status_code=400,
            detail="Search query cannot be empty"
        )
    
    # Check for short queries (less than 2 characters) and return 422
    if len(query.strip()) < 2:
        raise HTTPException(
            status_code=422,
            detail="Search query must be at least 2 characters long"
        )
    
    # Handle case where Depends objects are passed directly in tests
    if hasattr(search_cache, "__call__") and not isinstance(search_cache, CacheManager):
        search_cache = get_search_cache()
    
    # Try to get cached search results first
    cache_key = build_search_cache_key(query, page, per_page)
    cached_result = await search_cache.get(cache_key)
    
    if cached_result:
        try:
            # Parse the cached JSON string into a StorySearchResults object
            return StorySearchResults.model_validate_json(cached_result)
        except ValidationError:
            # If there's an error parsing the cached result, log it and continue with a fresh search
            logger.warning(f"Error parsing cached search results for {query}. Fetching fresh results.")
    
    try:
        logger.info(f"Searching for '{query}', page {page}, per_page {per_page}")
        
        # Search AniList
        anilist_results = await anilist_service.search_anime(
            query=query,
            page=page,
            per_page=per_page
        )
        
        # Process results
        media_items = anilist_results.get("data", {}).get("Page", {}).get("media", [])
        total = anilist_results.get("data", {}).get("Page", {}).get("pageInfo", {}).get("total", 0)
        
        logger.debug(f"Found {len(media_items)} stories matching '{query}'")
        
        # Transform and store results
        stories = []
        
        for media in media_items:
            try:
                # Transform the media item
                story_data = await anilist_service.transform_media_to_story_data(media)
                
                # Ensure ID is in the correct format
                story_id = f"story_{media.get('id', '')}"
                story_data["id"] = story_id
                
                # Use a separate transaction for each story
                try:
                    # Save to database using CRUD operations
                    db_story = await story_crud.create_or_update(db, obj_in=story_data)
                    
                    # Process the database record to convert Neo4j datetime objects and deserialize JSON strings
                    processed_story = convert_neo4j_datetime(db_story)
                    
                    stories.append(Story(**processed_story))
                except neo4j.exceptions.ClientError as e:
                    # Handle Neo4j transaction errors specifically
                    if "terminated" in str(e):
                        logger.warning(f"Neo4j transaction was terminated. Creating a minimal story object for {story_id}")
                        # Create a minimal story object from the AniList data without saving to DB
                        minimal_story = Story(
                            id=story_id,
                            external_id=str(media.get('id', '')),
                            title_english=media.get("title", {}).get("english"),
                            title_romaji=media.get("title", {}).get("romaji"),
                            title_native=media.get("title", {}).get("native"),
                            media_type=media.get("format"),
                            cover_image_large=media.get("coverImage", {}).get("large"),
                            average_score=media.get("averageScore")
                        )
                        stories.append(minimal_story)
                    else:
                        # Re-raise other Neo4j errors
                        raise
                
            except Exception as e:
                logger.error(f"Error processing search result: {str(e)}")
                continue
        
        # Create response
        result = StorySearchResults(total=total, items=stories)
        
        # Cache the result - serialize the Pydantic model to JSON string first
        try:
            # Convert Pydantic model to JSON string
            result_json = result.model_dump_json()
            await search_cache.set(cache_key, result_json)
            logger.debug(f"Cached search results for query: {query}")
        except Exception as e:
            logger.error(f"Error caching search results: {str(e)}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error searching stories: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error searching stories: {str(e)}"
        )

@router.get("/{story_id}", response_model=Story, 
    summary="Get a specific story by ID",
    description="""
    Retrieve detailed information about a specific story/anime by its ID.
    
    The endpoint first checks the cache for data. If not found or expired,
    it checks the Neo4j database. If the data is not found or is stale,
    it fetches fresh data from the AniList API.
    """
)
async def get_story(
    story_id: str,
    db: AsyncSession = Depends(get_write_session),
    story_cache: CacheManager = Depends(get_story_cache),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> Story:
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(story_id, "story")
    
    # Check cache using standardized ID
    cache_key = build_story_cache_key(standard_id)
    cached_data = await story_cache.get(cache_key)
    
    if cached_data:
        logger.debug(f"Cache hit for story: {standard_id}")
        if isinstance(cached_data, str):
            cached_data = json.loads(cached_data)
        
        # Parse story_metadata if it's a string
        if isinstance(cached_data.get("story_metadata"), str):
            try:
                cached_data["story_metadata"] = json.loads(cached_data["story_metadata"])
                logger.debug(f"Successfully parsed cached story_metadata for {standard_id}")
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse story_metadata for {standard_id}, removing field")
                cached_data.pop("story_metadata", None)
        
        # Check nested JSON strings in metadata fields
        if isinstance(cached_data.get("story_metadata"), dict):
            for field in ["genres", "tags", "studios", "relations", "recommendations"]:
                metadata_field = cached_data["story_metadata"].get(field)
                if isinstance(metadata_field, str):
                    try:
                        if metadata_field.startswith('[') or metadata_field.startswith('{'):
                            cached_data["story_metadata"][field] = json.loads(metadata_field)
                            logger.debug(f"Parsed nested JSON in story_metadata.{field}")
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse nested JSON in story_metadata.{field}")
                        # Set to default empty value based on field type
                        cached_data["story_metadata"][field] = [] if field not in ["relations", "recommendations"] else {}
        
        try:
            return Story(**cached_data)
        except ValidationError as e:
            logger.error(f"Error validating cached data for {standard_id}: {e}")
            
            # Try to repair the cache
            logger.warning(f"Attempting to repair cached data for {standard_id}")
            fixed = await fix_cached_story_data(standard_id, story_cache)
            
            if fixed:
                # Try again with fixed data
                logger.info(f"Cache repaired for {standard_id}, trying again")
                repaired_data = await story_cache.get(cache_key)
                if repaired_data:
                    if isinstance(repaired_data, str):
                        repaired_data = json.loads(repaired_data)
                    try:
                        return Story(**repaired_data)
                    except ValidationError:
                        # If it still fails, continue to database lookup
                        logger.error(f"Still failed to validate cached data after repair for {standard_id}")
            else:
                logger.warning(f"Could not repair cache for {standard_id}, invalidating cache")
                await story_cache.delete(cache_key)
    
    # Not in cache, check database using standardized ID
    story_crud = CRUDStory()
    
    # First try with standardized ID (with prefix)
    logger.debug(f"Trying to fetch story with standardized ID: {standard_id}")
    db_story = await story_crud.get(db, id=standard_id)
    
    # If not found, try with database ID (without prefix)
    if not db_story:
        db_id = IdService.to_database_id(standard_id, "story")
        logger.debug(f"Story not found with standardized ID, trying database ID: {db_id}")
        db_story = await story_crud.get(db, id=db_id)
        
    # If still not found, try with external_id
    if not db_story:
        logger.debug(f"Story not found with any ID format, trying external_id: {story_id}")
        db_story = await story_crud.get_by_external_id(db, external_id=story_id)
        
        # Also try with standardized external_id
        if not db_story:
            logger.debug(f"Story not found with raw external_id, trying standardized external_id: {standard_id}")
            db_story = await story_crud.get_by_external_id(db, external_id=standard_id)
    
    if db_story:
        logger.debug(f"Retrieved story {standard_id} from database")
        
        # Convert to dict for caching and returning
        story_data = db_story if isinstance(db_story, dict) else db_story.dict()
        
        # Ensure the ID is standardized in the response
        story_data["id"] = standard_id
        
        # Ensure story_metadata is a dictionary, not a string
        if "story_metadata" in story_data and isinstance(story_data["story_metadata"], str):
            try:
                story_data["story_metadata"] = json.loads(story_data["story_metadata"])
                logger.debug("Successfully parsed story_metadata JSON string to dictionary")
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse story_metadata as JSON for {standard_id}, removing invalid field")
                # If we can't parse it, remove it to avoid validation errors
                story_data.pop("story_metadata", None)
        
        # Check if the data is stale (older than 7 days)
        last_updated = story_data.get("updated_at")
        is_stale = is_data_stale(last_updated)
        
        if is_stale:
            logger.debug(f"Story {standard_id} data is stale, refreshing from AniList")
            try:
                # Use the numeric part of the ID for AniList API
                anilist_id = IdService.to_database_id(standard_id, "story")
                
                # Fetch fresh data from AniList
                anilist_data = await anilist_service.get_anime_details(anilist_id)
                if anilist_data:
                    # Transform the AniList data to our Story format
                    fresh_data = await anilist_service.transform_to_story(anilist_data)
                    
                    # Update with fresh data but preserve our ID format
                    story_data.update(fresh_data)
                    story_data["id"] = standard_id
                    story_data["updated_at"] = datetime.now(timezone.utc)
                    
                    # Save back to database
                    await story_crud.update(db, db_obj=db_story, obj_in=story_data)
                    logger.debug(f"Saved/Updated story {standard_id} in database")
            except Exception as e:
                logger.error(f"Error refreshing story {standard_id} from AniList: {e}")
        
        # Cache the data (whether refreshed or not)
        await cache_story_data(story_cache, cache_key, story_data)
        
        return Story(**story_data)
    
    # Not in database, try to fetch from AniList
    logger.debug(f"Story {standard_id} not found in database, checking AniList")
    
    try:
        # For AniList lookup, we need the numeric ID
        anilist_id = IdService.to_database_id(standard_id, "story")
        
        # Try to get story details from AniList
        anilist_data = await anilist_service.get_anime_details(anilist_id)
        
        if not anilist_data:
            logger.warning(f"Story {standard_id} not found in AniList")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Story with ID {standard_id} not found"
            )
            
        # Transform AniList data to our format
        story_data = await anilist_service.transform_to_story(anilist_data)
        
        # Process any date fields
        story_data = convert_neo4j_datetime(story_data)
        
        # Ensure story_metadata is properly formatted
        if "story_metadata" in story_data:
            if isinstance(story_data["story_metadata"], str):
                try:
                    story_data["story_metadata"] = json.loads(story_data["story_metadata"])
                except json.JSONDecodeError:
                    logger.warning(f"Error parsing story_metadata from AniList for {standard_id}")
                    story_data["story_metadata"] = {}
        else:
            # Create metadata field if missing
            story_data["story_metadata"] = {}
        
        # Add our internal ID 
        story_data["id"] = standard_id
        story_data["external_id"] = anilist_id
        
        # Add creation timestamp
        story_data["created_at"] = datetime.now(timezone.utc)
        story_data["updated_at"] = datetime.now(timezone.utc)
        
        # Try to save to database
        try:
            logger.debug(f"Saving story {standard_id} to database")
            story_crud = CRUDStory()
            await story_crud.create(db, obj_in=story_data)
        except Exception as e:
            logger.error(f"Error saving story {standard_id} to database: {str(e)}")
        
        # Cache the data
        await cache_story_data(story_cache, cache_key, story_data)
        
        return Story(**story_data)
    except Exception as e:
        logger.error(f"Error fetching story {standard_id} from AniList: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Story with ID {standard_id} not found. Error: {str(e)}"
        )

@router.post("/search", response_model=StorySearchResults,
    summary="Search for stories/anime via POST",
    description="""
    Search for stories/anime by title or other criteria using POST method.
    
    This endpoint is functionally identical to the GET endpoint but supports
    longer search queries via POST body.
    
    The endpoint first checks the cache for results. If not found or expired,
    it performs a search using the AniList API.
    
    Results are paginated with configurable page size.
    """
)
async def search_stories_post(
    search_query: Union[StorySearchInput, Dict[str, Any]] = Body(...),
    db: AsyncSession = Depends(get_write_session),
    search_cache: CacheManager = Depends(get_search_cache),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> StorySearchResults:
    """Search for stories/anime by title or other criteria using POST method."""
    
    # Handle both old format (direct query string) and new format (StorySearchInput)
    query = ""
    page = 1
    per_page = 10
    media_type = "all"
    sort = "popularity"
    
    if isinstance(search_query, dict):
        # Old format - query might be directly in the dict
        if "query" in search_query:
            query = search_query["query"]
        # Extract pagination if present
        page = search_query.get("page", 1)
        per_page = search_query.get("per_page", 10)
        # Extract filters if present
        media_type = search_query.get("mediaType", "all")
        sort = search_query.get("sort", "popularity")
    else:
        # New format - using StorySearchInput
        query = search_query.query
        page = search_query.page
        per_page = search_query.per_page
        media_type = search_query.mediaType
        sort = search_query.sort
    
    logger.info(f"POST search for stories with query: {query}, page: {page}, per_page: {per_page}, media_type: {media_type}, sort: {sort}")
    
    # Explicitly check for empty query and return 400
    if not query or query.strip() == "":
        raise HTTPException(
            status_code=400,
            detail="Search query cannot be empty"
        )
    
    # Check for short queries (less than 2 characters) and return 422
    if len(query.strip()) < 2:
        raise HTTPException(
            status_code=422,
            detail="Search query must be at least 2 characters long"
        )
    
    # Handle case where Depends objects are passed directly in tests
    if hasattr(search_cache, "__call__") and not isinstance(search_cache, CacheManager):
        search_cache = get_search_cache()
    
    # Try to get cached results first
    cache_key = build_search_cache_key(query, page, per_page)
    cached_results = await search_cache.get(cache_key)
    
    if cached_results:
        logger.debug(f"Cache hit for search query: {query}")
        try:
            # Parse cached JSON results
            if isinstance(cached_results, str):
                cached_data = json.loads(cached_results)
                
                # Handle nested story_metadata if present
                if "items" in cached_data:
                    for item in cached_data.get("items", []):
                        if isinstance(item.get("story_metadata"), str):
                            try:
                                item["story_metadata"] = json.loads(item["story_metadata"])
                            except json.JSONDecodeError:
                                logger.warning(f"Failed to parse story_metadata in search results")
                                item.pop("story_metadata", None)
                
                return StorySearchResults(**cached_data)
        except (json.JSONDecodeError, ValidationError) as e:
            logger.error(f"Error parsing cached search results: {e}")
    
    try:
        logger.info(f"Searching for '{query}', page {page}, per_page {per_page}")
        
        # Search AniList
        anilist_results = await anilist_service.search_anime(
            query=query,
            page=page,
            per_page=per_page,
            media_type=media_type,
            sort=sort
        )
        
        # Process results
        media_items = anilist_results.get("data", {}).get("Page", {}).get("media", [])
        total = anilist_results.get("data", {}).get("Page", {}).get("pageInfo", {}).get("total", 0)
        
        logger.debug(f"Found {len(media_items)} stories matching '{query}'")
        
        # Transform and store results
        stories = []
        
        # Use a new session for database operations to prevent transaction conflicts
        for media in media_items:
            try:
                # Transform the media item
                story_data = await anilist_service.transform_media_to_story_data(media)
                
                # Ensure ID is in the correct format
                story_id = f"story_{media.get('id', '')}"
                story_data["id"] = story_id
                
                # Use a separate transaction for each story
                try:
                    # Save to database using CRUD operations
                    db_story = await story_crud.create_or_update(db, obj_in=story_data)
                    
                    # Process the database record to convert Neo4j datetime objects and deserialize JSON strings
                    processed_story = convert_neo4j_datetime(db_story)
                    
                    stories.append(Story(**processed_story))
                except neo4j.exceptions.ClientError as e:
                    # Handle Neo4j transaction errors specifically
                    if "terminated" in str(e):
                        logger.warning(f"Neo4j transaction was terminated. Creating a minimal story object for {story_id}")
                        # Create a minimal story object from the AniList data without saving to DB
                        minimal_story = Story(
                            id=story_id,
                            external_id=str(media.get('id', '')),
                            title_english=media.get("title", {}).get("english"),
                            title_romaji=media.get("title", {}).get("romaji"),
                            title_native=media.get("title", {}).get("native"),
                            media_type=media.get("format"),
                            cover_image_large=media.get("coverImage", {}).get("large"),
                            average_score=media.get("averageScore")
                        )
                        stories.append(minimal_story)
                    else:
                        # Re-raise other Neo4j errors
                        raise
                
            except Exception as e:
                logger.error(f"Error processing search result: {str(e)}")
                continue
        
        # Create response
        result = StorySearchResults(total=total, items=stories)
        
        # Cache the result - serialize the Pydantic model to JSON string first
        try:
            # Convert Pydantic model to JSON string
            result_json = result.model_dump_json()
            await search_cache.set(cache_key, result_json)
            logger.debug(f"Cached search results for query: {query}")
        except Exception as e:
            logger.error(f"Error caching search results: {str(e)}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error searching stories: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error searching stories: {str(e)}"
        )

async def get_neo4j_recommendations(db, story_id, genres=None, min_score=0, limit=10, status=None):
    """
    Get story recommendations from Neo4j graph.
    
    Args:
        db: Database session
        story_id: Story ID to get recommendations for
        genres: List of genres to filter by
        min_score: Minimum score threshold
        limit: Maximum number of recommendations to return
        status: Filter by status
        
    Returns:
        List of story recommendation objects
    """
    try:
        # Standardize and prepare ID for database query
        standard_id = IdService.standardize_id(story_id, "story")
        db_id = IdService.to_database_id(standard_id, "story")
        
        logger.debug(f"Getting Neo4j recommendations for story {standard_id} (db_id: {db_id})")
        
        # Check if we need to try both with and without prefix
        # We'll try both because the database may have inconsistent ID formats
        ids_to_try = [db_id]
        if not db_id.startswith("story_"):
            ids_to_try.append(f"story_{db_id}")
        
        recommendations = []
        
        # Try each ID format
        for id_to_try in ids_to_try:
            logger.debug(f"Trying to find recommendations with story ID: {id_to_try}")
            
            # Build query with optional filters
            query = """
            MATCH (s:Story {id: $id})-[:HAS_THEME]->(t:Theme)<-[:HAS_THEME]-(rec:Story)
            WHERE rec.id <> $id
            """
            
            params = {"id": id_to_try, "min_score": min_score, "limit": limit}
            
            # Add genre filter if specified
            if genres and len(genres) > 0:
                genre_list = [genre.strip() for genre in genres if genre.strip()]
                if genre_list:
                    genre_clause = " AND rec.genres CONTAINS $genre " * len(genre_list)
                    query += genre_clause
                    for i, genre in enumerate(genre_list):
                        params[f"genre{i}"] = genre
            
            # Add minimum score filter if specified
            if min_score > 0:
                query += " AND rec.average_score >= $min_score"
                
            # Add status filter if specified
            if status:
                query += " AND rec.status = $status"
                params["status"] = status
            
            # Add scoring and aggregation
            query += """
            WITH rec, count(DISTINCT t) as shared_themes
            ORDER BY shared_themes DESC, rec.average_score DESC
            LIMIT $limit
            RETURN rec, shared_themes
            """
            
            result = await db.run(query, params)
            records = await result.data()
            
            if records:
                logger.debug(f"Found {len(records)} recommendations using ID format: {id_to_try}")
                
                for record in records:
                    story_data = record.get("rec", {})
                    story_data = convert_neo4j_datetime(story_data)
                    
                    # Ensure ID is standardized
                    rec_id = story_data.get("id")
                    standard_rec_id = IdService.standardize_id(rec_id, "story")
                    
                    # Handle nested story_metadata if it exists
                    if "story_metadata" in story_data and isinstance(story_data["story_metadata"], str):
                        try:
                            story_data["story_metadata"] = json.loads(story_data["story_metadata"])
                        except (json.JSONDecodeError, TypeError):
                            logger.warning(f"Failed to parse story_metadata for recommendation {standard_rec_id}")
                            story_data["story_metadata"] = {}
                    
                    # Create recommendation object with minimum required fields
                    recommendation = {
                        "id": standard_rec_id,
                        "external_id": story_data.get("external_id", standard_rec_id),
                        "title_romaji": story_data.get("title_romaji", ""),
                        "title_english": story_data.get("title_english", ""),
                        "title_native": story_data.get("title_native", ""),
                        "media_type": story_data.get("media_type", ""),
                        "average_score": story_data.get("average_score", 0),
                        "popularity": story_data.get("popularity", 0),
                        "status": story_data.get("status", ""),
                        "genres": story_data.get("genres", []),
                        "cover_image_large": story_data.get("cover_image_large", ""),
                        "cover_image_medium": story_data.get("cover_image_medium", ""),
                        "banner_image": story_data.get("banner_image", ""),
                        "shared_theme_count": record.get("shared_themes", 0),
                        "from_id": standard_id
                    }
                    
                    recommendations.append(recommendation)
                
                # If we found recommendations, no need to try other ID formats
                if recommendations:
                    break
            else:
                logger.debug(f"No recommendations found using ID format: {id_to_try}")
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error getting Neo4j recommendations: {str(e)}", exc_info=True)
        return []

@router.get("/{story_id}/recommendations", response_model=RecommendationResponse,
    summary="Get recommendations for a story",
    description="""
    Get recommendations for a story based on graph relationships.
    
    This endpoint retrieves recommendations based on graph relationships in the Neo4j database.
    It may supplement with recommendations from the AniList API if necessary.
    
    Recommendations can be filtered by genre, format, and minimum score.
    """
)
async def get_recommendations(
    story_id: str,
    genres: List[str] = Query(None, description="Filter by genres"),
    min_score: int = Query(0, ge=0, le=100, description="Minimum average score"),
    status: str = Query(None, description="Filter by status (FINISHED, RELEASING, etc.)"),
    limit: int = Query(10, ge=1, le=25, description="Maximum number of recommendations to return"),
    db: AsyncSession = Depends(get_write_session),
    story_cache: CacheManager = Depends(get_story_cache),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> RecommendationResponse:
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(story_id, "story")
    logger.debug(f"Getting recommendations for story: {standard_id}, genres: {genres}, min_score: {min_score}, limit: {limit}")
    
    try:
        # Try to get recommendations from Neo4j first
        neo4j_recommendations = await get_neo4j_recommendations(
            db=db, 
            story_id=standard_id, 
            genres=genres, 
            min_score=min_score, 
            limit=limit,
            status=status
        )
        
        if neo4j_recommendations and len(neo4j_recommendations) > 0:
            logger.info(f"Found {len(neo4j_recommendations)} Neo4j recommendations for {standard_id}")
            return RecommendationResponse(
                recommendations=neo4j_recommendations,
                count=len(neo4j_recommendations),
                source="neo4j"
            )
        
        # If no Neo4j recommendations, try AniList
        logger.debug(f"No Neo4j recommendations found for {standard_id}, trying AniList")
        
        # Get the story details to get the AniList ID
        story = await get_story(story_id, db, story_cache, anilist_service)
        if not story or not story.external_id:
            logger.warning(f"No AniList ID found for {standard_id}, returning empty recommendations")
            return RecommendationResponse(
                recommendations=[],
                count=0,
                source="none"
            )
        
        # Use external_id as the AniList ID
        anilist_id = story.external_id
        if isinstance(anilist_id, str) and anilist_id.startswith("story_"):
            # Strip the prefix if present
            anilist_id = anilist_id.replace("story_", "")
        
        # Get recommendations from AniList
        try:
            anilist_recommendations = await anilist_service.get_recommendations(
                anilist_id, 
                limit
            )
            
            if anilist_recommendations:
                processed_recommendations = []
                # Check if anilist_recommendations is a dict with 'recommendations' key
                recommendations_list = (
                    anilist_recommendations.get('recommendations', []) 
                    if isinstance(anilist_recommendations, dict) 
                    else anilist_recommendations
                )
                
                for rec in recommendations_list:
                    # Apply filters
                    if min_score > 0 and (not rec.get("average_score") or rec.get("average_score", 0) < min_score):
                        continue
                    
                    if genres and rec.get("genres"):
                        if not any(genre in rec.get("genres", []) for genre in genres):
                            continue
                    
                    if status and rec.get("status") and rec.get("status") != status:
                        continue
                    
                    # Create a properly formatted recommendation object
                    recommendation = {
                        "id": IdService.standardize_id(rec.get("id", ""), "story"),
                        "external_id": rec.get("external_id", rec.get("id", "")),
                        "title_romaji": rec.get("title_romaji", rec.get("title", "")),
                        "title_english": rec.get("title_english", ""),
                        "title_native": rec.get("title_native", ""),
                        "media_type": rec.get("media_type", ""),
                        "average_score": rec.get("average_score", 0),
                        "popularity": rec.get("popularity", 0),
                        "status": rec.get("status", ""),
                        "genres": rec.get("genres", []),
                        "cover_image_large": rec.get("cover_image_large", rec.get("cover_image", "")),
                        "cover_image_medium": rec.get("cover_image_medium", ""),
                        "banner_image": rec.get("banner_image", ""),
                        "from_id": standard_id
                    }
                    
                    processed_recommendations.append(recommendation)
                
                logger.info(f"Found {len(processed_recommendations)} AniList recommendations for {standard_id}")
                return RecommendationResponse(
                    recommendations=processed_recommendations,
                    count=len(processed_recommendations),
                    source="anilist"
                )
            
        except Exception as e:
            logger.error(f"Error getting recommendations from AniList: {str(e)}")
        
        # If all else fails, return empty list
        return RecommendationResponse(
            recommendations=[],
            count=0,
            source="none"
        )
        
    except Exception as e:
        logger.error(f"Error getting recommendations: {str(e)}", exc_info=True)
        # Always return a valid response even if there's an error
        return RecommendationResponse(
            recommendations=[],
            count=0,
            source="error",
            error=str(e)
        )

@router.get("/{story_id}/metadata", response_model=Dict[str, Any],
    summary="Get metadata for a specific story",
    description="""
    Retrieve metadata information about a specific story/anime by its ID.
    
    This endpoint returns additional metadata about the story, including genres, tags, studios,
    and other related information.
    """
)
async def get_story_metadata(
    story_id: str,
    db: AsyncSession = Depends(get_write_session),
    story_cache: CacheManager = Depends(get_story_cache),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> Dict[str, Any]:
    """Get metadata for a specific story."""
    logger.info(f"Getting metadata for story ID: {story_id}")
    
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(story_id, "story")
    
    # Build cache key with standardized ID
    cache_key = build_story_cache_key(f"{standard_id}:metadata")
    
    # Try to get from cache first
    cached_metadata = await story_cache.get(cache_key)
    if cached_metadata:
        logger.debug(f"Cache hit for story metadata: {standard_id}")
        if isinstance(cached_metadata, str):
            cached_metadata = json.loads(cached_metadata)
        return cached_metadata
    
    # Not in cache, get from database or API
    try:
        # First, get the story to ensure it exists
        # This will handle all ID standardization internally
        story = await get_story(story_id, db, story_cache, anilist_service)
        
        # Process any date fields
        story_dict = story.dict()
        story_dict = convert_neo4j_datetime(story_dict)
        
        # Extract metadata from story
        metadata = {}
        
        # Extract genres
        if hasattr(story, "story_metadata") and story.story_metadata:
            story_metadata_dict = {}
            
            # Handle story_metadata field which could be a dict, string, or StoryMetadata object
            if isinstance(story.story_metadata, dict):
                story_metadata_dict = story.story_metadata
            elif isinstance(story.story_metadata, str):
                try:
                    story_metadata_dict = json.loads(story.story_metadata)
                except json.JSONDecodeError:
                    logger.error(f"Error decoding story metadata JSON: {story.story_metadata}")
            else:
                # Handle case where it might be a Pydantic model
                try:
                    story_metadata_dict = story.story_metadata.dict()
                except (AttributeError, TypeError):
                    logger.error(f"Unable to extract story metadata: {type(story.story_metadata)}")
            
            # Extract fields from metadata
            metadata["genres"] = story_metadata_dict.get("genres", [])
            metadata["tags"] = story_metadata_dict.get("tags", [])
            metadata["studios"] = story_metadata_dict.get("studios", [])
            metadata["relations"] = story_metadata_dict.get("relations", [])
            metadata["recommendations"] = story_metadata_dict.get("recommendations", [])
            
            # Ensure all fields contain proper data structures
            for field in ["genres", "tags", "studios", "relations", "recommendations"]:
                # Convert any JSON strings to Python objects
                if isinstance(metadata[field], str):
                    try:
                        if metadata[field].startswith('[') or metadata[field].startswith('{'):
                            metadata[field] = json.loads(metadata[field])
                    except json.JSONDecodeError:
                        # If parsing fails, set to empty list/dict
                        metadata[field] = [] if field not in ["relations", "recommendations"] else {}
                
                # Ensure arrays for array fields
                if field in ["genres", "tags", "studios"] and not isinstance(metadata[field], list):
                    metadata[field] = []
        
        # Add basic metadata
        metadata["id"] = story.id
        metadata["external_id"] = story.external_id
        metadata["title_english"] = story.title_english
        metadata["title_romaji"] = story.title_romaji
        metadata["title_native"] = story.title_native
        metadata["media_type"] = story.media_type
        metadata["status"] = story.status
        metadata["average_score"] = story.average_score
        metadata["popularity"] = story.popularity
        
        # Cache the metadata
        await cache_story_data(story_cache, cache_key, metadata)
        
        return metadata
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting story metadata: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error getting story metadata: {str(e)}"
        )

async def fix_cached_story_data(story_id: str, story_cache: CacheManager) -> bool:
    """
    Attempts to fix invalid story data in the cache.
    
    Args:
        story_id: The ID of the story to fix
        story_cache: The cache manager instance
        
    Returns:
        True if successfully fixed, False otherwise
    """
    try:
        # Get standardized ID and cache key
        standard_id = IdService.standardize_id(story_id, "story")
        cache_key = build_story_cache_key(standard_id)
        
        # Fetch from cache
        cached_data = await story_cache.get(cache_key)
        if not cached_data:
            return False
            
        # Parse data
        if isinstance(cached_data, str):
            try:
                cached_data = json.loads(cached_data)
            except json.JSONDecodeError:
                logger.error(f"Cannot fix cache for {standard_id}: Invalid JSON")
                return False
                
        # Fix story_metadata if it's a string
        fixed = False
        if "story_metadata" in cached_data and isinstance(cached_data["story_metadata"], str):
            try:
                cached_data["story_metadata"] = json.loads(cached_data["story_metadata"])
                fixed = True
                logger.info(f"Fixed story_metadata for {standard_id}")
            except json.JSONDecodeError:
                # If parsing fails, set to empty dict
                cached_data["story_metadata"] = {}
                fixed = True
                logger.warning(f"Reset invalid story_metadata for {standard_id}")
                
        # Fix nested fields in metadata
        if "story_metadata" in cached_data and isinstance(cached_data["story_metadata"], dict):
            for field in ["genres", "tags", "studios", "relations", "recommendations"]:
                if field in cached_data["story_metadata"] and isinstance(cached_data["story_metadata"][field], str):
                    try:
                        if cached_data["story_metadata"][field].startswith('[') or cached_data["story_metadata"][field].startswith('{'):
                            cached_data["story_metadata"][field] = json.loads(cached_data["story_metadata"][field])
                            fixed = True
                            logger.info(f"Fixed nested {field} in story_metadata for {standard_id}")
                    except json.JSONDecodeError:
                        # Reset to default empty value
                        cached_data["story_metadata"][field] = [] if field not in ["relations", "recommendations"] else {}
                        fixed = True
                        logger.warning(f"Reset invalid {field} in story_metadata for {standard_id}")
                        
        # If we made any fixes, update the cache
        if fixed:
            await cache_story_data(story_cache, cache_key, cached_data)
            return True
            
        return False
    except Exception as e:
        logger.error(f"Error fixing cached story data: {e}")
        return False

@router.post("/{story_id}/repair-cache", response_model=Dict[str, Any],
    summary="Repair story cache",
    description="""
    Repair cached data for a specific story.
    
    This endpoint attempts to fix invalid story data in the cache.
    It's useful for addressing validation errors without having to
    completely invalidate the cache.
    """
)
async def repair_story_cache(
    story_id: str,
    story_cache: CacheManager = Depends(get_story_cache)
) -> Dict[str, Any]:
    """Repair cached data for a specific story."""
    logger.info(f"Repairing cache for story ID: {story_id}")
    
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(story_id, "story")
    
    # Try to repair the cache
    fixed = await fix_cached_story_data(standard_id, story_cache)
    
    if fixed:
        return {
            "success": True,
            "message": f"Successfully repaired cache for story {standard_id}",
            "story_id": standard_id
        }
    else:
        # If repair failed, invalidate the cache
        cache_key = build_story_cache_key(standard_id)
        await story_cache.delete(cache_key)
        
        return {
            "success": False,
            "message": f"Could not repair cache for story {standard_id}, cache invalidated",
            "story_id": standard_id
        }

@router.get("/debug/neo4j-id-analysis", response_model=Dict[str, Any],
    summary="Analyze ID formats in Neo4j",
    description="""
    Debug endpoint to analyze the ID formats in the Neo4j database.
    
    This endpoint checks for inconsistencies in how IDs are stored
    in the Neo4j database, particularly focusing on story IDs.
    """
)
async def analyze_neo4j_ids(
    limit: int = Query(100, ge=1, le=1000, description="Number of stories to analyze"),
    db: AsyncSession = Depends(get_write_session),
) -> Dict[str, Any]:
    """Analyze ID formats in Neo4j database."""
    try:
        # Check Story nodes
        query = """
        MATCH (s:Story)
        RETURN s.id AS id, s.external_id AS external_id
        LIMIT $limit
        """
        
        result = await db.run(query, {"limit": limit})
        records = await result.data()
        
        # Analyze ID formats
        id_formats = {
            "with_prefix": 0,
            "without_prefix": 0,
            "non_numeric": 0,
            "external_id_with_prefix": 0,
            "external_id_without_prefix": 0,
            "external_id_missing": 0,
            "inconsistent_format": 0
        }
        
        inconsistent_examples = []
        
        for record in records:
            story_id = record.get("id")
            external_id = record.get("external_id")
            
            # Check ID format
            if story_id:
                if story_id.startswith("story_"):
                    id_formats["with_prefix"] += 1
                    # Extract numeric part
                    numeric_part = story_id.replace("story_", "")
                    if not numeric_part.isdigit():
                        id_formats["non_numeric"] += 1
                else:
                    id_formats["without_prefix"] += 1
            
            # Check external_id format
            if external_id:
                if external_id.startswith("story_"):
                    id_formats["external_id_with_prefix"] += 1
                else:
                    id_formats["external_id_without_prefix"] += 1
                    
                # Check for inconsistency between id and external_id
                if story_id and external_id:
                    story_id_stripped = story_id.replace("story_", "")
                    external_id_stripped = external_id.replace("story_", "")
                    
                    if story_id_stripped != external_id_stripped:
                        id_formats["inconsistent_format"] += 1
                        if len(inconsistent_examples) < 10:
                            inconsistent_examples.append({
                                "id": story_id,
                                "external_id": external_id
                            })
            else:
                id_formats["external_id_missing"] += 1
        
        return {
            "total_analyzed": len(records),
            "id_formats": id_formats,
            "inconsistent_examples": inconsistent_examples
        }
        
    except Exception as e:
        logger.error(f"Error analyzing Neo4j IDs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing Neo4j IDs: {str(e)}"
        )

@router.post("/debug/standardize-neo4j-ids", response_model=Dict[str, Any],
    summary="Standardize IDs in Neo4j",
    description="""
    Debug endpoint to standardize ID formats in the Neo4j database.
    
    This endpoint updates the Neo4j database to ensure consistent ID formats,
    particularly focusing on story IDs. It can fix issues with prefixes and
    ensure external_id fields are properly set.
    
    WARNING: This is a potentially destructive operation that modifies the database.
    """
)
async def standardize_neo4j_ids(
    dry_run: bool = Query(True, description="If true, only show what would be changed without making changes"),
    limit: int = Query(100, ge=1, le=5000, description="Number of stories to process"),
    fix_double_prefixes: bool = Query(False, description="If true, also fix IDs with double prefixes (story_story_XXX)"),
    conflict_suffix: str = Query("_dup", description="Suffix to add to duplicate IDs to resolve conflicts"),
    db: AsyncSession = Depends(get_write_session),
) -> Dict[str, Any]:
    """Standardize ID formats in Neo4j database."""
    try:
        # First analyze current state
        analysis_result = await analyze_neo4j_ids(limit=min(100, limit), db=db)
        
        # Initialize counters
        updates = {
            "add_prefix_to_id": 0,
            "remove_prefix_from_id": 0,
            "fix_double_prefixed_id": 0,
            "add_prefix_to_external_id": 0,
            "remove_prefix_from_external_id": 0,
            "set_missing_external_id": 0,
            "updated_inconsistent_ids": 0,
            "resolved_conflicts": 0
        }
        
        examples = {
            "remove_prefix_from_id": [],
            "fix_double_prefixed_id": [],
            "add_prefix_to_external_id": [],
            "set_missing_external_id": [],
            "resolved_conflicts": []
        }
        
        # A safer, more direct approach to standardize IDs
        
        # Step 1: Get all story IDs that need processing
        query = """
        MATCH (s:Story)
        WHERE s.id STARTS WITH 'story_' OR s.external_id IS NULL OR 
              (s.external_id IS NOT NULL AND NOT s.external_id STARTS WITH 'story_')
        RETURN s.id as id, s.external_id as external_id
        LIMIT $limit
        """
        result = await db.run(query, {"limit": limit})
        records = await result.data()
        
        logger.info(f"Found {len(records)} stories to process")
        
        # Process each record individually
        if not dry_run:
            # Track how many of each type are processed
            double_prefix_fixed = 0
            prefix_removed = 0
            external_id_added = 0
            external_id_prefix_added = 0
            conflicts_resolved = 0
            
            # Set to track IDs we've already seen (to detect conflicts)
            seen_ids = set()
            
            # First pass: Get all existing IDs to check for conflicts
            query = """
            MATCH (s:Story)
            RETURN s.id as id
            """
            result = await db.run(query)
            existing_records = await result.data()
            
            for record in existing_records:
                seen_ids.add(record.get("id"))
            
            logger.info(f"Loaded {len(seen_ids)} existing IDs for conflict checking")
            
            # Now process each record
            for record in records:
                old_id = record.get("id")
                external_id = record.get("external_id")
                
                # 1. Handle double-prefixed IDs first
                if fix_double_prefixes and old_id and old_id.startswith("story_story_"):
                    # This is a double-prefixed ID
                    new_id = old_id[12:]  # Remove both prefixes
                    
                    # Check for conflicts
                    if new_id in seen_ids:
                        # Conflict - add suffix
                        new_id = f"{new_id}{conflict_suffix}"
                        conflicts_resolved += 1
                        
                        # Add to examples
                        if len(examples["resolved_conflicts"]) < 5:
                            examples["resolved_conflicts"].append({
                                "original_id": old_id,
                                "unprefixed_id": old_id[12:],
                                "existing_id": old_id[12:],
                                "new_id": new_id,
                                "resolution": f"{old_id} becomes {new_id}"
                            })
                    
                    # Update ID in database
                    update_query = """
                    MATCH (s:Story {id: $old_id})
                    SET s.id = $new_id
                    """
                    await db.run(update_query, {"old_id": old_id, "new_id": new_id})
                    
                    # Update tracking
                    seen_ids.add(new_id)
                    double_prefix_fixed += 1
                    
                    # Add to examples
                    if len(examples["fix_double_prefixed_id"]) < 5:
                        examples["fix_double_prefixed_id"].append({
                            "old_id": old_id,
                            "new_id": new_id
                        })
                    
                    # Set old_id to new_id for subsequent processing
                    old_id = new_id
                
                # 2. Handle regular prefixed IDs
                elif old_id and old_id.startswith("story_") and not old_id.startswith("story_story_"):
                    # Regular prefixed ID
                    new_id = old_id[6:]  # Remove prefix
                    
                    # Check for conflicts
                    if new_id in seen_ids:
                        # Conflict - add suffix
                        new_id = f"{new_id}{conflict_suffix}"
                        conflicts_resolved += 1
                        
                        # Add to examples
                        if len(examples["resolved_conflicts"]) < 5:
                            examples["resolved_conflicts"].append({
                                "original_id": old_id,
                                "unprefixed_id": old_id[6:],
                                "existing_id": old_id[6:],
                                "new_id": new_id,
                                "resolution": f"{old_id} becomes {new_id}"
                            })
                    
                    # Update ID in database
                    update_query = """
                    MATCH (s:Story {id: $old_id})
                    SET s.id = $new_id
                    """
                    await db.run(update_query, {"old_id": old_id, "new_id": new_id})
                    
                    # Update tracking
                    seen_ids.add(new_id)
                    prefix_removed += 1
                    
                    # Add to examples
                    if len(examples["remove_prefix_from_id"]) < 5:
                        examples["remove_prefix_from_id"].append({
                            "old_id": old_id,
                            "new_id": new_id
                        })
                    
                    # Set old_id to new_id for subsequent processing
                    old_id = new_id
                
                # 3. Handle missing external_ids
                if external_id is None and old_id:
                    # Set external_id with prefix
                    new_external_id = f"story_{old_id}"
                    
                    update_query = """
                    MATCH (s:Story {id: $id})
                    SET s.external_id = $external_id
                    """
                    await db.run(update_query, {"id": old_id, "external_id": new_external_id})
                    
                    external_id_added += 1
                    
                    # Add to examples
                    if len(examples["set_missing_external_id"]) < 5:
                        examples["set_missing_external_id"].append({
                            "id": old_id,
                            "would_be_external_id": new_external_id
                        })
                
                # 4. Handle external_ids without prefix
                elif external_id and not external_id.startswith("story_"):
                    # Add prefix to external_id
                    new_external_id = f"story_{external_id}"
                    
                    update_query = """
                    MATCH (s:Story {id: $id})
                    SET s.external_id = $external_id
                    """
                    await db.run(update_query, {"id": old_id, "external_id": new_external_id})
                    
                    external_id_prefix_added += 1
                    
                    # Add to examples
                    if len(examples["add_prefix_to_external_id"]) < 5:
                        examples["add_prefix_to_external_id"].append({
                            "old_external_id": external_id,
                            "new_external_id": new_external_id
                        })
            
            # Update total counts
            updates["fix_double_prefixed_id"] = double_prefix_fixed
            updates["remove_prefix_from_id"] = prefix_removed
            updates["set_missing_external_id"] = external_id_added
            updates["add_prefix_to_external_id"] = external_id_prefix_added
            updates["resolved_conflicts"] = conflicts_resolved
            
            logger.info(f"Standardization complete: {double_prefix_fixed} double-prefixed IDs fixed, "
                        f"{prefix_removed} prefixes removed, {external_id_added} external IDs added, "
                        f"{external_id_prefix_added} external ID prefixes added, {conflicts_resolved} conflicts resolved")
        else:
            # In dry run mode, count potential changes for stats
            for record in records:
                id_value = record.get("id", "")
                external_id = record.get("external_id")
                
                # Count double-prefixed IDs
                if fix_double_prefixes and id_value.startswith("story_story_"):
                    updates["fix_double_prefixed_id"] += 1
                    
                    # Add to examples
                    if len(examples["fix_double_prefixed_id"]) < 5:
                        examples["fix_double_prefixed_id"].append({
                            "old_id": id_value,
                            "new_id": id_value[12:]
                        })
                
                # Count regular prefixed IDs
                elif id_value.startswith("story_") and not id_value.startswith("story_story_"):
                    updates["remove_prefix_from_id"] += 1
                    
                    # Add to examples
                    if len(examples["remove_prefix_from_id"]) < 5:
                        examples["remove_prefix_from_id"].append({
                            "old_id": id_value,
                            "new_id": id_value[6:]
                        })
                
                # Count missing external IDs
                if external_id is None:
                    updates["set_missing_external_id"] += 1
                    
                    # Add to examples
                    if len(examples["set_missing_external_id"]) < 5:
                        examples["set_missing_external_id"].append({
                            "id": id_value,
                            "would_be_external_id": f"story_{id_value[6:] if id_value.startswith('story_') else id_value}"
                        })
                
                # Count external IDs without prefix
                elif external_id and not external_id.startswith("story_"):
                    updates["add_prefix_to_external_id"] += 1
                    
                    # Add to examples
                    if len(examples["add_prefix_to_external_id"]) < 5:
                        examples["add_prefix_to_external_id"].append({
                            "old_external_id": external_id,
                            "new_external_id": f"story_{external_id}"
                        })
            
            # For dry runs, add potentially conflicting IDs
            # Get all unprefixed IDs
            query = """
            MATCH (s:Story)
            WHERE NOT s.id STARTS WITH 'story_'
            RETURN s.id as unprefixed_id
            """
            unprefixed_result = await db.run(query)
            unprefixed_records = await unprefixed_result.data()
            unprefixed_ids = {r.get("unprefixed_id") for r in unprefixed_records}
            
            # Get all prefixed IDs that would conflict
            query = """
            MATCH (s:Story)
            WHERE s.id STARTS WITH 'story_'
            WITH s.id as prefixed_id, 
                 CASE 
                     WHEN s.id STARTS WITH 'story_story_' THEN SUBSTRING(s.id, 12)
                     ELSE SUBSTRING(s.id, 6)
                 END as unprefixed_id
            RETURN prefixed_id, unprefixed_id
            """
            conflict_result = await db.run(query)
            conflict_records = await conflict_result.data()
            
            # Check for conflicts
            conflicts = 0
            for record in conflict_records:
                prefixed_id = record.get("prefixed_id")
                unprefixed_id = record.get("unprefixed_id")
                
                if unprefixed_id in unprefixed_ids:
                    conflicts += 1
                    
                    # Add to examples
                    if len(examples["resolved_conflicts"]) < 5:
                        is_double = prefixed_id.startswith("story_story_")
                        examples["resolved_conflicts"].append({
                            "original_id": prefixed_id,
                            "unprefixed_id": unprefixed_id,
                            "existing_id": unprefixed_id,
                            "resolution": f"{prefixed_id} would become {unprefixed_id}{conflict_suffix}"
                        })
            
            updates["resolved_conflicts"] = conflicts
        
        # Flatten examples for the response
        flattened_examples = []
        for example_type, type_examples in examples.items():
            if type_examples:  # Only include non-empty lists
                for example in type_examples:
                    example["type"] = example_type
                flattened_examples.extend(type_examples)
        
        # Return result
        return {
            "before": analysis_result,
            "updates": updates,
            "examples": flattened_examples,
            "summary": {
                "total_changes": sum(updates.values()),
                "changes_applied": not dry_run
            },
            "dry_run": dry_run,
            "fix_double_prefixes": fix_double_prefixes,
            "conflicts_found": updates["resolved_conflicts"] > 0,
            "conflict_resolution_strategy": f"Appending '{conflict_suffix}' to duplicate IDs"
        }
    except Exception as e:
        logger.error(f"Error standardizing Neo4j IDs: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error standardizing Neo4j IDs: {e}")

# Batch operations