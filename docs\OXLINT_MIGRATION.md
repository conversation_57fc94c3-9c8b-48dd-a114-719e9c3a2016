# Oxlint Migration Guide

This document outlines the migration from <PERSON><PERSON><PERSON> + <PERSON><PERSON><PERSON> to <PERSON><PERSON><PERSON> for the Tahimoto project.

## What Changed

### Linter Migration
- **Before**: ESLint + <PERSON>ttier (JavaScript-based, slower)
- **After**: Oxlint (Rust-based, ultra-fast)

### Benefits of Oxlint
- **Blazing Fast**: 10-100x faster than ESLint (written in Rust)
- **Zero Configuration**: Works out of the box with sensible defaults
- **ESLint Compatible**: Supports most ESLint rules and configurations
- **Built-in Formatting**: Combines linting and formatting in one tool
- **Better Performance**: Utilizes all CPU cores for parallel processing

## Performance Comparison

### Before (ESLint + Prettier)
- **Linting**: ~2-5 seconds for 27 files
- **Formatting**: ~1-3 seconds additional
- **Total**: ~3-8 seconds

### After (Oxlint)
- **Linting**: ~58ms for 27 files with 87 rules using 16 threads
- **Formatting**: ~13ms for 27 files
- **Total**: ~71ms (50-100x faster!)

## Updated Files

### Package Configuration
- `frontend-vue/package.json`: Updated scripts to use oxlint
- `frontend-vue/oxlintrc.json`: New configuration file (replaces .eslintrc.cjs)
- Removed ESLint and Prettier dependencies

### Removed Dependencies
```json
{
  "devDependencies": {
    "@rushstack/eslint-patch": "removed",
    "@typescript-eslint/eslint-plugin": "removed",
    "@typescript-eslint/parser": "removed", 
    "@vue/eslint-config-prettier": "removed",
    "@vue/eslint-config-typescript": "removed",
    "eslint": "removed",
    "eslint-plugin-vue": "removed",
    "prettier": "removed"
  }
}
```

### Added Dependencies
```json
{
  "devDependencies": {
    "oxlint": "^1.5.0"
  }
}
```

## Updated Commands

### Development Workflow
```bash
# Lint code (check only)
pnpm run lint:frontend

# Lint and fix issues
pnpm run lint:frontend:fix

# Format code (same as lint:fix)
pnpm run format:frontend
```

### Root Level Commands
```bash
# From project root
pnpm run lint:frontend        # Check only
pnpm run lint:frontend:fix    # Fix issues
pnpm run format:frontend      # Format code
```

## Configuration

### Oxlint Configuration (`oxlintrc.json`)
```json
{
  "rules": {
    "typescript": "warn",
    "unicorn": "warn",
    "react": "off",
    "jsx-a11y": "off",
    "nextjs": "off"
  },
  "env": {
    "browser": true,
    "es2022": true,
    "node": true
  },
  "globals": {
    "describe": "readonly",
    "it": "readonly", 
    "expect": "readonly",
    "beforeEach": "readonly",
    "afterEach": "readonly",
    "vi": "readonly"
  },
  "ignore_patterns": [
    "**/node_modules/**",
    "**/dist/**",
    "**/.vite/**",
    "**/coverage/**",
    "**/*.min.js"
  ]
}
```

## Migration Steps Completed

1. ✅ **Installed Oxlint**: Added oxlint as dev dependency
2. ✅ **Removed Old Tools**: Uninstalled ESLint, Prettier, and related packages
3. ✅ **Updated Scripts**: Modified package.json scripts to use oxlint
4. ✅ **Created Configuration**: Added oxlintrc.json with project-specific rules
5. ✅ **Tested Performance**: Verified ultra-fast linting and formatting
6. ✅ **Updated Documentation**: Updated README.md and created this guide

## IDE Integration

### VS Code
For optimal VS Code integration, install the Oxlint extension:
1. Open VS Code Extensions (Ctrl+Shift+X)
2. Search for "oxlint"
3. Install the official Oxlint extension
4. Restart VS Code

### Configuration
The extension will automatically use the `oxlintrc.json` configuration file.

## Troubleshooting

### Common Issues

**"oxlint: command not found"**
- Ensure oxlint is installed: `pnpm install`
- Check if it's in devDependencies: `pnpm list oxlint`

**Configuration not working**
- Verify `oxlintrc.json` is in the frontend-vue directory
- Check JSON syntax is valid
- Restart your IDE after configuration changes

**Performance not as expected**
- Oxlint should be significantly faster than ESLint
- If not, check if other processes are consuming CPU
- Ensure you're using the latest version: `pnpm update oxlint`

## Rollback Plan

If issues arise, you can temporarily rollback:

1. Remove oxlint: `pnpm remove oxlint`
2. Reinstall ESLint stack: `pnpm add -D eslint prettier @typescript-eslint/eslint-plugin @typescript-eslint/parser @vue/eslint-config-prettier @vue/eslint-config-typescript eslint-plugin-vue`
3. Restore `.eslintrc.cjs` configuration
4. Update package.json scripts back to ESLint/Prettier

However, we recommend resolving oxlint issues rather than rolling back for the significant performance benefits.

## Next Steps

- Consider integrating oxlint into CI/CD pipeline
- Explore additional oxlint rules and configurations
- Monitor performance improvements in development workflow
- Share oxlint benefits with the team
