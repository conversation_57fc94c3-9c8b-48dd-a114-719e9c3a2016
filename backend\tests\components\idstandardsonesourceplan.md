# ID Standards Branch Plan

This plan follows a layered approach, starting from the core and working outward to ensure consistent ID handling throughout the system.

## Phase 1: Core Service Enhancement (Days 1-2)

### 1.1 Enhance IdService
- Ensure `IdService` has all necessary methods for your entity types
- Add any missing entity types to `ENTITY_TYPES` dictionary
- Implement robust error handling and logging

### 1.2 Create Comprehensive IdService Tests

```python
# Add tests for all entity types and edge cases
def test_id_service_standardization_for_all_entity_types():
    # Test story IDs
    assert IdService.standardize_id("12345", "story") == "story_12345"
    assert IdService.standardize_id("story_12345", "story") == "story_12345"
    
    # Test theme IDs
    assert IdService.standardize_id("adventure", "theme") == "theme_adventure"
    assert IdService.standardize_id("theme_adventure", "theme") == "theme_adventure"
    
    # Test relationship IDs
    # ... etc for all entity types
```

### 1.3 Create ID Format Migration Script

```python
# Create a utility script to validate and report on ID format issues
from app.services.id_service import IdService

def scan_database_for_id_format_issues():
    """Scan Neo4j for ID format inconsistencies"""
    # Implementation details...
```

## Phase 2: Database Layer Updates (Days 3-4)

### 2.1 Update Neo4j Data Access Functions
- Identify all database access functions that use IDs
- Apply IdService pattern to each function:

```python
async def get_story_by_id(session, story_id):
    # Standardize input ID
    standard_id = IdService.standardize_id(story_id, "story")
    
    # Convert to database format for query
    db_id = IdService.to_database_id(standard_id, "story")
    
    # Execute query with database ID
    result = await session.run("MATCH (s:Story {id: $id}) RETURN s", {"id": db_id})
    
    # Return with standardized ID
    story = result.single()
    if story:
        story_data = dict(story["s"])
        story_data["id"] = IdService.standardize_id(story_data.get("id"), "story")
        return story_data
    return None
```

### 2.2 Update Redis Caching Layer
- Identify all cache key generation functions
- Replace with IdService.get_cache_key():

```python
def get_story_cache_key(story_id):
    standard_id = IdService.standardize_id(story_id, "story")
    return IdService.get_cache_key(standard_id, "story")
```

### 2.3 Create/Update Database Tests
- Update database tests to verify ID standardization
- Add tests for edge cases and error handling

## Phase 3: Service Layer Updates (Days 5-7)

### 3.1 Identify All Service Classes That Use IDs
- ThemeRelationshipService
- MediaQueryService
- AniListService
- Any other services that handle IDs

### 3.2 Update Each Service Class
Example for ThemeRelationshipService:

```python
async def get_theme_relationship(self, session, relationship_id):
    # Standardize input ID
    standard_id = IdService.standardize_id(relationship_id, "relationship")
    
    # Parse standardized ID to get components
    source_id, rel_type, target_id = IdService.parse_relationship_id(standard_id)
    
    # Convert to database format for query
    source_db_id = IdService.to_database_id(source_id, "theme")
    target_db_id = IdService.to_database_id(target_id, "theme")
    
    # Execute query with database IDs...
```

### 3.3 Update Service Tests
- Adjust test expectations to match standardized ID formats
- Add tests specifically for ID handling

## Phase 4: API Endpoint Updates (Days 8-10)

### 4.1 REST API Endpoints
- Update `/app/api/v1/endpoints/stories.py` first:

```python
@router.get("/{story_id}")
async def get_story(story_id: str, ...):
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(story_id, "story")
    
    # Use standardized ID throughout function
    # ...
```

- Update other REST endpoints in priority order

### 4.2 GraphQL Resolvers
- Update resolver functions to standardize IDs:

```python
@query_resolvers.field("theme")
async def resolve_theme(_, info, id: str):
    # Standardize input ID immediately
    standard_id = IdService.standardize_id(id, "theme")
    
    # Use standardized ID throughout function
    # ...
```

### 4.3 Update API Tests
- Adjust API test expectations to match new ID formats
- Test both raw and standardized IDs in requests
- Verify responses always contain standardized IDs

## Phase 5: Verify Frontend Compatibility (Days 11-12)

### 5.1 Audit Frontend ID Handling
- Identify all places where IDs are used in the frontend
- Document any client-side normalization that can be removed

### 5.2 Update Frontend GraphQL Queries
- Ensure queries work with standardized IDs
- Remove client-side ID normalization once backend is consistent

### 5.3 Frontend Transition Plan
- Document which frontend components can use standardized IDs directly
- Create plan to remove all client-side ID normalization

## Phase 6: Testing and Integration (Days 13-14)

### 6.1 Run Comprehensive Test Suite
```bash
# Run all tests to verify changes
python -m pytest
```

### 6.2 Create Integration Tests
- Create tests that verify end-to-end ID handling

### 6.3 Manual Testing
- Create a checklist of scenarios to test manually

## Phase 7: Documentation and Rollout Plan (Day 15)

### 7.1 Update Documentation
- Update ID standardization guide
- Document the new "single source of truth" approach

### 7.2 Create Rollout Plan
- Plan for merging into main/develop branch
- Determine if feature flag is needed
- Create rollback plan

## Detailed Workflow for Each Component

### For Each File Requiring Changes:

1. **Assessment**:
   ```bash
   # Use grep to find ID usage patterns
   grep -r "id = " --include="*.py" .
   grep -r "\.id" --include="*.py" .
   grep -r "\"id\":" --include="*.py" .
   ```

2. **Categorization**:
   - ID input standardization
   - ID database conversion
   - ID response formatting
   - Cache key generation

3. **Transformation**:
   - Apply appropriate IdService method for each case
   - Add error handling
   - Update docstrings

4. **Testing**:
   - Run affected tests
   - Fix failing tests
   - Add new tests for edge cases

5. **Documentation**:
   - Add comments explaining ID standardization
   - Update function docstrings

### Example Change Workflow for stories.py:

1. Replace manual ID normalization in `get_neo4j_recommendations`:
   ```python
   # BEFORE
   normalized_id = story_id
   if not normalized_id.startswith("story_"):
       if "_" in normalized_id:
           parts = normalized_id.split("_", 1)
           if len(parts) > 1:
               normalized_id = parts[1]
       normalized_id = f"story_{normalized_id}"
   
   # AFTER
   normalized_id = IdService.standardize_id(story_id, "story")
   db_id = IdService.to_database_id(normalized_id, "story")
   ```

2. Update cache key generation:
   ```python
   # BEFORE
   def build_story_cache_key(key: str) -> str:
       return f"story:{key}"
   
   # AFTER
   def build_story_cache_key(key: str) -> str:
       standard_id = IdService.standardize_id(key, "story")
       return IdService.get_cache_key(standard_id, "story")
   ```

3. Update all endpoint handlers:
   ```python
   # BEFORE
   @router.get("/{story_id}")
   async def get_story(story_id: str, ...):
       # Direct use of story_id
   
   # AFTER
   @router.get("/{story_id}")
   async def get_story(story_id: str, ...):
       # Standardize immediately
       standard_id = IdService.standardize_id(story_id, "story")
       # Use standard_id throughout
   ```

4. Update response formatting:
   ```python
   # BEFORE
   return {
       "id": story_data["id"],
       # other fields...
   }
   
   # AFTER
   return {
       "id": IdService.standardize_id(story_data["id"], "story"),
       # other fields...
   }
   ```

## Dependency Tracking

Create a dependency graph to ensure changes are made in the correct order:

1. IdService → Database Helpers → Service Classes → API Endpoints → Tests
2. Cache Utilities → Cache Implementation → Cache Tests

## Final Checklist Before Merging:

1. ✅ All tests pass
2. ✅ No hardcoded ID manipulation remains
3. ✅ Documentation is updated
4. ✅ Frontend compatibility is verified
5. ✅ Performance impact is assessed

This plan provides a systematic approach to implementing ID standardization throughout your application, ensuring a single source of truth for ID handling.