#!/usr/bin/env python
"""
Theme Taxonomy Builder Script

This script builds a hierarchical taxonomy of themes by establishing
parent-child relationships and other semantic relationships between themes.
It can be used to initialize or update the theme structure in the Neo4j database.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.append(str(Path(__file__).parent.parent))

from app.db.neo4j_session import init_db, driver
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Theme taxonomy structure
# This defines our theme hierarchy and relationships
THEME_TAXONOMY = {
    # Adventure themes and their hierarchy
    "Adventure": {
        "description": "Themes centered around journeys, quests, and exploration",
        "category": "NARRATIVE_STRUCTURE",
        "children": [
            {
                "name": "Exploration",
                "description": "Discovery of new places, ideas, or experiences",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Coming of Age", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.2},
                    {"target": "Freedom", "type": "COMPLEMENTS", "strength": 0.9, "tension": 0.1}
                ]
            },
            {
                "name": "Quest",
                "description": "Journey with a specific goal or purpose",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Heroism", "type": "COMPLEMENTS", "strength": 0.9, "tension": 0.1},
                    {"target": "Sacrifice", "type": "REQUIRES", "strength": 0.7, "tension": 0.6}
                ]
            },
            {
                "name": "Survival",
                "description": "Overcoming deadly challenges and harsh environments",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Man vs Nature", "type": "SPECIALIZES", "strength": 0.9, "tension": 0.2},
                    {"target": "Resilience", "type": "REQUIRES", "strength": 0.8, "tension": 0.3}
                ]
            }
        ],
        "relationships": [
            {"target": "Heroism", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.2},
            {"target": "Coming of Age", "type": "COMPLEMENTS", "strength": 0.7, "tension": 0.3},
            {"target": "Tragedy", "type": "CONTRASTS", "strength": 0.6, "tension": 0.8}
        ]
    },
    
    # Character-related themes
    "Character Development": {
        "description": "Themes related to how characters grow and change",
        "category": "CHARACTER_DYNAMIC",
        "children": [
            {
                "name": "Coming of Age",
                "description": "The transition from youth to adulthood",
                "category": "CHARACTER_DYNAMIC",
                "relationships": [
                    {"target": "Identity", "type": "REQUIRES", "strength": 0.9, "tension": 0.4},
                    {"target": "Rebellion", "type": "COMPLEMENTS", "strength": 0.7, "tension": 0.5}
                ]
            },
            {
                "name": "Redemption",
                "description": "A character's journey from moral failure to moral recovery",
                "category": "CHARACTER_DYNAMIC",
                "relationships": [
                    {"target": "Forgiveness", "type": "REQUIRES", "strength": 0.9, "tension": 0.2},
                    {"target": "Sacrifice", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.3}
                ]
            },
            {
                "name": "Identity",
                "description": "The search for or expression of one's true self",
                "category": "CHARACTER_DYNAMIC",
                "relationships": [
                    {"target": "Society", "type": "CONTRASTS", "strength": 0.7, "tension": 0.6},
                    {"target": "Freedom", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.3}
                ]
            }
        ],
        "relationships": [
            {"target": "Society", "type": "CONTRASTS", "strength": 0.6, "tension": 0.7}
        ]
    },
    
    # Emotional themes
    "Emotional Themes": {
        "description": "Themes focusing on emotional experiences and states",
        "category": "MOOD",
        "children": [
            {
                "name": "Love",
                "description": "Deep affection and attachment between characters",
                "category": "MOOD",
                "relationships": [
                    {"target": "Sacrifice", "type": "COMPLEMENTS", "strength": 0.9, "tension": 0.4},
                    {"target": "Betrayal", "type": "CONTRASTS", "strength": 0.7, "tension": 0.9}
                ]
            },
            {
                "name": "Fear",
                "description": "Response to threat or danger",
                "category": "MOOD",
                "relationships": [
                    {"target": "Courage", "type": "CONTRASTS", "strength": 0.9, "tension": 0.7},
                    {"target": "Survival", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.5}
                ]
            },
            {
                "name": "Joy",
                "description": "Experiences of happiness and fulfillment",
                "category": "MOOD",
                "relationships": [
                    {"target": "Tragedy", "type": "CONTRASTS", "strength": 0.9, "tension": 0.8},
                    {"target": "Freedom", "type": "COMPLEMENTS", "strength": 0.7, "tension": 0.2}
                ]
            }
        ],
        "relationships": []
    },
    
    # Narrative structure themes
    "Narrative Structures": {
        "description": "Themes related to story structure and common plots",
        "category": "NARRATIVE_STRUCTURE",
        "children": [
            {
                "name": "Tragedy",
                "description": "Story ending with the downfall of the protagonist",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Hubris", "type": "COMPLEMENTS", "strength": 0.9, "tension": 0.5},
                    {"target": "Heroism", "type": "CONTRASTS", "strength": 0.6, "tension": 0.8}
                ]
            },
            {
                "name": "Comedy",
                "description": "Humorous or happy endings resolving initial conflicts",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Tragedy", "type": "CONTRASTS", "strength": 0.9, "tension": 0.8},
                    {"target": "Joy", "type": "COMPLEMENTS", "strength": 0.8, "tension": 0.2}
                ]
            },
            {
                "name": "Heroism",
                "description": "Stories of exceptional courage and noble qualities",
                "category": "NARRATIVE_STRUCTURE",
                "relationships": [
                    {"target": "Sacrifice", "type": "REQUIRES", "strength": 0.8, "tension": 0.6},
                    {"target": "Courage", "type": "REQUIRES", "strength": 0.9, "tension": 0.3}
                ]
            }
        ],
        "relationships": []
    }
}

async def ensure_theme_exists(session, name, description, category="UNCATEGORIZED"):
    """
    Ensure a theme exists in the database, create it if it doesn't.
    Returns the theme ID.
    """
    query = """
    MERGE (theme:Theme {name: $name}) 
    ON CREATE SET 
        theme.id = randomUUID(),
        theme.description = $description,
        theme.category = $category,
        theme.confidence = 1.0,
        theme.status = 'VERIFIED',
        theme.created_at = datetime(),
        theme.updated_at = datetime()
    ON MATCH SET
        theme.description = $description,
        theme.category = $category,
        theme.updated_at = datetime()
    RETURN theme.id as id
    """
    result = await session.run(query, {
        "name": name,
        "description": description,
        "category": category
    })
    record = await result.single()
    return record["id"]

async def create_theme_relationship(session, source_id, target_id, rel_type, strength=1.0, tension=0.0):
    """
    Create a relationship between two themes.
    """
    query = """
    MATCH (source:Theme), (target:Theme)
    WHERE source.id = $source_id AND target.id = $target_id
    MERGE (source)-[r:%s]->(target)
    ON CREATE SET 
        r.id = randomUUID(),
        r.strength = $strength,
        r.tension = $tension,
        r.created_at = datetime(),
        r.updated_at = datetime()
    ON MATCH SET
        r.strength = $strength,
        r.tension = $tension,
        r.updated_at = datetime()
    """ % rel_type
    
    await session.run(query, {
        "source_id": source_id,
        "target_id": target_id,
        "strength": strength,
        "tension": tension
    })
    logger.info(f"Created/updated {rel_type} relationship: {source_id} -> {target_id}")

async def create_parent_child_relationship(session, parent_id, child_id):
    """
    Create parent-child relationship between themes.
    This creates both PARENT_OF and CHILD_OF relationships for bi-directional navigation.
    """
    # Create parent->child relationship
    await create_theme_relationship(session, parent_id, child_id, "PARENT_OF", 1.0, 0.0)
    
    # Create child->parent relationship
    await create_theme_relationship(session, child_id, parent_id, "CHILD_OF", 1.0, 0.0)

async def process_theme_hierarchy(session, theme_name, theme_data, parent_id=None):
    """
    Process a theme and its hierarchy recursively.
    """
    # Create or update the theme
    theme_id = await ensure_theme_exists(
        session, 
        theme_name, 
        theme_data.get("description", ""),
        theme_data.get("category", "UNCATEGORIZED")
    )
    logger.info(f"Processed theme: {theme_name} (ID: {theme_id})")
    
    # If this theme has a parent, create the parent-child relationship
    if parent_id:
        await create_parent_child_relationship(session, parent_id, theme_id)
    
    # Process relationships
    for rel in theme_data.get("relationships", []):
        # First ensure the target theme exists
        target_id = await ensure_theme_exists(
            session,
            rel["target"],
            f"Auto-created as relationship target for {theme_name}"
        )
        
        # Create the relationship
        await create_theme_relationship(
            session,
            theme_id,
            target_id,
            rel["type"],
            rel.get("strength", 1.0),
            rel.get("tension", 0.0)
        )
    
    # Process children
    for child in theme_data.get("children", []):
        await process_theme_hierarchy(session, child["name"], child, theme_id)

async def build_taxonomy():
    """
    Main function to build the theme taxonomy.
    """
    logger.info("Starting theme taxonomy build...")
    
    # Initialize the database connection
    await init_db()
    
    async with driver.session(database="neo4j") as session:
        # Process each top-level theme
        for theme_name, theme_data in THEME_TAXONOMY.items():
            await process_theme_hierarchy(session, theme_name, theme_data)
    
    # Close the database connection
    await driver.close()
    logger.info("Theme taxonomy build completed successfully!")

if __name__ == "__main__":
    asyncio.run(build_taxonomy()) 