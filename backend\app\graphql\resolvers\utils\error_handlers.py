"""
Error handling utilities for GraphQL resolvers.
"""
import logging
import traceback
import functools
from typing import Any, Callable, TypeVar, cast

logger = logging.getLogger(__name__)

# Type variables for the decorator
T = TypeVar('T')
ResolverFunc = Callable[..., T]

def handle_resolver_errors(default_return_value: Any = None) -> Callable[[ResolverFunc], ResolverFunc]:
    """
    Decorator for consistent error handling in resolvers.
    
    Args:
        default_return_value: The default value to return in case of error.
                             If None, returns None for queries and {"success": False} for mutations.
    
    Returns:
        A decorator function that wraps resolver functions with error handling.
    """
    def decorator(func: ResolverFunc) -> ResolverFunc:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Get function name for better error logging
                func_name = func.__name__
                
                # Log the error
                logger.error(f"Error in resolver {func_name}: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                
                # Determine default return value based on function name if not provided
                if default_return_value is None:
                    if func_name.startswith("resolve_"):
                        # For mutations that return success/id objects
                        if any(func_name.startswith(f"resolve_{prefix}") for prefix in 
                              ["create", "update", "delete", "approve", "reject", "request"]):
                            # Extract ID from kwargs if available
                            id_value = kwargs.get("id", "unknown")
                            return {"success": False, "id": id_value}
                        # For standard queries
                        return None
                    return None
                return default_return_value
        
        return cast(ResolverFunc, wrapper)
    
    return decorator

def handle_id_format_error(id_value: str, entity_type: str = "entity") -> dict:
    """
    Create a standardized error response for invalid ID format errors.
    
    Args:
        id_value: The invalid ID value
        entity_type: The type of entity (theme, mapping, relationship, etc.)
    
    Returns:
        A standardized error response dictionary
    """
    logger.error(f"Invalid {entity_type} ID format: {id_value}")
    return {
        "success": False, 
        "id": id_value,
        "error": f"Invalid {entity_type} ID format"
    }

def log_resolver_call(func: ResolverFunc) -> ResolverFunc:
    """
    Decorator to log resolver function calls with their arguments.
    
    Args:
        func: The resolver function to wrap
        
    Returns:
        The wrapped function with logging
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        func_name = func.__name__
        # Skip the first two arguments (obj and info) when logging
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['info']}
        logger.debug(f"Calling {func_name} with args: {filtered_kwargs}")
        
        result = await func(*args, **kwargs)
        
        # Log completion (without the full result to avoid huge logs)
        if result is None:
            logger.debug(f"{func_name} returned None")
        elif isinstance(result, dict) and "success" in result:
            logger.debug(f"{func_name} returned success={result.get('success')}")
        else:
            logger.debug(f"{func_name} completed successfully")
            
        return result
    
    return cast(ResolverFunc, wrapper)

class ResolverError(Exception):
    """
    Base class for resolver-specific exceptions.
    
    This allows us to distinguish between expected application errors
    and unexpected system errors.
    """
    def __init__(self, message: str, code: str = "RESOLVER_ERROR"):
        self.message = message
        self.code = code
        super().__init__(self.message)

class InvalidIdError(ResolverError):
    """Exception raised when an ID format is invalid."""
    def __init__(self, id_value: str, entity_type: str = "entity"):
        message = f"Invalid {entity_type} ID format: {id_value}"
        super().__init__(message, code="INVALID_ID")
        self.id_value = id_value
        self.entity_type = entity_type

class EntityNotFoundError(ResolverError):
    """Exception raised when an entity is not found."""
    def __init__(self, id_value: str, entity_type: str = "entity"):
        message = f"{entity_type.capitalize()} with ID {id_value} not found"
        super().__init__(message, code="NOT_FOUND")
        self.id_value = id_value
        self.entity_type = entity_type 