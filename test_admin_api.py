#!/usr/bin/env python3
"""
Test script for Admin Theme Relations API

Tests the admin theme relations API endpoints to verify the complete workflow.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def test_admin_api():
    """Test the admin theme relations API workflow."""
    
    print("🧪 Testing Admin Theme Relations API")
    print("=" * 50)
    
    # Test 1: Get initial dashboard
    print("\n1. Testing Admin Dashboard...")
    try:
        response = requests.get(f"{BASE_URL}/admin/theme-relations/dashboard")
        response.raise_for_status()
        dashboard = response.json()
        print(f"   ✅ Dashboard: {dashboard['total_suggestions']} total suggestions")
        print(f"   📊 Pending: {dashboard['pending_suggestions']}, Approved: {dashboard['approved_suggestions']}")
    except Exception as e:
        print(f"   ❌ Dashboard error: {e}")
        return
    
    # Test 2: Analyze anime to create suggestions
    print("\n2. Testing Anime Analysis...")
    try:
        analyze_request = {
            "anime_id": "161645",  # The Apothecary Diaries
            "force_refresh": True
        }
        response = requests.post(
            f"{BASE_URL}/admin/theme-relations/analyze-anime",
            json=analyze_request
        )
        response.raise_for_status()
        suggestions = response.json()
        print(f"   ✅ Created {len(suggestions)} theme suggestions")
        
        for i, suggestion in enumerate(suggestions[:3], 1):  # Show first 3
            print(f"   {i}. {suggestion['theme_name']} ({suggestion['confidence_percent']})")
            print(f"      Suggestion ID: {suggestion['suggestion_id']}")
        
        if suggestions:
            first_suggestion_id = suggestions[0]['suggestion_id']
            print(f"   📝 First suggestion ID: {first_suggestion_id}")
        else:
            print("   ⚠️  No suggestions created")
            return
            
    except Exception as e:
        print(f"   ❌ Analysis error: {e}")
        return
    
    # Test 3: Get suggestions list
    print("\n3. Testing Suggestions List...")
    try:
        response = requests.get(f"{BASE_URL}/admin/theme-relations/suggestions")
        response.raise_for_status()
        all_suggestions = response.json()
        print(f"   ✅ Retrieved {len(all_suggestions)} suggestions")
        
        # Test filtering by high confidence
        response = requests.get(f"{BASE_URL}/admin/theme-relations/suggestions?min_confidence=0.8")
        response.raise_for_status()
        high_conf_suggestions = response.json()
        print(f"   🎯 High confidence (≥80%): {len(high_conf_suggestions)} suggestions")
        
    except Exception as e:
        print(f"   ❌ Suggestions list error: {e}")
        return
    
    # Test 4: Get specific suggestion
    print("\n4. Testing Individual Suggestion...")
    try:
        response = requests.get(f"{BASE_URL}/admin/theme-relations/suggestions/{first_suggestion_id}")
        response.raise_for_status()
        suggestion = response.json()
        print(f"   ✅ Retrieved suggestion: {suggestion['theme_name']}")
        print(f"   📊 Confidence: {suggestion['confidence_percent']}")
        print(f"   📝 Status: {suggestion['status']}")
        
    except Exception as e:
        print(f"   ❌ Individual suggestion error: {e}")
        return
    
    # Test 5: Review suggestion (approve)
    print("\n5. Testing Suggestion Review...")
    try:
        review_request = {
            "suggestion_id": first_suggestion_id,
            "action": "approve",
            "admin_notes": "Excellent contextual analysis - female MC in historical setting clearly indicates female agency themes"
        }
        response = requests.post(
            f"{BASE_URL}/admin/theme-relations/suggestions/{first_suggestion_id}/review?admin_user=test_admin",
            json=review_request
        )
        response.raise_for_status()
        review_result = response.json()
        print(f"   ✅ Review result: {review_result['message']}")
        print(f"   👤 Reviewed by: {review_result['reviewed_by']}")
        print(f"   📅 Reviewed at: {review_result['reviewed_at']}")
        
    except Exception as e:
        print(f"   ❌ Review error: {e}")
        return
    
    # Test 6: Updated dashboard
    print("\n6. Testing Updated Dashboard...")
    try:
        response = requests.get(f"{BASE_URL}/admin/theme-relations/dashboard")
        response.raise_for_status()
        updated_dashboard = response.json()
        print(f"   ✅ Updated Dashboard:")
        print(f"      Total: {updated_dashboard['total_suggestions']}")
        print(f"      Pending: {updated_dashboard['pending_suggestions']}")
        print(f"      Approved: {updated_dashboard['approved_suggestions']}")
        print(f"      High Confidence Pending: {updated_dashboard['high_confidence_pending']}")
        
    except Exception as e:
        print(f"   ❌ Updated dashboard error: {e}")
        return
    
    # Test 7: Statistics
    print("\n7. Testing Statistics...")
    try:
        response = requests.get(f"{BASE_URL}/admin/theme-relations/stats")
        response.raise_for_status()
        stats = response.json()
        print(f"   ✅ Statistics:")
        print(f"      Total Suggestions: {stats['total_suggestions']}")
        print(f"      Approval Rate: {stats['approval_rate'] * 100:.1f}%")
        print(f"      Average Confidence: {stats['average_confidence'] * 100:.1f}%")
        print(f"      Theme Distribution: {stats['theme_distribution']}")
        
    except Exception as e:
        print(f"   ❌ Statistics error: {e}")
        return
    
    print(f"\n🎉 Admin Theme Relations API Test COMPLETED!")
    print(f"   ✅ All endpoints working correctly")
    print(f"   ✅ Workflow: Analyze → Review → Approve → Statistics")
    print(f"   ✅ Human-in-the-loop validation system operational")


if __name__ == "__main__":
    test_admin_api()
