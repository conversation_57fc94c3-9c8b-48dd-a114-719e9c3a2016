name: Check ID Standardization

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**/*.py'
      - 'backend/**/*.graphql'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**/*.py'
      - 'backend/**/*.graphql'
  workflow_dispatch:  # Allows manual triggering

jobs:
  check-id-standardization:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Fetch all history for all branches and tags
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f backend/requirements.txt ]; then
          pip install -r backend/requirements.txt
        else
          pip install typing-extensions
        fi
    
    - name: Run ID standardization checker
      id: id-check
      run: |
        echo "Running ID standardization check..."
        # Run the checker but save output to a file
        python backend/scripts/check_id_standardization.py > id_check_output.txt 2>&1
        EXIT_CODE=$?
        echo "exit_code=$EXIT_CODE" >> $GITHUB_OUTPUT
        
        # If there were issues, process them to create GitHub annotations
        if [ $EXIT_CODE -ne 0 ]; then
          # Extract file paths and line numbers to create annotations
          grep -n "Line " id_check_output.txt | while read -r line; do
            FILE_PATH=$(echo "$line" | grep -oP "Issues in \K[^:]+")
            LINE_NUM=$(echo "$line" | grep -oP "Line \K[0-9]+")
            ISSUE_MSG=$(echo "$line" | grep -oP "Line [0-9]+: \K.*")
            if [ ! -z "$FILE_PATH" ] && [ ! -z "$LINE_NUM" ] && [ ! -z "$ISSUE_MSG" ]; then
              echo "::error file=$FILE_PATH,line=$LINE_NUM::$ISSUE_MSG"
            fi
          done
        fi
        
        # Print the output for the logs
        cat id_check_output.txt
      continue-on-error: true
      
    - name: Report results
      run: |
        if [ "${{ steps.id-check.outputs.exit_code }}" != "0" ]; then
          echo "ID standardization check failed! Please review the issues above and fix them according to the ID Standardization Guide."
          echo "See docs/ID_Standardization_Guide.md for formatting guidelines."
          exit 1
        else
          echo "ID standardization check passed! All ID formats appear to be compliant."
        fi 