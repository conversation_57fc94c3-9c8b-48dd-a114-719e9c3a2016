#!/usr/bin/env python3
"""
Setup script for initializing the Tahimoto backend environment using uv.
This script:
1. Creates a virtual environment if it doesn't exist
2. Installs dependencies from requirements.txt
3. Sets up environment variables for development

Usage:
    python setup.py
"""

import os
import subprocess
import sys
from pathlib import Path

# Define colors for terminal output
RESET = "\033[0m"
BOLD = "\033[1m"
GREEN = "\033[32m"
YELLOW = "\033[33m"
BLUE = "\033[34m"
RED = "\033[31m"

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()
VENV_DIR = SCRIPT_DIR / ".venv"
REQ_FILE = SCRIPT_DIR / "requirements.txt"
ENV_FILE = SCRIPT_DIR / ".env"
SAMPLE_ENV_FILE = SCRIPT_DIR / ".env.sample"

def print_step(message):
    """Print a step message in a formatted way."""
    print(f"\n{BOLD}{BLUE}=== {message} ==={RESET}\n")

def print_success(message):
    """Print a success message in a formatted way."""
    print(f"{GREEN}✓ {message}{RESET}")

def print_warning(message):
    """Print a warning message in a formatted way."""
    print(f"{YELLOW}⚠ {message}{RESET}")

def print_error(message):
    """Print an error message in a formatted way."""
    print(f"{RED}✗ {message}{RESET}")

def run_command(command, env=None):
    """Run a shell command and return its output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {command}")
        print(e.stderr)
        return None

def check_uv_installed():
    """Check if uv is installed, and install it if not."""
    print_step("Checking for uv installation")
    
    result = run_command("uv --version")
    if result:
        print_success(f"uv is installed (version: {result.strip()})")
        return True
    
    print_warning("uv not found. Installing uv...")
    install_result = run_command("curl -sSf https://install.python-uv.org | python3 -")
    
    if install_result:
        print_success("uv installed successfully")
        return True
    else:
        print_error("Failed to install uv. Please install manually: https://github.com/astral-sh/uv")
        return False

def create_venv():
    """Create a virtual environment using uv if it doesn't exist."""
    print_step("Setting up virtual environment")
    
    if VENV_DIR.exists():
        print_success(f"Virtual environment already exists at {VENV_DIR}")
        return True
    
    print(f"Creating virtual environment at {VENV_DIR}...")
    result = run_command(f"uv venv {VENV_DIR}")
    
    if result is not None:
        print_success("Virtual environment created successfully")
        return True
    else:
        print_error("Failed to create virtual environment")
        return False

def install_dependencies():
    """Install dependencies from requirements.txt using uv."""
    print_step("Installing dependencies")
    
    if not REQ_FILE.exists():
        print_error(f"Requirements file not found at {REQ_FILE}")
        return False
    
    print(f"Installing packages from {REQ_FILE}...")
    result = run_command(f"uv pip install -r {REQ_FILE}")
    
    if result is not None:
        print_success("Dependencies installed successfully")
        return True
    else:
        print_error("Failed to install dependencies")
        return False

def setup_env_file():
    """Create a .env file if it doesn't exist."""
    print_step("Setting up environment variables")
    
    if ENV_FILE.exists():
        print_success(f".env file already exists at {ENV_FILE}")
        return True
    
    if SAMPLE_ENV_FILE.exists():
        # Copy sample env file
        with open(SAMPLE_ENV_FILE, "r") as f_in:
            content = f_in.read()
        
        with open(ENV_FILE, "w") as f_out:
            f_out.write(content)
        
        print_success(f".env file created from sample at {ENV_FILE}")
    else:
        # Create basic .env file
        with open(ENV_FILE, "w") as f:
            f.write("""# Tahimoto Backend Environment Variables
DEBUG=True
LOG_LEVEL=DEBUG

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
SECRET_KEY=dev_secret_key_change_in_production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Agent Service Configuration
USE_AGENT_MAPPING=False
AGENT_SERVICE_URL=http://localhost:8001/api
""")
        print_success(f"Basic .env file created at {ENV_FILE}")
        print_warning("Please update the values in .env with your actual configuration")
    
    return True

def main():
    """Main entry point for the setup script."""
    print(f"{BOLD}Tahimoto Backend Setup{RESET}")
    print("This script will set up your development environment for the Tahimoto backend.")
    
    # Check for uv installation
    if not check_uv_installed():
        sys.exit(1)
    
    # Create virtual environment
    if not create_venv():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup environment variables
    if not setup_env_file():
        sys.exit(1)
    
    print_step("Setup complete!")
    print(f"""
{BOLD}Next steps:{RESET}
1. Activate the virtual environment:
   {BOLD}$ source {VENV_DIR}/bin/activate{RESET} (Linux/macOS)
   {BOLD}$ {VENV_DIR}\\Scripts\\activate{RESET} (Windows)

2. Start the development server:
   {BOLD}$ cd {SCRIPT_DIR}{RESET}
   {BOLD}$ uvicorn app.main:app --reload{RESET}

3. Visit the API documentation:
   {BOLD}http://localhost:8000/docs{RESET}
""")

if __name__ == "__main__":
    main() 