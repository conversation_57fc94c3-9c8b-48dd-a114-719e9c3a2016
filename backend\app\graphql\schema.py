"""
GraphQL schema definition using <PERSON>dn<PERSON>.
"""
import logging
from ariadne import load_schema_from_path, make_executable_schema
from ariadne.asgi import GraphQL
from ariadne.asgi.handlers import GraphQLHTTPHandler
from fastapi import FastAPI
from .resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Load schema from a .graphql file
logger.debug("Loading GraphQL schema from file")
type_defs = load_schema_from_path("app/graphql/schema.graphql")
logger.debug("GraphQL schema loaded successfully")

# Make the executable schema by binding resolvers
logger.debug("Creating executable schema with resolvers")
schema = make_executable_schema(
    type_defs,
    query_resolvers,
    mutation_resolvers
)
logger.debug("Executable schema created successfully")

# Create the GraphQL application
logger.debug("Initializing GraphQL application")
graphql_app = GraphQL(
    schema=schema,
    debug=True
)
logger.debug("GraphQL application initialized successfully")

def bind_schema_to_fastapi(app: FastAPI, path: str = "/graphql"):
    """
    Bind the GraphQL schema to a FastAPI application at the specified path.
    
    Args:
        app: The FastAPI application
        path: The path where GraphQL should be mounted
    """
    logger.info(f"Mounting GraphQL schema at {path}")
    app.mount(path, graphql_app)
    logger.info("GraphQL schema mounted successfully") 