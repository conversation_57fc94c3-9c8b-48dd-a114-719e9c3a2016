from enum import Enum

class ThemeRelationshipType(str, Enum):
    """
    Types of relationships between themes.
    
    These relationship types define how themes interact with each other:
    - PARENT_OF: Hierarchical relationship where one theme encompasses another
    - COMPLEMENTS: Themes naturally enhance each other
    - CONTRASTS: Themes create tension through their apparent contradiction
    - RELATED_TO: General connection between themes without specific relationship type
    """
    PARENT_OF = "PARENT_OF"
    COMPLEMENTS = "COMPLEMENTS"
    CONTRASTS = "CONTRASTS"
    RELATED_TO = "RELATED_TO" 