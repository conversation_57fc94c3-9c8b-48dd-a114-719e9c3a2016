# PNPM Migration Guide

This document outlines the migration from npm to pnpm for the Tahimoto project.

## What Changed

### Package Manager
- **Before**: npm
- **After**: pnpm (v8.15.0+)

### Benefits of pnpm
- **Faster installations**: Up to 2x faster than npm
- **Disk space efficient**: Uses hard links to avoid duplicating packages
- **Strict dependency resolution**: Prevents phantom dependencies
- **Better monorepo support**: Native workspace support

## Updated Files

### Root Package Configuration
- `package.json`: Updated all scripts to use `pnpm` instead of `npm`
- Added `packageManager` field specifying pnpm version
- Updated `engines` field to require pnpm instead of npm

### Frontend Configuration
- `frontend-vue/Dockerfile.dev`: Updated to install and use pnpm
- `frontend-vue/pnpm-lock.yaml`: New lockfile (replaces package-lock.json)

### Documentation
- `README.md`: Updated development commands to use pnpm
- `docs/TESTING.md`: Updated test commands to use pnpm

## Installation

### Prerequisites
Install pnpm globally if not already installed:

```bash
# Using npm (one-time only)
npm install -g pnpm

# Or using PowerShell (Windows)
iwr https://get.pnpm.io/install.ps1 -useb | iex

# Or using Homebrew (macOS)
brew install pnpm
```

### Project Setup
```bash
# Clone the repository
git clone https://github.com/calmren/Tahimoto.git
cd Tahimoto

# Install frontend dependencies
cd frontend-vue
pnpm install

# Return to root
cd ..
```

## Updated Commands

### Development
```bash
# Start development environment
pnpm run dev

# Start frontend only
pnpm run dev:frontend

# Start backend only
pnpm run dev:backend
```

### Testing
```bash
# Run all tests
pnpm test

# Run frontend tests
pnpm run test:frontend

# Run backend tests
pnpm run test:backend

# Run with coverage
pnpm run test:coverage
```

### Linting & Formatting
```bash
# Lint frontend code (check only)
pnpm run lint:frontend

# Lint and fix issues
pnpm run lint:frontend:fix

# Format frontend code
pnpm run format:frontend
```

**Note**: The project now uses Oxlint (Rust-based) instead of ESLint + Prettier for ultra-fast linting and formatting. See [OXLINT_MIGRATION.md](OXLINT_MIGRATION.md) for details.

## Docker Integration

The Docker setup has been updated to use pnpm:
- Frontend container now installs pnpm globally
- Uses `pnpm install --frozen-lockfile` for reproducible builds
- Includes pnpm-lock.yaml in the build context

## Migration Notes

### For Developers
1. **Remove old files**: Delete `node_modules` and `package-lock.json` from frontend
2. **Install pnpm**: Follow installation instructions above
3. **Install dependencies**: Run `pnpm install` in frontend-vue directory
4. **Update habits**: Use `pnpm` commands instead of `npm`

### For CI/CD
- Update build scripts to use pnpm
- Cache `~/.pnpm-store` instead of `~/.npm`
- Use `pnpm install --frozen-lockfile` for production builds

## Troubleshooting

### Common Issues

**"pnpm: command not found"**
- Install pnpm globally: `npm install -g pnpm`

**"Cannot find module" errors**
- Delete node_modules and run `pnpm install`
- Ensure you're using pnpm commands, not npm

**Docker build failures**
- Ensure pnpm-lock.yaml is included in build context
- Check Dockerfile.dev uses correct pnpm commands

### Performance Tips
- Use `pnpm install --frozen-lockfile` in production
- Enable pnpm store on CI for faster builds
- Use `pnpm prune` to remove unused dependencies

## Rollback Plan

If issues arise, you can temporarily rollback:

1. Delete `pnpm-lock.yaml`
2. Restore `package-lock.json` from git
3. Run `npm install`
4. Update scripts back to npm commands

However, we recommend resolving pnpm issues rather than rolling back for long-term benefits.
