"""
GraphQL resolvers for cross-media recommendation operations.
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme, story
import neo4j
from app.graphql.resolvers.utils.error_handlers import handle_resolver_errors, log_resolver_call
from app.graphql.resolvers.utils.id_standardization import ensure_story_id_prefix
from app.services.media_query import MediaQueryService
from app.graphql.resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

# Initialize services
media_service = MediaQueryService()

@query_resolvers.field("recommendCrossmedia")
@handle_resolver_errors()
@log_resolver_call
async def resolve_recommend_crossmedia(_, info, sourceType: str, sourceId: str, targetTypes=None, limit: int = 5):
    """
    Resolve cross-media recommendations based on theme similarity.
    
    Args:
        sourceType: The type of the source media (e.g., ANIME)
        sourceId: The ID of the source media
        targetTypes: Optional list of target media types (e.g., BOOK, MOVIE)
        limit: Maximum number of results to return
        
    Returns:
        List of recommended media items from different media types
    """
    logger.debug(f"Finding cross-media recommendations for {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use the MediaQueryService to get cross-media recommendations
    recommendations = await media_service.recommend_crossmedia(
        session=session,
        source_id=sourceId,
        source_type=sourceType,
        target_types=targetTypes,
        limit=limit
    )
    
    return recommendations 