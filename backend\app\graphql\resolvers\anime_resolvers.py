"""
GraphQL resolvers for anime-related operations.
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme, story
import neo4j
from app.graphql.resolvers.utils.error_handlers import handle_resolver_errors, log_resolver_call
from app.graphql.resolvers.utils.id_standardization import ensure_story_id_prefix
from app.services.media_query import MediaQueryService
from app.graphql.resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

# Initialize services
media_service = MediaQueryService()

@query_resolvers.field("anime")
@handle_resolver_errors()
@log_resolver_call
async def resolve_anime(_, info, id: str):
    """
    Resolve anime details.
    
    Args:
        id: The ID of the anime
        
    Returns:
        Anime details
    """
    logger.debug(f"Resolving anime with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use the MediaQueryService to get anime details
    media = await media_service.get_media(
        session=session,
        media_id=id,
        media_type="ANIME"
    )
    
    return media

@query_resolvers.field("similarAnime")
@handle_resolver_errors()
@log_resolver_call
async def resolve_similar_anime(_, info, id: str, limit: int = 5):
    """
    Resolve similar anime based on theme similarity.
    
    Args:
        id: The ID of the reference anime
        limit: Maximum number of results to return
        
    Returns:
        List of similar anime
    """
    logger.debug(f"Finding similar anime for ID: {id}, limit: {limit}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use the MediaQueryService to find similar anime
    similar_anime = await media_service.find_similar_media(
        session=session,
        source_id=id,
        source_type="ANIME",
        target_type="ANIME",
        limit=limit
    )
    
    return similar_anime 