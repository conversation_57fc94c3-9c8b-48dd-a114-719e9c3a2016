"""
Tests for the AnimeThemeMapper component.

This test suite verifies the functionality of the anime theme mapper,
particularly its integration with the TagCombinationAnalyzer.
"""
import pytest
from unittest.mock import patch, MagicMock
from app.services.mapping.anime_mapper import AnimeThemeMapperManual
from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer


@pytest.fixture
def mock_tag_analyzer():
    """Create a mock TagCombinationAnalyzer for testing."""
    analyzer = MagicMock(spec=TagCombinationAnalyzer)
    
    # Configure analyzer.analyze_combinations to return test data
    analyzer.analyze_combinations.return_value = [
        {
            "theme_id": "psychological_thriller",
            "name": "Psychological Thriller",
            "confidence": 0.85,
            "mapping_type": "mood",
            "context": "Pattern match: Thriller, Psychological"
        }
    ]
    
    # Configure get_confidence_scores to return test data
    analyzer.get_confidence_scores.return_value = {
        "psychological_thriller": 0.85
    }
    
    # Configure get_mapping_types to return test data
    analyzer.get_mapping_types.return_value = {
        "psychological_thriller": "mood"
    }
    
    return analyzer


@pytest.fixture
def anime_mapper(mock_tag_analyzer):
    """Create an AnimeThemeMapperManual with mock analyzer for testing."""
    mapper = AnimeThemeMapperManual()
    mapper.tag_analyzer = mock_tag_analyzer
    return mapper


@pytest.mark.component
class TestAnimeThemeMapper:
    """Test suite for AnimeThemeMapper component."""

    def test_map_to_universal_themes(self, anime_mapper, mock_tag_analyzer):
        """Test mapping anime metadata to universal themes with enhanced analysis."""
        # Test media info
        media_info = {
            "title": "Test Anime",
            "genres": ["Thriller", "Psychological"],
            "tags": [
                {"name": "Mystery", "category": "Genre", "rank": 80}
            ]
        }
        
        # Map to themes
        themes = anime_mapper.map_to_universal_themes(media_info)
        
        # Verify analyzer was called with correct parameters
        mock_tag_analyzer.analyze_combinations.assert_called_once_with(
            media_info["genres"], 
            media_info["tags"]
        )
        
        # Verify enhanced themes are included in results
        assert "psychological_thriller" in themes
        # Also verify basic mapping still works
        for genre in media_info["genres"]:
            genre_themes = anime_mapper.genre_map.get(genre, [])
            for theme in genre_themes:
                assert theme in themes

    def test_get_enhanced_theme_mapping(self, anime_mapper, mock_tag_analyzer):
        """Test getting enhanced theme mapping with detailed information."""
        # Test media info
        media_info = {
            "title": "Test Anime",
            "genres": ["Thriller", "Psychological"],
            "tags": [
                {"name": "Mystery", "category": "Genre", "rank": 80}
            ]
        }
        
        # Get enhanced themes
        enhanced_themes = anime_mapper.get_enhanced_theme_mapping(media_info)
        
        # Verify analyzer was called with correct parameters
        mock_tag_analyzer.analyze_combinations.assert_called_once_with(
            media_info["genres"], 
            media_info["tags"]
        )
        
        # Verify enhanced themes contain expected data
        assert len(enhanced_themes) == 1
        assert enhanced_themes[0]["theme_id"] == "psychological_thriller"
        assert enhanced_themes[0]["confidence"] == 0.85
        assert enhanced_themes[0]["mapping_type"] == "mood"
        assert "source" in enhanced_themes[0]
        assert enhanced_themes[0]["source"] == "tag_combination_analyzer"

    def test_get_confidence_scores(self, anime_mapper, mock_tag_analyzer):
        """Test getting confidence scores for theme mappings."""
        # Test media info
        media_info = {
            "title": "Test Anime",
            "genres": ["Thriller", "Psychological"],
            "tags": [
                {"name": "Mystery", "category": "Genre", "rank": 80}
            ]
        }
        
        # Get confidence scores
        scores = anime_mapper.get_confidence_scores(media_info)
        
        # Verify analyzer methods were called
        assert mock_tag_analyzer.analyze_combinations.called
        assert mock_tag_analyzer.get_confidence_scores.called
        
        # Verify enhanced scores are included
        assert "psychological_thriller" in scores
        assert scores["psychological_thriller"] == 0.85

    def test_get_mapping_types(self, anime_mapper, mock_tag_analyzer):
        """Test getting mapping types for theme mappings."""
        # Test media info
        media_info = {
            "title": "Test Anime",
            "genres": ["Thriller", "Psychological"],
            "tags": [
                {"name": "Mystery", "category": "Genre", "rank": 80}
            ]
        }
        
        # Get mapping types
        types = anime_mapper.get_mapping_types(media_info)
        
        # Verify analyzer methods were called
        assert mock_tag_analyzer.analyze_combinations.called
        assert mock_tag_analyzer.get_mapping_types.called
        
        # Verify enhanced types are included
        assert "psychological_thriller" in types
        assert types["psychological_thriller"] == "mood"

    def test_analyze_context(self, anime_mapper, mock_tag_analyzer):
        """Test analyzing context for theme mappings."""
        # Configure mock to return context
        mock_tag_analyzer.analyze_combinations.return_value = [
            {
                "theme_id": "psychological_thriller",
                "name": "Psychological Thriller",
                "confidence": 0.85,
                "mapping_type": "mood",
                "context": "Pattern match: Thriller, Psychological"
            }
        ]
        
        # Test media info
        media_info = {
            "title": "Test Anime",
            "genres": ["Thriller", "Psychological"],
            "tags": [
                {"name": "Mystery", "category": "Genre", "rank": 80}
            ]
        }
        
        # Analyze context
        context = anime_mapper.analyze_context(media_info)
        
        # Verify context contains expected data
        assert context is not None
        assert "psychological_thriller" in context
        assert context["psychological_thriller"] == "Pattern match: Thriller, Psychological" 