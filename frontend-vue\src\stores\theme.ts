import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { themeApi } from '@/services/api'
import type {
  Theme,
  ThemeSearchQuery,
  ThemeSearchResponse,
  MediaAnalysisRequest,
  MediaAnalysisResponse,
  MCPCapabilities,
} from '@/types/theme'

export const useThemeStore = defineStore('theme', () => {
  // State
  const themes = ref<Theme[]>([])
  const currentTheme = ref<Theme | null>(null)
  const searchResults = ref<ThemeSearchResponse | null>(null)
  const capabilities = ref<MCPCapabilities | null>(null)
  const categories = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const themeCount = computed(() => themes.value.length)
  const hasError = computed(() => error.value !== null)

  // Actions
  async function fetchCapabilities() {
    try {
      loading.value = true
      error.value = null
      capabilities.value = await themeApi.getCapabilities()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch capabilities'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function fetchCategories() {
    try {
      loading.value = true
      error.value = null
      categories.value = await themeApi.getCategories()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch categories'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function searchThemes(query: ThemeSearchQuery) {
    try {
      loading.value = true
      error.value = null
      searchResults.value = await themeApi.searchThemes(query)
      return searchResults.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to search themes'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function analyzeMedia(request: MediaAnalysisRequest): Promise<MediaAnalysisResponse> {
    try {
      loading.value = true
      error.value = null
      const result = await themeApi.analyzeMedia(request)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to analyze media'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function getThemeRelationships(themeId: string) {
    try {
      loading.value = true
      error.value = null
      const result = await themeApi.getThemeRelationships(themeId)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get theme relationships'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function fetchAllThemes() {
    try {
      loading.value = true
      error.value = null
      themes.value = await themeApi.getAllThemes()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch themes'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function fetchTheme(id: string) {
    try {
      loading.value = true
      error.value = null
      currentTheme.value = await themeApi.getTheme(id)
      return currentTheme.value
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch theme'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function createTheme(theme: Omit<Theme, 'id'>) {
    try {
      loading.value = true
      error.value = null
      const newTheme = await themeApi.createTheme(theme)
      themes.value.push(newTheme)
      return newTheme
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create theme'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateTheme(id: string, theme: Partial<Theme>) {
    try {
      loading.value = true
      error.value = null
      const updatedTheme = await themeApi.updateTheme(id, theme)
      const index = themes.value.findIndex((t) => t.id === id)
      if (index !== -1) {
        themes.value[index] = updatedTheme
      }
      if (currentTheme.value?.id === id) {
        currentTheme.value = updatedTheme
      }
      return updatedTheme
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update theme'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteTheme(id: string) {
    try {
      loading.value = true
      error.value = null
      await themeApi.deleteTheme(id)
      themes.value = themes.value.filter((t) => t.id !== id)
      if (currentTheme.value?.id === id) {
        currentTheme.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete theme'
      throw err
    } finally {
      loading.value = false
    }
  }

  function clearError() {
    error.value = null
  }

  function clearSearchResults() {
    searchResults.value = null
  }

  return {
    // State
    themes,
    currentTheme,
    searchResults,
    capabilities,
    categories,
    loading,
    error,
    
    // Getters
    themeCount,
    hasError,
    
    // Actions
    fetchCapabilities,
    fetchCategories,
    searchThemes,
    analyzeMedia,
    getThemeRelationships,
    fetchAllThemes,
    fetchTheme,
    createTheme,
    updateTheme,
    deleteTheme,
    clearError,
    clearSearchResults,
  }
})
