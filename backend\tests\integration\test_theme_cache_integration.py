"""
Test the integration between Neo4j theme operations and Redis caching.
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from app.core.redis import RedisConnection
from app.services.theme_redis import ThemeRedisService
from app.crud.neo4j.theme import CRUDTheme
from app.services.theme_analysis import ThemeAnalysisService

# Test data
TEST_THEME = {
    "id": "theme_12345",
    "name": "Test Theme",
    "description": "A test theme",
    "status": "ACTIVE"
}

TEST_ANALYSIS = {
    "themes": [
        {
            "id": "mapping_12345",
            "theme": {
                "id": "theme_12345",
                "name": "Test Theme"
            },
            "mapping_type": "PRIMARY",
            "mapping_strength": 0.8
        }
    ],
    "primary_themes": [
        {
            "id": "mapping_12345",
            "theme": {
                "id": "theme_12345",
                "name": "Test Theme"
            },
            "mapping_type": "PRIMARY",
            "mapping_strength": 0.8
        }
    ],
    "confidence": 0.8,
    "metadata": {
        "genres": ["Action", "Adventure"],
        "tags": [{"name": "Magic", "category": "Setting"}]
    }
}

@pytest.fixture
def redis_mock():
    """Mock Redis client."""
    redis_instance = AsyncMock()
    redis_instance.pipeline.return_value = redis_instance
    redis_instance.execute.return_value = [True, True]
    return redis_instance

@pytest.fixture
def neo4j_session_mock():
    """Mock Neo4j session."""
    session = AsyncMock()
    # Configure the mock to return test data
    record_mock = MagicMock()
    record_mock.__getitem__.return_value = TEST_THEME
    session.run.return_value.single.return_value = record_mock
    return session

class TestThemeCacheIntegration:
    """Test the integration between Neo4j theme operations and Redis caching."""
    
    async def test_get_theme_with_cache(self, monkeypatch, redis_mock):
        """Test that getting a theme checks Redis cache first."""
        # Mock Redis get to return cached theme
        redis_mock.get.return_value = json.dumps(TEST_THEME)
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Test getting theme from cache
        cached_theme = await theme_redis.get_theme("theme_12345")
        
        # Verify Redis was called with correct key
        redis_mock.get.assert_called_once()
        assert cached_theme == TEST_THEME
    
    async def test_neo4j_crud_invalidates_cache(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test that Neo4j CRUD operations invalidate Redis cache."""
        # Mock Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Setup ThemeRedisService with invalidation spy
        theme_redis = ThemeRedisService()
        invalidate_spy = AsyncMock(return_value=2)
        monkeypatch.setattr(theme_redis, "invalidate_related_caches", invalidate_spy)
        
        # Patch the imported theme_redis in crud module to use our mock
        with patch("app.crud.neo4j.theme.theme_redis", theme_redis):
            # Create CRUDTheme instance
            crud_theme = CRUDTheme()
            
            # Test creating a relationship
            result = await crud_theme.create_relationship(
                neo4j_session_mock,
                source_type="story",
                source_id="21",
                theme_id="theme_12345",
                mapping_type="primary",
                mapping_strength=0.8
            )
            
            # Verify cache invalidation was called for both source and theme
            assert invalidate_spy.call_count == 2
            invalidate_spy.assert_any_call("story", "story_21")
            invalidate_spy.assert_any_call("theme", "theme_12345")
    
    async def test_theme_analysis_uses_cache(self, monkeypatch, redis_mock):
        """Test that theme analysis uses Redis cache."""
        # Mock Redis get to return cached analysis
        redis_mock.get.return_value = json.dumps(TEST_ANALYSIS)
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeAnalysisService instance
        theme_analysis = ThemeAnalysisService()
        
        # Patch theme_redis in analysis service
        theme_redis = ThemeRedisService()
        get_analysis_spy = AsyncMock(return_value=TEST_ANALYSIS)
        monkeypatch.setattr(theme_redis, "get_analysis", get_analysis_spy)
        monkeypatch.setattr(theme_redis, "set_analysis", AsyncMock())
        
        with patch("app.services.theme_analysis.theme_redis", theme_redis):
            # Run analysis with matching metadata to trigger cache hit
            result = await theme_analysis.analyze_story(
                source_type="story",
                source_id="21",
                genres=["Action", "Adventure"],
                tags=[{"name": "Magic", "category": "Setting"}]
            )
            
            # Verify cache was checked
            get_analysis_spy.assert_called_once_with("story", "story_21")
            assert result == TEST_ANALYSIS
    
    async def test_bulk_operations(self, monkeypatch, redis_mock):
        """Test bulk Redis operations for performance."""
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Create bulk operations
        operations = [
            {
                "operation": "set",
                "cache_type": "theme",
                "key": "theme_1",
                "value": {"id": "theme_1", "name": "Theme 1"}
            },
            {
                "operation": "set",
                "cache_type": "analysis",
                "key": "story:story_21",
                "value": {"themes": [{"id": "theme_1"}]}
            },
            {
                "operation": "delete",
                "cache_type": "theme",
                "key": "theme_2"
            }
        ]
        
        # Execute bulk operation
        result = await theme_redis.bulk_operation(operations)
        
        # Verify pipeline was used correctly
        assert redis_mock.pipeline.called
        assert redis_mock.execute.called
        
        # Two set operations, one delete
        assert result["set"] == 2
        assert result["delete"] == 1
        assert result["error"] == 0
    
    async def test_get_theme_with_fallback_cache_hit(self, monkeypatch, redis_mock):
        """Test the get_theme_with_fallback method when there's a cache hit."""
        # Mock Redis get to return cached theme
        redis_mock.get.return_value = json.dumps(TEST_THEME)
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Mock the Neo4j get method to ensure it's not called
        neo4j_spy = AsyncMock()
        monkeypatch.setattr(theme_redis.theme_neo4j, "get", neo4j_spy)
        
        # Create a mock session
        session_mock = AsyncMock()
        
        # Test getting theme with fallback - should use cache
        cached_theme = await theme_redis.get_theme_with_fallback(session_mock, "theme_12345")
        
        # Verify Redis was called with correct key and Neo4j was not called
        redis_mock.get.assert_called_once()
        neo4j_spy.assert_not_called()
        assert cached_theme == TEST_THEME
    
    async def test_get_theme_with_fallback_cache_miss(self, monkeypatch, redis_mock, neo4j_session_mock):
        """Test the get_theme_with_fallback method when there's a cache miss."""
        # Mock Redis get to return None (cache miss)
        redis_mock.get.return_value = None
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Mock the Neo4j get method to return a theme
        neo4j_spy = AsyncMock(return_value=TEST_THEME)
        monkeypatch.setattr(theme_redis.theme_neo4j, "get", neo4j_spy)
        
        # Mock the set_theme method to verify it's called
        set_theme_spy = AsyncMock()
        monkeypatch.setattr(theme_redis, "set_theme", set_theme_spy)
        
        # Test getting theme with fallback - should use Neo4j
        db_theme = await theme_redis.get_theme_with_fallback(neo4j_session_mock, "theme_12345")
        
        # Verify Redis was called, then Neo4j was called, and then the result was cached
        redis_mock.get.assert_called_once()
        neo4j_spy.assert_called_once_with(neo4j_session_mock, id="theme_12345") 
        set_theme_spy.assert_called_once_with("theme_12345", TEST_THEME)
        assert db_theme == TEST_THEME
    
    async def test_get_analysis_with_fallback_cache_hit(self, monkeypatch, redis_mock):
        """Test the get_analysis_with_fallback method when there's a cache hit."""
        # Mock Redis get to return cached analysis
        redis_mock.get.return_value = json.dumps(TEST_ANALYSIS)
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Mock the Neo4j get_analysis method to ensure it's not called
        neo4j_spy = AsyncMock()
        monkeypatch.setattr(theme_redis.theme_neo4j, "get_analysis", neo4j_spy)
        
        # Create a mock session
        session_mock = AsyncMock()
        
        # Test getting analysis with fallback - should use cache
        cached_analysis = await theme_redis.get_analysis_with_fallback(session_mock, "story", "21")
        
        # Verify Redis was called with correct key and Neo4j was not called
        redis_mock.get.assert_called_once()
        neo4j_spy.assert_not_called()
        assert cached_analysis == TEST_ANALYSIS
    
    async def test_get_analysis_with_fallback_cache_miss(self, monkeypatch, redis_mock, neo4j_session_mock):
        """Test the get_analysis_with_fallback method when there's a cache miss."""
        # Mock Redis get to return None (cache miss)
        redis_mock.get.return_value = None
        
        # Patch Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Mock the Neo4j get_analysis method to return analysis
        neo4j_spy = AsyncMock(return_value=TEST_ANALYSIS)
        monkeypatch.setattr(theme_redis.theme_neo4j, "get_analysis", neo4j_spy)
        
        # Mock the set_analysis method to verify it's called
        set_analysis_spy = AsyncMock()
        monkeypatch.setattr(theme_redis, "set_analysis", set_analysis_spy)
        
        # Test getting analysis with fallback - should use Neo4j
        db_analysis = await theme_redis.get_analysis_with_fallback(neo4j_session_mock, "story", "21")
        
        # Verify Redis was called, then Neo4j was called, and then the result was cached
        redis_mock.get.assert_called_once()
        neo4j_spy.assert_called_once_with(neo4j_session_mock, source_type="story", source_id="story_21")
        set_analysis_spy.assert_called_once()
        assert db_analysis == TEST_ANALYSIS

    async def test_warm_popular_theme_cache(self, monkeypatch, neo4j_session_mock, redis_mock):
        """Test the warm_popular_theme_cache method."""
        # Mock Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Create ThemeRedisService instance
        theme_redis = ThemeRedisService()
        
        # Mock the Neo4j run and record handling
        theme1 = {"id": "theme_popular1", "name": "Popular Theme 1"}
        theme2 = {"id": "theme_popular2", "name": "Popular Theme 2"}
        
        # Create mock records that match the structure expected by the method
        class MockRecord:
            def __init__(self, theme_data):
                self.data = {"t": theme_data}
                
            def __getitem__(self, key):
                return self.data[key]
        
        mock_records = [MockRecord(theme1), MockRecord(theme2)]
        
        # Mock the session.run().to_list() to return our mock records
        neo4j_session_mock.run.return_value.to_list = AsyncMock(return_value=mock_records)
        
        # Mock the set_theme method to verify it's called for each theme
        set_theme_spy = AsyncMock()
        monkeypatch.setattr(theme_redis, "set_theme", set_theme_spy)
        
        # Test warming the cache
        result = await theme_redis.warm_popular_theme_cache(neo4j_session_mock, 10)
        
        # Verify the Neo4j query was executed and themes were cached
        neo4j_session_mock.run.assert_called_once()
        assert "MATCH" in neo4j_session_mock.run.call_args[0][0]  # Verify a Cypher query was used
        assert "HAS_THEME" in neo4j_session_mock.run.call_args[0][0]  # Verify the relationship was used
        
        # Verify each theme was cached
        assert set_theme_spy.call_count == 2
        set_theme_spy.assert_any_call("theme_popular1", theme1)
        set_theme_spy.assert_any_call("theme_popular2", theme2)
        
        # Verify the return value is the number of themes cached
        assert result == 2 