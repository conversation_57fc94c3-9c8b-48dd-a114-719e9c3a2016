"""
Anime-specific theme mapper implementations.
"""
from typing import Dict, List, Any, Optional
import logging
from app.core.config import settings
from .interface import ThemeMapper
from .tag_combination_analyzer import TagCombinationAnalyzer

logger = logging.getLogger(__name__)

# Fixed anime genre to theme mappings
ANIME_GENRE_THEME_MAP = {
    "Action": ["action", "combat", "physical_conflict"],
    "Adventure": ["journey", "exploration", "quest"],
    "Comedy": ["humor", "comedy", "satire"],
    "Drama": ["emotional_conflict", "interpersonal_relationships", "tragedy"],
    "Fantasy": ["magic", "supernatural_elements", "alternate_worlds"],
    "Horror": ["fear", "suspense", "supernatural_horror"],
    "Mecha": ["technology", "robotics", "human_machine_interface"],
    "Music": ["performing_arts", "creative_expression", "musical_journey"],
    "Mystery": ["investigation", "suspense", "puzzle_solving"],
    "Psychological": ["mental_states", "psychological_conflict", "perception"],
    "Romance": ["love", "relationship_development", "emotional_intimacy"],
    "Sci-Fi": ["futuristic_technology", "space_exploration", "scientific_concepts"],
    "Slice of Life": ["everyday_life", "ordinary_experiences", "character_growth"],
    "Sports": ["competition", "teamwork", "personal_growth_physical"],
    "Supernatural": ["paranormal", "mystical_elements", "beyond_normal_reality"],
    "Thriller": ["suspense", "tension", "high_stakes"]
}

# Fixed anime tag to theme mappings
ANIME_TAG_THEME_MAP = {
    "Time Travel": ["time_manipulation", "temporal_displacement", "historical_changes"],
    "Isekai": ["transported_to_another_world", "fish_out_of_water", "adaptation"],
    "School": ["educational_setting", "coming_of_age", "peer_relationships"],
    "Magic": ["supernatural_powers", "magical_systems", "enchantment"],
    "Demons": ["supernatural_entities", "good_vs_evil", "corruption"],
    "Military": ["armed_conflict", "duty", "nationalism"],
    "Harem": ["romantic_competition", "wish_fulfillment", "multiple_love_interests"],
    "Historical": ["period_setting", "historical_events", "cultural_representation"],
    "Martial Arts": ["combat_techniques", "discipline", "physical_mastery"],
    "Super Power": ["extraordinary_abilities", "power_dynamics", "responsibility"],
}


class AnimeThemeMapperManual(ThemeMapper):
    """Manual implementation of theme mapping for anime."""
    
    def __init__(self):
        self.genre_map = ANIME_GENRE_THEME_MAP
        self.tag_map = ANIME_TAG_THEME_MAP
        self.tag_analyzer = TagCombinationAnalyzer()
    
    def map_to_universal_themes(self, media_info: Dict[str, Any]) -> List[str]:
        """Map anime genres and tags to universal themes."""
        themes = set()
        
        # Extract genres and tags from media_info
        genres = media_info.get("genres", [])
        tags = media_info.get("tags", [])
        
        # Basic mapping using existing approach
        for genre in genres:
            if genre in self.genre_map:
                themes.update(self.genre_map[genre])
        
        for tag in tags:
            tag_name = tag.get("name") if isinstance(tag, dict) else tag
            if tag_name in self.tag_map:
                themes.update(self.tag_map[tag_name])
        
        # Enhanced mapping using tag combination analyzer
        enhanced_themes = self._get_enhanced_themes(genres, tags)
        for theme in enhanced_themes:
            themes.add(theme["theme_id"])
        
        logger.debug(f"Mapped {len(themes)} themes for anime: {media_info.get('title', 'Unknown')}")
        return list(themes)
    
    def get_enhanced_theme_mapping(self, media_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get enhanced theme mapping using the tag combination analyzer.
        
        This method provides more detailed theme information than the standard
        map_to_universal_themes method, including confidence scores, mapping types,
        and contextual information.
        
        Args:
            media_info: Dictionary containing media metadata (genres, tags, etc.)
            
        Returns:
            List of detailed theme mapping dictionaries
        """
        genres = media_info.get("genres", [])
        tags = media_info.get("tags", [])
        
        try:
            # Get enhanced themes from the analyzer
            enhanced_themes = self.tag_analyzer.analyze_combinations(genres, tags)
            
            # Add source information
            for theme in enhanced_themes:
                theme["source"] = "tag_combination_analyzer"
                
            logger.debug(f"Generated {len(enhanced_themes)} enhanced themes for {media_info.get('title', 'Unknown')}")
            return enhanced_themes
        except Exception as e:
            logger.error(f"Error in enhanced theme mapping: {str(e)}")
            return []
    
    def _get_enhanced_themes(self, genres: List[str], tags: List[Any]) -> List[Dict[str, Any]]:
        """
        Get enhanced themes using the tag combination analyzer.
        
        Args:
            genres: List of genre names
            tags: List of tag objects
            
        Returns:
            List of theme dictionaries from the analyzer
        """
        try:
            return self.tag_analyzer.analyze_combinations(genres, tags)
        except Exception as e:
            logger.error(f"Error in tag combination analysis: {str(e)}")
            return []
    
    def get_confidence_scores(self, media_info: Dict[str, Any]) -> Dict[str, float]:
        """Get confidence scores for theme mappings."""
        themes = self.map_to_universal_themes(media_info)
        
        # Initialize with scores from basic approach
        confidence_scores = {}
        genres = media_info.get("genres", [])
        
        for theme in themes:
            # Check if theme came from a genre (higher confidence) or a tag
            is_from_genre = any(
                theme in genre_themes
                for genre in genres
                if genre in self.genre_map
                for genre_themes in [self.genre_map[genre]]
            )
            
            confidence_scores[theme] = 0.8 if is_from_genre else 0.6
        
        # Enhanced scoring from tag analyzer
        try:
            enhanced_themes = self._get_enhanced_themes(
                media_info.get("genres", []), 
                media_info.get("tags", [])
            )
            enhanced_scores = self.tag_analyzer.get_confidence_scores(enhanced_themes)
            
            # Merge scores, preferring enhanced scores when available
            for theme_id, score in enhanced_scores.items():
                confidence_scores[theme_id] = score
        except Exception as e:
            logger.error(f"Error getting enhanced confidence scores: {str(e)}")
        
        return confidence_scores
    
    def get_mapping_types(self, media_info: Dict[str, Any]) -> Dict[str, str]:
        """Determine mapping types (primary, secondary, etc.) for themes."""
        themes = self.map_to_universal_themes(media_info)
        confidence_scores = self.get_confidence_scores(media_info)
        
        # Initialize with basic approach
        mapping_types = {}
        for theme in themes:
            confidence = confidence_scores.get(theme, 0.0)
            
            # Simple threshold-based mapping type assignment
            if confidence >= 0.75:
                mapping_types[theme] = "primary"
            elif confidence >= 0.6:
                mapping_types[theme] = "secondary"
            elif confidence >= 0.4:
                mapping_types[theme] = "tertiary"
            else:
                mapping_types[theme] = "minor"
        
        # Enhanced mapping types from tag analyzer
        try:
            enhanced_themes = self._get_enhanced_themes(
                media_info.get("genres", []), 
                media_info.get("tags", [])
            )
            enhanced_types = self.tag_analyzer.get_mapping_types(enhanced_themes)
            
            # Merge types, preferring enhanced types when available
            for theme_id, mapping_type in enhanced_types.items():
                mapping_types[theme_id] = mapping_type
        except Exception as e:
            logger.error(f"Error getting enhanced mapping types: {str(e)}")
        
        return mapping_types
        
    def analyze_context(self, media_info: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        Analyze context for theme mappings.
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            Dictionary mapping theme identifiers to context descriptions
        """
        # Get enhanced themes with context information
        genres = media_info.get("genres", [])
        tags = media_info.get("tags", [])
        
        try:
            enhanced_themes = self._get_enhanced_themes(genres, tags)
            
            # Extract context information
            context_map = {}
            for theme in enhanced_themes:
                if "context" in theme and "theme_id" in theme:
                    context_map[theme["theme_id"]] = theme["context"]
            
            return context_map if context_map else None
        except Exception as e:
            logger.error(f"Error analyzing context: {str(e)}")
            return None


class AnimeThemeMapperAgent(ThemeMapper):
    """Agent-based implementation of theme mapping for anime."""
    
    def __init__(self):
        self.fallback_mapper = AnimeThemeMapperManual()
        self.agent_url = settings.AGENT_SERVICE_URL
        # We'll need to implement the HTTP client for the Agent Service
        # but for now, we'll use the manual mapper as a fallback
    
    def map_to_universal_themes(self, media_info: Dict[str, Any]) -> List[str]:
        """Map anime to universal themes using the Agent Service."""
        # TODO: Implement agent service call
        # For now, fall back to manual mapping
        logger.warning("Agent-based mapping not yet implemented. Using manual mapping as fallback.")
        return self.fallback_mapper.map_to_universal_themes(media_info)
    
    def get_confidence_scores(self, media_info: Dict[str, Any]) -> Dict[str, float]:
        """Get confidence scores from the Agent Service."""
        # TODO: Implement agent service call
        # For now, fall back to manual mapping
        return self.fallback_mapper.get_confidence_scores(media_info)
    
    def get_mapping_types(self, media_info: Dict[str, Any]) -> Dict[str, str]:
        """Get mapping types from the Agent Service."""
        # TODO: Implement agent service call
        # For now, fall back to manual mapping
        return self.fallback_mapper.get_mapping_types(media_info)
    
    def analyze_context(self, media_info: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """Analyze context using the Agent Service."""
        # TODO: Implement agent service call for context analysis
        # For now, return None
        return None 