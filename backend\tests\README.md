# Tahimoto Backend Testing

This directory contains the test suite for the <PERSON><PERSON><PERSON> backend. The tests are organized into several categories to ensure comprehensive coverage of all aspects of the system.

## Test Organization

The test suite is organized into the following directories:

### 1. Unit Tests (`/tests/unit/`)

Tests for pure functions with no external dependencies:
- Pydantic model serialization
- Utility functions
- Pure business logic

These tests use heavy mocking and have no external dependencies. They are fast and focused on isolated functionality.

See [Unit Testing README](unit/README.md) for details.

### 2. Component Tests (`/tests/components/`)

Tests for specific components with mocked dependencies:
- Search functionality
- Story retrieval
- Recommendations
- Neo4j queries
- Cache behavior
- Redis operations
- Theme cache management
- Scheduled tasks

These tests isolate specific components while mocking their dependencies to ensure focused testing.

See [Component Testing README](components/README.md) for details.

### 3. Infrastructure Tests (`/tests/infrastructure/`)

Tests for the fundamental infrastructure components:
- Neo4j database connectivity
- GraphQL resolver functionality
- GraphQL API
- AniList service integration

These tests verify that our infrastructure connections work correctly but don't test full business workflows.

See [Infrastructure Testing README](infrastructure/README.md) for details.

### 4. Integration Tests (`/tests/integration/`)

Tests for complete business workflows from end to end:
- API endpoints
- Search-to-display workflow
- Neo4j and Redis integration
- Theme cache integration
- Data flow across multiple components

These tests use real dependencies and verify that components work together properly.

See [Integration Testing README](integration/README.md) for details.

## Neo4j Testing Strategy

We use a multi-layered approach for testing Neo4j interactions:

### 1. Mocked Neo4j Operations (Unit & Component Tests)

For unit and component tests, we completely mock Neo4j operations:
- Mock the `AsyncSession` class and its methods
- Mock the CRUD operation results
- Mock query responses for predictable test data

Example:
```python
async def mock_get_story(*args, **kwargs):
    return {"id": "story_123", "title_english": "Test Story"}

# Then patch the actual method
with patch('app.crud.neo4j.story.CRUDStory.get', side_effect=mock_get_story):
    # Test code here
```

### 2. Container-Based Neo4j Testing (Integration Tests)

For integration tests, we can use actual Neo4j instances in containers:
- Tests run against a real database
- Containers are isolated from development databases
- Tests create and clean up their own data

### 3. Hybrid Approach

For API endpoint tests, we use a hybrid approach:
- Mock Neo4j sessions to avoid actual database operations
- Implement realistic mocking that simulates database behavior
- Mock specific functions like `get_neo4j_recommendations`

This approach allows for:
- Faster test execution
- No dependency on database state
- Realistic simulation of database behavior
- Testing of complex query logic without actual queries

See the [Integration Testing README](integration/README.md) for more details on Neo4j mocking strategies.

## Test Configuration

- `conftest.py`: Contains pytest fixtures and configuration
- `test_utils.py`: Common test utilities and helper functions

## Running Tests

### Using PowerShell Script (Recommended)

The most reliable way to run tests is using the consolidated test script, which supports different test types:

```powershell
# Run unit tests
.\tests\scripts\run_tests.ps1 -TestType unit

# Run component tests
.\tests\scripts\run_tests.ps1 -TestType component

# Run infrastructure tests
.\tests\scripts\run_tests.ps1 -TestType infrastructure

# Run integration tests with Docker support
.\tests\scripts\run_tests.ps1 -TestType integration -WithDocker

# Run all tests
.\tests\scripts\run_tests.ps1 -TestType all

# Run with specific test pattern
.\tests\scripts\run_tests.ps1 -TestType unit -TestPattern "serialization"

# Run with coverage reporting
.\tests\scripts\run_tests.ps1 -TestType unit -Coverage
```

### Manual Test Execution

For running specific tests during development:

```powershell
# Run a specific test file
docker exec tahimoto-backend python -m pytest tests/unit/test_pydantic_serialization.py -v

# Run tests with specific markers
docker exec tahimoto-backend python -m pytest -m "not slow" tests/components/ -v

# Run a specific test function
docker exec tahimoto-backend python -m pytest tests/components/test_search_component.py::TestSearchComponent::test_search_by_title -v
```

## Test Markers

We use pytest markers to categorize tests:

- `@pytest.mark.unit`: Pure unit tests with no external dependencies
- `@pytest.mark.component`: Component tests with mocked dependencies
- `@pytest.mark.infrastructure`: Tests for infrastructure connections
- `@pytest.mark.integration`: Tests that verify components work together
- `@pytest.mark.slow`: Tests that take longer to run (can be excluded with `-m "not slow"`)
- `@pytest.mark.container`: Tests that require Docker containers

## Test Dependencies

The tests rely on the following main dependencies:

- **pytest**: Testing framework
- **httpx**: Async HTTP client for API testing
- **unittest.mock**: For mocking dependencies
- **Neo4j**: Graph database
- **AniList API**: External anime data service
- **Redis**: For cache testing

## Continuous Integration

These tests are designed to run in CI/CD pipelines. The Docker-based execution ensures consistent test environments across development and CI systems. 