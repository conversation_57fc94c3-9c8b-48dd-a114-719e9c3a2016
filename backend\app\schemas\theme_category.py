from enum import Enum
from typing import List, Dict, Any

class ThemeCategory(str, Enum):
    """
    Enhanced categories for organizing themes across multiple media types.

    Core Categories (Universal):
    - MOOD: Emotional tone and atmosphere
    - NARRATIVE_STRUCTURE: Storytelling patterns and plot architecture
    - CHARACTER_DYNAMIC: Relationship patterns and character roles
    - SETTING_TYPE: Environment, world, and contextual framework

    Media-Specific Categories:
    - ANIME_SPECIFIC: Anime-unique thematic elements
    - TV_SPECIFIC: Television-specific narrative patterns
    - BOOK_SPECIFIC: Literary-specific thematic elements
    - MUSIC_SPECIFIC: Musical thematic elements and patterns

    Cross-Media Categories:
    - CULTURAL_CONTEXT: Cultural and societal themes
    - ARTISTIC_STYLE: Visual and artistic presentation themes
    - THEMATIC_DEPTH: Philosophical and conceptual depth
    - AUDIENCE_APPEAL: Target audience and accessibility themes
    """
    # Core universal categories
    MOOD = "MOOD"
    NARRATIVE_STRUCTURE = "NARRATIVE_STRUCTURE"
    CHARACTER_DYNAMIC = "CHARACTER_DYNAMIC"
    SETTING_TYPE = "SETTING_TYPE"

    # Media-specific categories
    ANIME_SPECIFIC = "ANIME_SPECIFIC"
    TV_SPECIFIC = "TV_SPECIFIC"
    BOOK_SPECIFIC = "BOOK_SPECIFIC"
    MUSIC_SPECIFIC = "MUSIC_SPECIFIC"

    # Cross-media categories
    CULTURAL_CONTEXT = "CULTURAL_CONTEXT"
    ARTISTIC_STYLE = "ARTISTIC_STYLE"
    THEMATIC_DEPTH = "THEMATIC_DEPTH"
    AUDIENCE_APPEAL = "AUDIENCE_APPEAL"

class MediaType(str, Enum):
    """Media types supported by the theme system."""
    ANIME = "ANIME"
    TV = "TV"
    BOOK = "BOOK"
    MUSIC = "MUSIC"
    MOVIE = "MOVIE"
    GAME = "GAME"

def get_all_categories() -> List[ThemeCategory]:
    """
    Get all available theme categories.

    Returns:
        List of all ThemeCategory enum values
    """
    return list(ThemeCategory)

def get_core_categories() -> List[ThemeCategory]:
    """
    Get core universal theme categories.

    Returns:
        List of core ThemeCategory enum values
    """
    return [
        ThemeCategory.MOOD,
        ThemeCategory.NARRATIVE_STRUCTURE,
        ThemeCategory.CHARACTER_DYNAMIC,
        ThemeCategory.SETTING_TYPE
    ]

def get_media_specific_categories() -> List[ThemeCategory]:
    """
    Get media-specific theme categories.

    Returns:
        List of media-specific ThemeCategory enum values
    """
    return [
        ThemeCategory.ANIME_SPECIFIC,
        ThemeCategory.TV_SPECIFIC,
        ThemeCategory.BOOK_SPECIFIC,
        ThemeCategory.MUSIC_SPECIFIC
    ]

def get_cross_media_categories() -> List[ThemeCategory]:
    """
    Get cross-media theme categories.

    Returns:
        List of cross-media ThemeCategory enum values
    """
    return [
        ThemeCategory.CULTURAL_CONTEXT,
        ThemeCategory.ARTISTIC_STYLE,
        ThemeCategory.THEMATIC_DEPTH,
        ThemeCategory.AUDIENCE_APPEAL
    ]

def get_categories_for_media_type(media_type: MediaType) -> List[ThemeCategory]:
    """
    Get relevant theme categories for a specific media type.

    Args:
        media_type: The media type to get categories for

    Returns:
        List of relevant ThemeCategory enum values
    """
    # All media types get core and cross-media categories
    categories = get_core_categories() + get_cross_media_categories()

    # Add media-specific category
    media_category_map = {
        MediaType.ANIME: ThemeCategory.ANIME_SPECIFIC,
        MediaType.TV: ThemeCategory.TV_SPECIFIC,
        MediaType.BOOK: ThemeCategory.BOOK_SPECIFIC,
        MediaType.MUSIC: ThemeCategory.MUSIC_SPECIFIC,
        MediaType.MOVIE: ThemeCategory.TV_SPECIFIC,  # Movies use TV-specific for now
        MediaType.GAME: ThemeCategory.ANIME_SPECIFIC,  # Games use anime-specific for now
    }

    if media_type in media_category_map:
        categories.append(media_category_map[media_type])

    return categories

def get_category_metadata() -> Dict[str, Dict[str, Any]]:
    """
    Get metadata for each theme category including descriptions and examples.

    Returns:
        Dictionary mapping category names to their metadata
    """
    return {
        "MOOD": {
            "description": "Emotional tone and atmosphere",
            "examples": ["cozy", "tense", "melancholic", "uplifting", "dark", "comedic"],
            "media_types": ["all"],
            "priority": 1
        },
        "NARRATIVE_STRUCTURE": {
            "description": "Storytelling patterns and plot architecture",
            "examples": ["hero_journey", "slice_of_life", "mystery", "episodic", "tournament"],
            "media_types": ["all"],
            "priority": 1
        },
        "CHARACTER_DYNAMIC": {
            "description": "Relationship patterns and character roles",
            "examples": ["rivalry", "mentorship", "friendship", "romance", "ensemble"],
            "media_types": ["all"],
            "priority": 1
        },
        "SETTING_TYPE": {
            "description": "Environment, world, and contextual framework",
            "examples": ["urban", "fantasy", "historical", "futuristic", "school", "workplace"],
            "media_types": ["all"],
            "priority": 1
        },
        "ANIME_SPECIFIC": {
            "description": "Anime-unique thematic elements",
            "examples": ["mecha", "magical_girl", "isekai", "shounen_power", "moe", "otaku_culture"],
            "media_types": ["anime", "game"],
            "priority": 2
        },
        "TV_SPECIFIC": {
            "description": "Television-specific narrative patterns",
            "examples": ["procedural", "anthology", "soap_opera", "sitcom", "drama_series"],
            "media_types": ["tv", "movie"],
            "priority": 2
        },
        "BOOK_SPECIFIC": {
            "description": "Literary-specific thematic elements",
            "examples": ["unreliable_narrator", "stream_of_consciousness", "epistolary", "metafiction"],
            "media_types": ["book"],
            "priority": 2
        },
        "MUSIC_SPECIFIC": {
            "description": "Musical thematic elements and patterns",
            "examples": ["concept_album", "instrumental_narrative", "lyrical_storytelling", "genre_fusion"],
            "media_types": ["music"],
            "priority": 2
        },
        "CULTURAL_CONTEXT": {
            "description": "Cultural and societal themes",
            "examples": ["japanese_culture", "western_values", "social_commentary", "historical_context"],
            "media_types": ["all"],
            "priority": 3
        },
        "ARTISTIC_STYLE": {
            "description": "Visual and artistic presentation themes",
            "examples": ["minimalist", "surreal", "realistic", "stylized", "experimental"],
            "media_types": ["all"],
            "priority": 3
        },
        "THEMATIC_DEPTH": {
            "description": "Philosophical and conceptual depth",
            "examples": ["existential", "psychological", "philosophical", "metaphysical", "abstract"],
            "media_types": ["all"],
            "priority": 3
        },
        "AUDIENCE_APPEAL": {
            "description": "Target audience and accessibility themes",
            "examples": ["family_friendly", "mature_themes", "niche_appeal", "mainstream", "cult_following"],
            "media_types": ["all"],
            "priority": 3
        }
    }