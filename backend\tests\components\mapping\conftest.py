"""
Fixtures for mapping component tests.

This module provides shared fixtures for testing theme mapping components.
"""
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, List, Any, Optional

from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer
from app.services.mapping.anime_mapper import AnimeThemeMapperManual
from app.services.mapping.interface import ThemeMapper


@pytest.fixture
def sample_tag_data():
    """Sample tag data for testing tag analysis."""
    return [
        {"name": "Psychological", "category": "Theme", "rank": 90},
        {"name": "Thriller", "category": "Genre", "rank": 85},
        {"name": "Mystery", "category": "Genre", "rank": 80},
        {"name": "School", "category": "Setting", "rank": 70},
        {"name": "Romance", "category": "Genre", "rank": 65},
        {"name": "Comedy", "category": "Genre", "rank": 60},
        {"name": "Slice of Life", "category": "Theme", "rank": 55},
        {"name": "Coming of Age", "category": "Theme", "rank": 75},
        {"name": "Tragedy", "category": "Theme", "rank": 70},
        {"name": "War", "category": "Setting", "rank": 65},
        {"name": "Historical", "category": "Setting", "rank": 60}
    ]


@pytest.fixture
def sample_genre_data():
    """Sample genre data for testing."""
    return [
        "Action",
        "Adventure",
        "Comedy",
        "Drama",
        "Fantasy",
        "Horror",
        "Mystery",
        "Psychological",
        "Romance",
        "Sci-Fi",
        "Slice of Life",
        "Supernatural",
        "Thriller"
    ]


@pytest.fixture
def sample_media_info(sample_tag_data, sample_genre_data):
    """Sample media info for testing mappers."""
    return {
        "id": "12345",
        "title": "Test Anime",
        "title_english": "Test Anime",
        "title_native": "テストアニメ",
        "description": "This is a test anime for unit testing",
        "genres": ["Psychological", "Thriller", "Mystery"],
        "tags": [
            {"name": "School", "category": "Setting", "rank": 80},
            {"name": "Tragedy", "category": "Theme", "rank": 75},
            {"name": "Coming of Age", "category": "Theme", "rank": 70}
        ],
        "status": "FINISHED",
        "episodes": 24,
        "duration": 24,
        "start_date": "2023-01-01",
        "end_date": "2023-03-30",
        "season": "WINTER",
        "average_score": 85,
        "popularity": 10000,
        "is_adult": False
    }


@pytest.fixture
def sample_complex_media_info(sample_tag_data):
    """More complex media info for testing pattern matching."""
    return {
        "id": "67890",
        "title": "Complex Test Anime",
        "title_english": "Complex Test Anime",
        "description": "This anime has multiple tag patterns for testing",
        "genres": ["Romance", "Comedy", "Slice of Life"],
        "tags": [
            {"name": "School", "category": "Setting", "rank": 90},
            {"name": "Iyashikei", "category": "Theme", "rank": 85},
            {"name": "Time Travel", "category": "Plot", "rank": 80},
            {"name": "Mystery", "category": "Genre", "rank": 75},
            {"name": "Dystopian", "category": "Setting", "rank": 70},
            {"name": "Sci-Fi", "category": "Genre", "rank": 65}
        ],
        "status": "RELEASING",
        "episodes": 12,
        "is_adult": False
    }


@pytest.fixture
def theme_combination_rules():
    """Sample theme combination rules for testing."""
    return [
        {
            "pattern": ["Thriller", "Psychological"],
            "result_theme": "psychological_thriller",
            "display_name": "Psychological Thriller",
            "confidence": 0.85,
            "theme_type": "mood",
            "description": "Dark exploration of the human mind under extreme stress"
        },
        {
            "pattern": ["School", "Romance", "Comedy"],
            "result_theme": "school_romantic_comedy",
            "display_name": "School Romantic Comedy",
            "confidence": 0.80,
            "theme_type": "genre_combination",
            "description": "Light-hearted romantic stories set in school environments"
        },
        {
            "pattern": ["Slice of Life", "Iyashikei"],
            "result_theme": "healing_slice_of_life",
            "display_name": "Healing Slice of Life",
            "confidence": 0.80,
            "theme_type": "mood",
            "description": "Calming stories that focus on peaceful everyday experiences"
        }
    ]


@pytest.fixture
def mock_tag_analyzer(theme_combination_rules):
    """Create a mock TagCombinationAnalyzer with configurable behavior."""
    analyzer = MagicMock(spec=TagCombinationAnalyzer)
    
    # Configure default returns
    analyzer.analyze_combinations.return_value = [
        {
            "theme_id": "psychological_thriller",
            "name": "Psychological Thriller",
            "confidence": 0.85,
            "mapping_type": "mood",
            "context": "Pattern match: Thriller, Psychological"
        }
    ]
    
    analyzer.get_confidence_scores.return_value = {
        "psychological_thriller": 0.85
    }
    
    analyzer.get_mapping_types.return_value = {
        "psychological_thriller": "mood"
    }
    
    # Add helper to configure the mock for specific media info
    def configure_for_media(media_info: Dict[str, Any], results: List[Dict[str, Any]]):
        """Configure the analyzer to return specific results for given media info."""
        def match_media_info(genres, tags):
            # Check if genres match
            if not set(genres).issubset(set(media_info.get("genres", []))):
                return False
                
            # Check if tags are subset (simplified check - just by name)
            media_tag_names = {t.get("name") for t in media_info.get("tags", []) if isinstance(t, dict)}
            input_tag_names = {t.get("name") for t in tags if isinstance(t, dict)}
            return input_tag_names.issubset(media_tag_names)
            
        # Set up the analyzer to return specific results when given matching media info
        def analyze_side_effect(genres, tags):
            if match_media_info(genres, tags):
                return results
            return []
            
        analyzer.analyze_combinations.side_effect = analyze_side_effect
        
        # Also set up confidence scores and mapping types
        confidence_scores = {theme["theme_id"]: theme["confidence"] for theme in results}
        mapping_types = {theme["theme_id"]: theme["mapping_type"] for theme in results}
        
        analyzer.get_confidence_scores.return_value = confidence_scores
        analyzer.get_mapping_types.return_value = mapping_types
        
    # Add the configuration method to the mock
    analyzer.configure_for_media = configure_for_media
    
    return analyzer


@pytest.fixture
def anime_mapper(mock_tag_analyzer):
    """Create an AnimeThemeMapperManual with mock analyzer for testing."""
    mapper = AnimeThemeMapperManual()
    mapper.tag_analyzer = mock_tag_analyzer
    return mapper 