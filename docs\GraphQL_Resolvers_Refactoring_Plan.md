# GraphQL Resolvers Refactoring Plan

## Overview

This document outlines the plan for refactoring the GraphQL resolvers in the Tahimoto application, with a focus on enhancing maintainability, facilitating pattern recognition features, and adhering to best practices. The current implementation in `backend/app/graphql/resolvers.py` is over 1200 lines, making it difficult to maintain and extend.

## Goals

1. **Improve Maintainability** - Split the monolithic resolvers file into domain-specific modules
2. **Enhance Code Organization** - Separate business logic from resolver functions
3. **Standardize Patterns** - Implement consistent error handling and ID standardization
4. **Prepare for Enhancement** - Structure code to support advanced pattern recognition features
5. **Maintain Functionality** - Ensure no regression in existing features during refactoring
6. **Improve Performance** - Optimize database queries and implement consistent caching

## Refactoring Phases

### Phase 1: Code Splitting and Organization

**Status: IN PROGRESS**

1. Split resolvers.py into domain-specific modules:
   - `theme_resolvers.py` - Theme-specific queries and mutations
   - `mapping_resolvers.py` - Theme mapping operations
   - `relationship_resolvers.py` - Theme relationship operations
   - `analysis_resolvers.py` - Analysis-related operations
   - `anime_resolvers.py` - Anime-specific operations
   - `recommendation_resolvers.py` - Recommendation operations

2. Create utilities module:
   - `formatters.py` - Data formatting helpers
   - `error_handlers.py` - Centralized error handling

3. Create central exports in `__init__.py` to maintain backward compatibility

### Phase 2: Extract Business Logic to Services

**Status: PLANNED**

1. Create dedicated service classes:
   - `ThemeMappingService`
   - `ThemeRelationshipService`
   - `PatternRecognitionService` (new)

2. Move complex business logic from resolvers to service classes
3. Inject services into resolvers to maintain separation of concerns

### Phase 3: Standardize Common Patterns

**Status: PLANNED**

1. Implement ID standardization utilities:
   - Create `id_standardization.py` with helpers for all entity types
   - Replace inline ID normalization with utility functions

2. Create consistent error handling:
   - Implement decorator pattern for all resolvers
   - Centralize logging and error reporting

3. Standardize database session management:
   - Create session handling utilities
   - Implement consistent transaction patterns

### Phase 4: Pattern Recognition Enhancement

**Status: PLANNED**

1. Implement `PatternRecognitionService` with:
   - Advanced pattern matching algorithms
   - Fuzzy matching for similar tags
   - Context-aware pattern recognition
   - Keyword extraction from descriptions
   - Negative pattern support
   - Rule complexity management

2. Add test endpoints for pattern recognition features
3. Integrate pattern recognition with existing theme analysis flow

### Phase 5: Performance Optimization

**STATUS: PLANNED**

1. Optimize Neo4j queries:
   - Review and optimize Cypher queries
   - Implement query parameterization

2. Enhance Redis caching strategy:
   - Standardize cache keys
   - Implement invalidation patterns
   - Add cache warming for frequently accessed data

3. Add performance monitoring:
   - Log query execution times
   - Implement tracing for complex operations

## Implementation Approach

To maintain functionality and minimize disruption, we will:

1. Make incremental changes with thorough testing at each step
2. Maintain backward compatibility throughout the refactoring
3. Use feature flags for new pattern recognition capabilities
4. Implement comprehensive test coverage for all refactored components
5. Document architectural decisions and patterns
6. Review changes collaboratively before deployment

## Progress Tracking

| Phase | Task | Status | Notes |
|-------|------|--------|-------|
| 1 | Create directory structure | ✅ Completed | Created resolvers/ and resolvers/utils/ directories |
| 1 | Split theme resolvers | ✅ Completed | Extracted theme resolvers to theme_resolvers.py |
| 1 | Split mapping resolvers | ✅ Completed | Extracted mapping resolvers to mapping_resolvers.py |
| 1 | Split relationship resolvers | ✅ Completed | Extracted relationship resolvers to relationship_resolvers.py |
| 1 | Split analysis resolvers | ✅ Completed | Extracted analysis resolvers to analysis_resolvers.py |
| 1 | Split anime resolvers | ✅ Completed | Extracted anime resolvers to anime_resolvers.py |
| 1 | Split recommendation resolvers | ✅ Completed | Extracted recommendation resolvers to recommendation_resolvers.py |
| 1 | Create formatting utilities | ✅ Completed | Extracted formatters to utils/formatters.py |
| 1 | Create error handling utilities | ✅ Completed | Created error_handlers.py with decorators and helper functions |
| 1 | Create ID standardization utilities | ✅ Completed | Created id_standardization.py with helpers for all entity types |
| 1 | Update __init__.py exports | ✅ Completed | Created backward-compatible exports |
| 2 | Extract business logic to services | 🟡 In Progress | Created ThemeRelationshipService and MediaQueryService |
| 2 | Apply utilities in resolvers | 🟡 In Progress | Integrated services with relationship, anime, and recommendation resolvers |
| 2 | Create PatternRecognitionService | ⚪ Not Started | |

## Implementation Notes

### Phase 1 Progress (2023-07-03)

We've successfully completed Phase 1 of our refactoring process by:

1. **Creating the directory structure** - Set up the new module structure with proper Python packages
2. **Extracting theme resolvers** - Moved all theme-related resolvers to a dedicated module
3. **Extracting mapping resolvers** - Moved all mapping-related resolvers to a dedicated module
4. **Extracting relationship resolvers** - Moved all relationship-related resolvers to a dedicated module
5. **Extracting analysis resolvers** - Moved all analysis-related resolvers to a dedicated module
6. **Extracting anime resolvers** - Moved all anime-related resolvers to a dedicated module
7. **Extracting recommendation resolvers** - Moved all recommendation-related resolvers to a dedicated module
8. **Creating formatting utilities** - Extracted common formatting functions to a utilities module
9. **Creating error handling utilities** - Created decorators and helper functions for standardized error handling
10. **Creating ID standardization utilities** - Added helpers for working with IDs across different entity types
11. **Maintaining backward compatibility** - Set up the __init__.py to re-export all resolvers

### Phase 2 Progress (2023-07-03)

We've made significant progress on Phase 2 of our refactoring plan:

1. **Created ThemeRelationshipService** - Developed a service that encapsulates theme relationship operations:
   - Extracted complex Neo4j query logic from resolvers
   - Standardized error handling
   - Implemented consistent ID standardization
   - Provided a clean interface for resolver functions

2. **Created MediaQueryService** - Developed a service that handles media entity operations:
   - Standardized media data fetching and formatting
   - Implemented type-specific formatters for different media types
   - Extracted recommendation logic from resolvers
   - Added support for external data sources via AniListService

3. **Refactored Multiple Resolver Modules** - Updated resolvers to use the new services:
   - Simplified relationship resolvers with ThemeRelationshipService
   - Streamlined anime resolvers with MediaQueryService
   - Enhanced recommendation resolvers with MediaQueryService
   - Applied error handling and logging decorators consistently
   - Improved docstrings with detailed parameter descriptions

The integration of these services has produced significant benefits:

1. **Reduced Code Duplication**: Complex query logic is now in dedicated service classes
2. **Improved Type Safety**: Better typing with more specific parameter and return types
3. **Enhanced Maintainability**: Clear separation of concerns between GraphQL and business logic
4. **Standardized Formatting**: Consistent data formatting across different resolver types
5. **Better Error Handling**: Robust error handling with standardized patterns

### Next Steps

Our next focus will be on:

1. **Create PatternRecognitionService**:
   - Build on the existing TagCombinationAnalyzer
   - Implement advanced pattern matching for themes
   - Support for fuzzy matching of similar tags
   - Add negative pattern support

2. **Refactor Analysis Resolvers**:
   - Update analysis resolvers to use the ThemeAnalysisService more effectively
   - Integrate with the new PatternRecognitionService
   - Apply consistent error handling and logging

3. **Enhance Test Coverage**:
   - Add unit tests for the new services
   - Add integration tests for refactored resolvers
   - Test error handling and edge cases 