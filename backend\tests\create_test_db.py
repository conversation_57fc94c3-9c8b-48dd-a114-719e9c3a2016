import asyncio
import asyncpg

async def create_test_database():
    """Create test database if it doesn't exist."""
    try:
        # Connect to default database to create test database
        conn = await asyncpg.connect(
            user='user',
            password='password',
            database='storytime',
            host='db'
        )
        
        # Check if database exists
        result = await conn.fetch(
            "SELECT 1 FROM pg_database WHERE datname = 'test_storytime'"
        )
        
        if not result:
            # Create test database
            await conn.execute('CREATE DATABASE test_storytime')
            print("Created test database 'test_storytime'")
        else:
            print("Test database 'test_storytime' already exists")
        
        await conn.close()
        
    except Exception as e:
        print(f"Error creating test database: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(create_test_database()) 