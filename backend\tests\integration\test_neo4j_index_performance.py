"""
Integration tests for Neo4j index performance.

This module contains tests that measure the performance impact of Neo4j indexes
on common query patterns. These tests require a connection to a Neo4j database.
"""
import pytest
import time
import asyncio
import statistics
from neo4j import AsyncGraphDatabase

from app.core.config import settings
from app.db.neo4j.indexes import create_indexes, drop_all_indexes


@pytest.mark.integration
class TestNeo4jIndexPerformance:
    """Integration tests for Neo4j index performance."""
    
    @pytest.fixture
    async def neo4j_driver(self):
        """Create a Neo4j driver for testing."""
        driver = AsyncGraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
        )
        yield driver
        await driver.close()
    
    async def run_query_with_timing(self, driver, query, params=None, iterations=3):
        """Run a query multiple times and return average execution time."""
        execution_times = []
        
        async with driver.session() as session:
            for _ in range(iterations):
                start_time = time.time()
                result = await session.run(query, params or {})
                # Consume the result to ensure query execution is complete
                records = []
                async for record in result:
                    records.append(record)
                execution_time = time.time() - start_time
                execution_times.append(execution_time)
        
        # Return mean and standard deviation
        return {
            "mean": statistics.mean(execution_times),
            "stdev": statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
            "min": min(execution_times),
            "max": max(execution_times),
            "iterations": iterations,
            "record_count": len(records)
        }
    
    async def test_story_id_index_performance(self, neo4j_driver):
        """Test performance improvement for story ID lookups."""
        # Drop all indexes to start clean
        await drop_all_indexes()
        
        # Define test query that should benefit from story_id index
        test_query = """
        MATCH (s:Story)
        WHERE s.id = $story_id
        RETURN s
        """
        params = {"story_id": "story_21"}  # One Piece
        
        # Run query without indexes
        no_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Without index: {no_index_stats}")
        
        # Create indexes
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Run query with indexes
        with_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"With index: {with_index_stats}")
        
        # Assert performance improvement
        assert with_index_stats["mean"] < no_index_stats["mean"], \
            f"Query with indexes ({with_index_stats['mean']:.4f}s) should be faster than without ({no_index_stats['mean']:.4f}s)"
    
    async def test_media_type_filter_performance(self, neo4j_driver):
        """Test performance improvement for media type filtering."""
        # Drop all indexes to start clean
        await drop_all_indexes()
        
        # Define test query that should benefit from media_type index
        test_query = """
        MATCH (s:Story)
        WHERE s.media_type = $media_type
        RETURN s
        LIMIT 20
        """
        params = {"media_type": "ANIME"}
        
        # Run query without indexes
        no_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Without index: {no_index_stats}")
        
        # Create indexes
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Run query with indexes
        with_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"With index: {with_index_stats}")
        
        # Assert performance improvement
        assert with_index_stats["mean"] < no_index_stats["mean"], \
            f"Query with indexes ({with_index_stats['mean']:.4f}s) should be faster than without ({no_index_stats['mean']:.4f}s)"
    
    async def test_theme_relationship_performance(self, neo4j_driver):
        """Test performance improvement for theme relationship queries."""
        # Drop all indexes to start clean
        await drop_all_indexes()
        
        # Define test query that should benefit from relationship indexes
        test_query = """
        MATCH (s:Story)-[r:HAS_THEME]->(t:Theme)
        WHERE r.strength > $min_strength
        RETURN s, t, r
        LIMIT 20
        """
        params = {"min_strength": 0.7}
        
        # Run query without indexes
        no_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Without index: {no_index_stats}")
        
        # Create indexes
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Run query with indexes
        with_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"With index: {with_index_stats}")
        
        # Assert performance improvement
        assert with_index_stats["mean"] < no_index_stats["mean"] * 1.5, \
            f"Query with indexes ({with_index_stats['mean']:.4f}s) should be faster than without ({no_index_stats['mean']:.4f}s)"
    
    async def test_combined_filter_performance(self, neo4j_driver):
        """Test performance improvement for combined property filters."""
        # Drop all indexes to start clean
        await drop_all_indexes()
        
        # Define test query that should benefit from combined index
        test_query = """
        MATCH (s:Story)
        WHERE s.media_type = $media_type AND s.score > $min_score
        RETURN s
        ORDER BY s.score DESC
        LIMIT 10
        """
        params = {"media_type": "ANIME", "min_score": 80}
        
        # Run query without indexes
        no_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Without index: {no_index_stats}")
        
        # Create indexes
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Run query with indexes
        with_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"With index: {with_index_stats}")
        
        # Assert performance improvement
        assert with_index_stats["mean"] < no_index_stats["mean"], \
            f"Query with indexes ({with_index_stats['mean']:.4f}s) should be faster than without ({no_index_stats['mean']:.4f}s)"
    
    async def test_recommendation_query_performance(self, neo4j_driver):
        """Test performance improvement for story recommendation queries."""
        # Drop all indexes to start clean
        await drop_all_indexes()
        
        # Define test query for recommendations with UNION
        test_query = """
        // Start by finding recommendations from explicit RECOMMENDS relationships
        MATCH (s:Story)-[r:RECOMMENDS]->(rec:Story)
        WHERE s.id = $story_id
        
        // Apply recommendation filters
        WHERE rec.status = $status AND rec.average_score >= $min_score
        
        // Return detailed recommendation information with priority for explicit recommendations
        RETURN rec, r.strength AS strength, r.source AS source, 1 AS priority
        
        UNION
        
        // Find thematically similar stories through shared themes
        MATCH (s:Story {id: $story_id})-[r1:HAS_THEME]->(t:Theme)<-[r2:HAS_THEME]-(rec:Story)
        WHERE s.id <> rec.id
        AND rec.status = $status AND rec.average_score >= $min_score
        
        // Calculate thematic similarity
        WITH rec, sum(r1.mapping_strength * r2.mapping_strength) / count(t) AS theme_strength, 
             'thematic' AS source, 2 AS priority
        WHERE theme_strength >= 0.1
        
        // Return thematic recommendations
        RETURN rec, theme_strength AS strength, source, priority
        
        // Order results, giving priority to explicit recommendations
        ORDER BY priority, strength DESC
        LIMIT $limit
        """
        params = {
            "story_id": "story_21",  # One Piece
            "status": "FINISHED",
            "min_score": 70,
            "limit": 10
        }
        
        # Run query without indexes
        no_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Recommendation query without index: {no_index_stats}")
        
        # Create indexes
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Run query with indexes
        with_index_stats = await self.run_query_with_timing(neo4j_driver, test_query, params)
        print(f"Recommendation query with index: {with_index_stats}")
        
        # Assert performance improvement
        assert with_index_stats["mean"] < no_index_stats["mean"] * 1.5, \
            f"Recommendation query with indexes ({with_index_stats['mean']:.4f}s) should be faster than without ({no_index_stats['mean']:.4f}s)" 