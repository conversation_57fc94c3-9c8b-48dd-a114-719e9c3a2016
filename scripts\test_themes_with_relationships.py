import requests
import json

def test_themes_query():
    url = "http://localhost:3000/graphql"
    query = """
    query GetThemesWithRelationships {
      themes {
        id
        name
        description
        category
        confidence
        status
      }
      themeRelationships {
        id
        sourceId
        sourceName
        targetId
        targetName
        type
        strength
        tension
      }
    }
    """
    
    # Prepare the request
    payload = {"query": query}
    headers = {"Content-Type": "application/json"}
    
    # Send the request
    response = requests.post(url, json=payload, headers=headers)
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print("Response:")
    print(json.dumps(response.json(), indent=2))
    
    # Print the structure that would be accessed in the ThemeBrowser
    if response.status_code == 200:
        data = response.json()
        # Check how the data is structured
        print("\nResponse Data Structure:")
        for key in data:
            print(f"- {key}")
            
        # Check if themes are directly in the response or in data.data
        if "data" in data:
            data_obj = data["data"]
            print("\nData Object Structure:")
            for key in data_obj:
                print(f"- {key}")
                
            if "themes" in data_obj:
                print(f"\nThemes count: {len(data_obj['themes'])}")
        else:
            print("\nNo 'data' field in response")
            if "themes" in data:
                print(f"\nThemes count: {len(data['themes'])}")
    
    return response.json()

if __name__ == "__main__":
    test_themes_query() 