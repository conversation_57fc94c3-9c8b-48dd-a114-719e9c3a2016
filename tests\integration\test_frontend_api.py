#!/usr/bin/env python3
"""
Test the frontend API integration with contextual themes.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_frontend_theme_analysis():
    """Test the API call that the frontend will make."""
    print("🎯 Testing Frontend Theme Analysis Integration")
    print("=" * 60)
    
    # This simulates what the frontend will call
    anime_id = "21202"  # The Apothecary Diaries
    request_data = {
        "anime_id": anime_id,
        "include_reasoning": True
    }
    
    try:
        print(f"1. Testing contextual theme analysis for anime {anime_id}...")
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data['theme_matches'])} themes")
            
            # Simulate the frontend conversion
            mappings = []
            for match in data['theme_matches']:
                mapping = {
                    'theme_id': match.get('theme_id', 'unknown'),
                    'theme_name': match['theme_name'],
                    'theme_category': match.get('theme_category', 'CONTEXTUAL'),
                    'mapping_strength': match.get('confidence', 0),  # Use float confidence directly (0-1 range)
                    'mapping_type': 'CONTEXTUAL',
                    'source': 'contextual_analysis',
                    'context': '; '.join(match.get('reasoning', [])) if match.get('reasoning') else 'Contextual theme analysis'
                }
                mappings.append(mapping)
            
            print(f"\n   📊 Converted mappings for frontend:")
            for i, mapping in enumerate(mappings, 1):
                print(f"   {i}. {mapping['theme_name']}")
                print(f"      Category: {mapping['theme_category']}")
                print(f"      Strength: {mapping['mapping_strength']:.1%}")
                print(f"      Context: {mapping['context'][:100]}...")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_anime_metadata_retrieval():
    """Test if we can get anime metadata for the contextual analysis."""
    print(f"\n🎯 Testing Anime Metadata Retrieval")
    print("=" * 50)
    
    anime_id = "21202"
    
    try:
        # Test if we can get anime data from the stories endpoint
        response = requests.get(f"{BASE_URL}/stories/{anime_id}", timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            anime = response.json()
            print(f"   ✅ Anime data retrieved:")
            print(f"      Title: {anime.get('title_english', anime.get('title_romaji', 'Unknown'))}")
            print(f"      Genres: {anime.get('genres', [])}")
            print(f"      Tags: {len(anime.get('tags', []))} tags")
            
            # Check if we have the data needed for contextual analysis
            required_fields = ['title_romaji', 'genres', 'tags']
            missing_fields = [field for field in required_fields if not anime.get(field)]
            
            if missing_fields:
                print(f"   ⚠️  Missing fields for contextual analysis: {missing_fields}")
            else:
                print(f"   ✅ All required fields present for contextual analysis")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_frontend_theme_analysis()
    test_anime_metadata_retrieval()
