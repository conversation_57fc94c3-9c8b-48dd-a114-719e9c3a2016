"""
GraphQL resolvers package.

This module exports all resolvers to maintain backward compatibility
during the refactoring process.
"""
import logging
from ariadne import QueryType, MutationType, ObjectType, ScalarType

# Initialize main resolver objects
query_resolvers = QueryType()
mutation_resolvers = MutationType()

# Import resolver modules
# This will register all resolvers with their respective types
from app.graphql.resolvers.theme_resolvers import (
    resolve_theme,
    resolve_themes,
    resolve_theme_stats,
    resolve_create_theme,
    resolve_update_theme,
    resolve_delete_theme
)

# Import mapping resolvers
from app.graphql.resolvers.mapping_resolvers import (
    resolve_theme_mapping,
    resolve_theme_mappings,
    resolve_create_theme_mapping,
    resolve_update_theme_mapping,
    resolve_delete_theme_mapping,
    resolve_approve_theme_mapping,
    resolve_reject_theme_mapping,
    resolve_request_review
)

# Import relationship resolvers
from app.graphql.resolvers.relationship_resolvers import (
    resolve_theme_relationships,
    resolve_create_theme_relationship,
    resolve_update_theme_relationship,
    resolve_delete_theme_relationship
)

# Import analysis resolvers
from app.graphql.resolvers.analysis_resolvers import (
    resolve_analyze_media,
    resolve_analyze_story_themes
)

# Import anime resolvers
from app.graphql.resolvers.anime_resolvers import (
    resolve_anime,
    resolve_similar_anime
)

# Import recommendation resolvers
from app.graphql.resolvers.recommendation_resolvers import (
    resolve_recommend_crossmedia
)

# Export all resolvers
__all__ = ["query_resolvers", "mutation_resolvers"]
