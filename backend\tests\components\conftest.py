# backend/tests/infrastructure/conftest.py

import pytest
import asyncio
from app.db.neo4j_session import driver, init_db
from app.core.config import settings
from app.services.anilist import anilist_service
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

# Mark all tests in this directory as component tests
pytest.mark.component = pytest.mark.component

@pytest.fixture(scope="session", autouse=True)
async def setup_neo4j():
    """Initialize Neo4j database and verify connectivity."""
    # Initialize the database
    await init_db()
    yield
    # No cleanup needed - the connection will be closed with the application

@pytest.fixture
async def neo4j_session():
    """Create a Neo4j session for testing."""
    async with driver.session() as session:
        yield session

@pytest.fixture
async def clean_neo4j_database(neo4j_session):
    """Clean the Neo4j database before and after tests."""
    # Clean before test
    await neo4j_session.run("MATCH (n) WHERE NOT n:_Schema DETACH DELETE n")
    yield
    # Clean after test
    await neo4j_session.run("MATCH (n) WHERE NOT n:_Schema DETACH DELETE n")

@pytest.fixture
def anilist():
    """Provide the AniList service for testing."""
    return anilist_service

@pytest.fixture
async def graphql_context(neo4j_session):
    """Create a GraphQL context for resolver testing."""
    return {
        "session": neo4j_session,
        "anilist_service": anilist_service,
    }

@pytest.fixture
def mock_neo4j_transaction():
    """Create a mock Neo4j transaction for component tests."""
    mock = AsyncMock()
    
    # Configure the run method
    async def mock_run(*args, **kwargs):
        mock_result = AsyncMock()
        mock_result.__aiter__.return_value = []
        return mock_result
        
    mock.run.side_effect = mock_run
    return mock

@pytest.fixture
def mock_story_data():
    """Create mock story data for component tests."""
    return {
        "id": "story_21",
        "title": "One Piece",
        "description": "Test description",
        "image_url": "http://example.com/image.jpg",
        "themes": ["Action", "Adventure", "Pirates", "Shounen"],
        "source": "anilist",
        "source_id": 21,
        "last_updated": "2023-01-01T00:00:00"
    }

@pytest.fixture
def mock_multiple_stories():
    """Create multiple mock stories for testing."""
    return [
        {
            "id": "story_21",
            "title": "One Piece",
            "description": "Test description for One Piece",
            "image_url": "http://example.com/onepiece.jpg",
            "themes": ["Action", "Adventure", "Pirates", "Shounen"],
            "source": "anilist",
            "source_id": 21,
            "last_updated": "2023-01-01T00:00:00"
        },
        {
            "id": "story_5",
            "title": "Cowboy Bebop",
            "description": "Test description for Cowboy Bebop",
            "image_url": "http://example.com/cowboybebop.jpg",
            "themes": ["Action", "SciFi", "Space", "Noir"],
            "source": "anilist",
            "source_id": 5,
            "last_updated": "2023-01-01T00:00:00"
        },
        {
            "id": "story_31240",
            "title": "Re:Zero",
            "description": "Test description for Re:Zero",
            "image_url": "http://example.com/rezero.jpg",
            "themes": ["Fantasy", "Isekai", "Psychological", "Drama"],
            "source": "anilist",
            "source_id": 31240,
            "last_updated": "2023-01-01T00:00:00"
        }
    ]

@pytest.fixture
def configure_mock_story_search():
    """Configure a mock Neo4j session to return story search results."""
    def _configure(mock_session, stories_to_return: List[Dict[str, Any]]):
        """
        Configure the mock Neo4j session to return specific stories for search queries.
        
        Args:
            mock_session: The mock Neo4j session
            stories_to_return: List of story dictionaries to return
        """
        # Create mock records from the stories
        story_records = []
        for story in stories_to_return:
            record = MagicMock()
            record["s"] = story
            story_records.append(record)
            
        # Configure the session.run method
        async def mock_run(query, **params):
            mock_result = AsyncMock()
            
            # Return story records for search queries
            if "MATCH (s:Story)" in query:
                mock_result.__aiter__.return_value = story_records
            else:
                mock_result.__aiter__.return_value = []
                
            return mock_result
            
        mock_session.run.side_effect = mock_run
        return mock_session
        
    return _configure

@pytest.fixture
def mock_cache_service():
    """Create a mock cache service for component tests."""
    mock = MagicMock()
    
    # Configure common cache methods
    mock.get = AsyncMock(return_value=None)
    mock.set = AsyncMock(return_value=True)
    mock.delete = AsyncMock(return_value=1)
    mock.exists = AsyncMock(return_value=False)
    mock.get_json = AsyncMock(return_value=None)
    mock.set_json = AsyncMock(return_value=True)
    
    return mock