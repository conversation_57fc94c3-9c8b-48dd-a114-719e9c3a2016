# Tahimoto - Cross-Media Story Recommendation Platform

## Overview

Tahimoto is an intelligent recommendation platform that helps users discover stories across different media types (anime, books, movies, etc.) based on universal themes and storytelling elements. The system uses a graph database (Neo4j), a GraphQL API (Ariadne on FastAPI), and a dedicated agent service (planned) powered by LLMs for advanced thematic analysis and cross-media recommendations.

## Key Features

- **Universal Theme Mapping:** Translates media-specific genres and tags into universal themes using a combination of pre-defined mappings and LLM-powered analysis.
- **Theme Categorization System:** Organizes themes into four primary dimensions (Mood, Narrative Structure, Character Dynamic, Setting Type) to create a comprehensive thematic profile.
- **Living Framework Approach:** Implements a theme ontology designed to continuously evolve and adapt over time rather than remaining a static taxonomy.
- **Cross-Media Recommendations:** Find similar stories across different media types (anime, books, movies, etc.) based on thematic similarity.
- **Agent-Powered Reasoning:** Leverages a dedicated agent service with persistent memory (SSM/Transformers) for in-depth thematic analysis and relationship discovery.
- **GraphQL API:** Provides a flexible and efficient interface for querying data using Ariadne on FastAPI.
- **Graph Database (Neo4j):** Stores story data and relationships in a graph structure, optimized for traversing thematic connections.
- **Caching System:** Uses Redis for improved performance.
- **Admin Dashboard:** Provides tools for managing and reviewing theme mappings, including a visual theme browser for exploring theme relationships.
- **Flexible Architecture:** Designed to work with or without the Agent Service, using a formal interface for theme mapping that supports both manual and agent-based implementations.

## Architecture

Tahimoto follows a microservice architecture:

1. **Frontend (React + TypeScript):** Provides the user interface for searching, browsing, and viewing recommendations. Includes an admin dashboard with theme visualization tools.
2. **Backend (FastAPI + Ariadne):** Exposes a GraphQL API for the frontend and communicates with the Agent Service and Neo4j database. Houses the ThemeMapper interface with implementations for both manual and agent-based mapping.
3. **Agent Service (FastAPI):** Performs thematic analysis, reasoning, and real-time learning using LLMs and persistent memory. Communicates with the Neo4j database.
4. **Database (Neo4j):** Stores story metadata, themes, and relationships as a graph.
5. **Cache (Redis):** Caches API responses and frequently accessed data.

See [Architecture](docs/Architecture%20update.md) for more details.

## Theme Categorization System

Tahimoto organizes themes into four primary dimensions that capture different aspects of media content:

### MOOD
The emotional tone, atmosphere, and feeling evoked by the content (e.g., Cozy, Tense, Melancholic, Uplifting).

### NARRATIVE_STRUCTURE
The patterns and frameworks that shape how a story is told (e.g., Journey, Coming of Age, Mystery, Episodic).

### CHARACTER_DYNAMIC
The patterns of relationships and interactions between characters (e.g., Found Family, Rivalry, Mentor-Student).

### SETTING_TYPE
The environment and contextual framework in which the story takes place (e.g., School Life, Fantasy World, Urban Modern).

These dimensions exist in parallel and intersect to create the full thematic profile of any story. For detailed specifications, see [Core Theme Categories and Taxonomy](docs/Core%20Theme%20Categories%20and%20Taxonomy%20-%20Detailed%20Specifications.md).

## Theme Taxonomy Implementation Status

As of March 2025, we have made significant progress in implementing our theme taxonomy system:

- **Enhanced Data Model**: Our theme data model now includes multi-dimensional analysis capability, evolution tracking, cultural context awareness, and expanded relationship types.

- **Relationship Visualization**: We've implemented a comprehensive theme relationship browser that visualizes connections between themes with directional arrows, different line styles for relationship types, and interactive filtering.

- **Admin Components**: Several specialized components have been developed:
  - **ThemeEditor**: For creating and editing themes with support for all taxonomy features
  - **RelationshipEditor**: For managing relationships between themes with strength, tension, and dimensional context
  - **ThemeRelationshipGraph**: For visualizing the complex web of theme connections across different dimensions

- **Interactive Filtering**: Users can filter themes and relationships by category, relationship type, and dimension to explore specific aspects of the taxonomy.

These implementations directly support our "living framework" approach, allowing the taxonomy to evolve organically while maintaining structure. For complete details about our implementation progress, see the [Thematic Mapping Enhancement Implementation](Thematic%20Mapping%20Enhancement%20Implementation.md) document.

## ID Standardization

To ensure consistency and maintainability across the system, we follow a strict ID standardization approach. All entities in the system (themes, stories, mappings, relationships) use standardized ID formats with appropriate prefixes.

For detailed information on our ID formatting conventions, validation processes, and best practices, see the [ID Standardization Guide](docs/ID_Standardization_Guide.md).

## Database Implementation

Tahimoto uses Neo4j as its graph database for several key reasons:

### Graph Data Model Benefits

- **Relationship-First Design**: Naturally represents thematic connections between stories
- **Efficient Traversal**: Fast queries across complex relationship patterns
- **Flexible Schema**: Easily adaptable as new media types and properties are added
- **Query Performance**: Optimized for finding patterns and connections in data

### Neo4j Implementation

The backend interacts with Neo4j through a well-defined workflow:

1. **Session Management**: Neo4j sessions are provided via dependency injection
2. **GraphQL Resolvers**: Receive database session from request context
3. **CRUD Operations**: Execute Cypher queries and transform results for GraphQL schema
4. **Cypher Queries**: Optimized graph traversal for theme mapping and recommendations

Example Cypher query for cross-media recommendations:
```cypher
MATCH (s1:Story {id: $story_id})-[r1:HAS_THEME]->(t:Theme)<-[r2:HAS_THEME]-(s2:Story)
WHERE s1.media_type <> s2.media_type
RETURN s2, sum(r1.strength * r2.strength) AS similarity
ORDER BY similarity DESC
LIMIT 10
```

See [Database Workflow](docs/Architecture%20update.md#neo4j-database-workflow) for more details.

## Tech Stack

### Backend
- **FastAPI:** Modern, high-performance web framework
- **Ariadne:** Schema-first GraphQL implementation for Python
- **Neo4j:** Graph database for storing story data and relationships
- **Redis:** In-memory data store for caching
- **Python 3.11+:** With type hints and modern features
- **uv:** Fast Python package installer and environment manager

### Frontend
- **React + TypeScript:** Component-based UI framework with type safety
- **Astro:** Fast, modern static site generator with React integration
- **Tailwind CSS:** Utility-first CSS framework for rapid UI development

### Infrastructure
- **Docker + Docker Compose:** Containerization for consistent environments
- **Prometheus + Grafana (planned):** Monitoring and metrics
- **Sentry:** Error tracking and performance monitoring

## Getting Started

### Prerequisites
- Docker and Docker Compose (recommended)
- Alternatively: Python 3.11+, Node.js 18+, and uv

### Quick Start with Docker

1. Clone the repository:
```bash
git clone https://github.com/calmren/tahimoto.git
cd tahimoto
```

2. Start the containers:
```bash
docker-compose up -d
```

3. Access the services:
- Frontend: http://localhost:3000
- Admin Dashboard: http://localhost:3000/admin
- Backend GraphQL API: http://localhost:8000/graphql
- Backend API docs: http://localhost:8000/docs
- Neo4j Browser: http://localhost:7474 (username: neo4j, password: password)

### Manual Setup for Development

#### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Run the setup script to initialize your environment (installs uv, creates a virtual environment, and installs dependencies):
```bash
python setup.py
```

3. Activate the virtual environment:
```bash
# On Windows
.venv\Scripts\activate

# On macOS/Linux
source .venv/bin/activate
```

4. Start the backend server:
```bash
uvicorn app.main:app --reload
```

The backend API will be available at http://localhost:8000

#### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at http://localhost:3000

### Environment Configuration

The backend uses environment variables for configuration. You can create a `.env` file in the backend directory based on the provided `.env.sample`:

```env
# Tahimoto Backend Environment Variables
DEBUG=True
LOG_LEVEL=DEBUG

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Agent Service Configuration
USE_AGENT_MAPPING=False
AGENT_SERVICE_URL=http://localhost:8001/api
```

## Project Structure

### Backend Structure

```
backend/
├── app/
│   ├── api/          # REST API endpoints
│   ├── core/         # Core functionality (config, logging)
│   ├── db/           # Database connection management (Neo4j)
│   ├── graphql/      # GraphQL schema and resolvers
│   │   ├── schema.graphql  # GraphQL schema definition
│   │   ├── resolvers.py    # GraphQL resolvers
│   │   └── schema.py       # Ariadne schema binding
│   ├── schemas/      # Pydantic schemas for validation
│   └── services/     # Business logic
│       └── mapping/  # Theme mapping services
│           ├── interface.py   # ABC interface for mappers
│           ├── anime_mapper.py # Anime-specific mappers
│           ├── tag_combination_analyzer.py # Tag analysis service
│           └── factory.py     # Factory for getting mappers
├── .env.sample       # Sample environment variables
├── Dockerfile.dev    # Development Dockerfile
├── requirements.txt  # Python dependencies
└── setup.py          # Environment setup script
```

### Frontend Structure

```
frontend-vue/
├── src/
│   ├── components/   # Vue components
│   │   └── admin/    # Admin dashboard components
│   ├── views/        # Vue page components
│   ├── router/       # Vue Router configuration
│   ├── stores/       # Pinia state management
│   ├── services/     # API services and utilities
│   ├── types/        # TypeScript type definitions
│   └── style.css     # Global styles
├── public/           # Static assets
├── vite.config.ts    # Vite configuration
├── tailwind.config.js # Tailwind CSS configuration
├── package.json      # Node.js dependencies
└── Dockerfile.dev    # Development Dockerfile
```

## ThemeMapper Interface

Tahimoto uses a pluggable architecture for theme mapping. The `ThemeMapper` interface (using Python's Abstract Base Classes) provides a consistent way to map media-specific metadata to universal themes:

```python
class ThemeMapper(ABC):
    @abstractmethod
    def map_to_universal_themes(self, media_info: Dict[str, Any]) -> List[str]:
        """Maps media-specific information to universal themes."""
        pass
    
    @abstractmethod
    def get_confidence_scores(self, media_info: Dict[str, Any]) -> Dict[str, float]:
        """Returns confidence scores for theme mappings."""
        pass
```

Two implementations are provided:
- **Manual mappers:** Use predefined rules (e.g., genre-to-theme mappings)
- **Agent-based mappers:** Will use the Agent Service for LLM-powered analysis

See [Thematic Mapping Process](docs/Thematic%20Mapping%20Process.md) for more details.

## Development Setup

### Prerequisites

- Docker and Docker Compose
- Git

### Quick Start

1. **Clone the repository:**
   ```bash
   git clone https://github.com/calmren/Tahimoto.git
   cd Tahimoto
   ```

2. **Start all services with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - GraphQL Playground: http://localhost:8000/graphql
   - Neo4j Browser: http://localhost:7474 (neo4j/password)

### Development Commands

#### Frontend (Vue + Vite + Tailwind)
```bash
# Start development server (inside container)
cd frontend-vue
pnpm run dev

# Build for production
pnpm run build

# Lint and format code
pnpm run lint
pnpm run format
```

#### Backend (FastAPI + Ariadne)
```bash
# Run tests
./backend/run_tests.ps1

# Run specific test types
./backend/run_tests.ps1 -TestType neo4j
./backend/run_tests.ps1 -TestType component

# Start development server (inside container)
cd backend
python run.py
```

#### Docker Management
```bash
# Rebuild containers
docker-compose up --build

# View logs
docker-compose logs -f frontend
docker-compose logs -f backend

# Stop all services
docker-compose down

# Clean up volumes (WARNING: This will delete all data)
docker-compose down -v
```

## Development Workflow

### Adding New Media Types

1. Create a new mapper implementation in `app/services/mapping/`:
```python
class BookThemeMapperManual(ThemeMapper):
    # Implement the required methods
```

2. Register it in the factory:
```python
_MANUAL_MAPPERS = {
    "anime": AnimeThemeMapperManual,
    "book": BookThemeMapperManual,  # New mapper
}
```

### Working with the GraphQL API

The GraphQL playground is available at `http://localhost:8000/graphql`. Example query:

```graphql
query {
  analyzeMedia(sourceType: "anime", sourceId: "12345") {
    primaryThemes {
      theme {
        name
        category
      }
      mappingStrength
    }
  }
}
```

## Documentation

- [Architecture](docs/Architecture%20update.md)
- [Technology Stack](docs/Technology%20Stack%20update.md)
- [Thematic Mapping Process](docs/Thematic%20Mapping%20Process.md)
- [Core Theme Categories and Taxonomy](docs/Core%20Theme%20Categories%20and%20Taxonomy%20-%20Detailed%20Specifications.md)
- [Thematic Mapping Enhancement Implementation](Thematic%20Mapping%20Enhancement%20Implementation.md)
- [API Documentation](http://localhost:8000/docs) (FastAPI's built-in docs)

## Docker Services

The project includes the following Docker services:

- **frontend:** Vue 3 + Vite + Tailwind frontend application
- **backend:** FastAPI + Ariadne backend with GraphQL and REST APIs
- **neo4j:** Neo4j graph database for storing themes and relationships
- **redis:** Redis for caching and session management
- **agent:** (Commented out) Future Agent Service for LLM-based analysis

## Docker Workspaces and Port Configuration

### Using Docker Compose Profiles

The project now supports Docker Compose profiles, allowing you to run different "workspaces" with different sets of services. This helps avoid port conflicts when running multiple instances of the project.

Available profiles:
- `frontend`: Only frontend service
- `backend`: Only backend service
- `db`: Database services (Neo4j and Redis)
- `agent`: Agent service when enabled
- `all`: All services
- `dev`: Frontend, backend, and databases (typical development setup)

#### Examples:

Run only the database services:
```bash
docker compose --profile db up
```

Run a complete development environment:
```bash
docker compose --profile dev up
```

Run everything:
```bash
docker compose --profile all up
```

### Custom Port Configuration

You can customize the ports for each service by modifying the variables in the `.env` file:

```
FRONTEND_PORT=3000
BACKEND_PORT=8000
NEO4J_HTTP_PORT=7474
NEO4J_BOLT_PORT=7687
REDIS_PORT=6379
AGENT_PORT=8001
```

For example, to run multiple instances with different ports:

1. Create a new `.env` file for each workspace (e.g., `.env.workspace2`)
2. Set different ports in each file
3. Run Docker Compose with the specific env file:

```bash
docker compose --env-file .env.workspace2 --profile dev up
```

This allows you to have multiple workspaces with their own port configurations, avoiding conflicts between instances.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## Testing

Tahimoto uses a comprehensive testing suite to ensure code quality and correctness. The most reliable way to run tests is using Docker commands, which ensures a consistent environment.

### Running Tests with Docker

```bash
# Run all component tests
docker exec tahimoto-backend python -m pytest tests/components/ -v

# Run tests with a specific pattern
docker exec tahimoto-backend python -m pytest tests/components/ -k "cache_hit" -v

# Run a specific test file
docker exec tahimoto-backend python -m pytest tests/components/test_search_component.py -v

# Run a specific test function
docker exec tahimoto-backend python -m pytest tests/components/test_search_component.py::TestSearchComponent::test_search_cache_hit -v

# Run with coverage reporting
docker exec tahimoto-backend python -m pytest tests/components/ --cov=app -v
```

For more detailed information about the testing architecture and available test types, see the [Backend Testing README](backend/tests/README.md).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Recent enhancements to the visualization system have significantly improved the user experience:

- **Robust ID Handling**: The system now properly handles both human-readable and UUID-style theme IDs throughout the pipeline
- **Optimized GraphQL Integration**: Enhanced data flow between backend and frontend ensures complete theme relationship information
- **Adaptive Name Rendering**: Intelligent extraction of readable names from different ID formats
- **Processing Transparency**: Added status indicators for complex relationship processing
- **Improved Data Source Approach**: Fixed issues at the data source rather than relying on frontend workarounds

These improvements directly support our living framework philosophy by making the theme relationship system more accessible and understandable, facilitating more effective taxonomy evolution.