"""
GraphQL resolvers for theme relationship operations.
"""
import logging
from typing import Dict, List, Any
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme
import neo4j
from uuid import uuid4
from app.graphql.resolvers.utils.formatters import format_theme
from app.graphql.resolvers.utils.error_handlers import handle_resolver_errors, log_resolver_call
from app.services.theme_relationship import ThemeRelationshipService
from app.graphql.resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

# Initialize services
relationship_service = ThemeRelationshipService()

@query_resolvers.field("themeRelationships")
@handle_resolver_errors()
@log_resolver_call
async def resolve_theme_relationships(_, info, sourceId=None, targetId=None, type=None, limit=100, offset=0):
    """
    Resolve theme relationships.
    
    Args:
        sourceId: Optional source theme ID filter
        targetId: Optional target theme ID filter
        type: Optional relationship type filter
        limit: Maximum number of results
        offset: Offset for pagination
        
    Returns:
        List of theme relationships
    """
    # Get the existing session
    session = info.context["request"].state.db_session
    
    logger.debug(f"Resolving theme relationships with filters - sourceId: {sourceId}, targetId: {targetId}, type: {type}")
    
    # Use the ThemeRelationshipService to get relationships
    relationships = await relationship_service.get_relationships(
        session=session,
        source_id=sourceId,
        target_id=targetId,
        relationship_type=type,
        limit=limit,
        offset=offset
    )
    
    logger.debug(f"Found {len(relationships)} theme relationships")
    
    # Ensure all required fields are present
    for rel in relationships:
        if 'sourceName' not in rel or not rel['sourceName']:
            logger.warning(f"Missing sourceName for relationship {rel.get('id')}, setting default")
            rel['sourceName'] = "Unknown Source Theme"
        
        if 'targetName' not in rel or not rel['targetName']:
            logger.warning(f"Missing targetName for relationship {rel.get('id')}, setting default")
            rel['targetName'] = "Unknown Target Theme"
    
    return relationships

@mutation_resolvers.field("createThemeRelationship")
@handle_resolver_errors()
@log_resolver_call
async def resolve_create_theme_relationship(_, info, input):
    """
    Resolver for createThemeRelationship mutation.
    
    Args:
        input: Dictionary containing relationship details
        
    Returns:
        Created relationship details
    """
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Extract input data
    source_id = input["sourceId"]
    target_id = input["targetId"]
    relationship_type = input["type"]
    
    # Prepare relationship data
    relationship_data = {
        "strength": input.get("strength", 1.0),
        "bidirectional": input.get("bidirectional", False),
        "description": input.get("description"),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Use the ThemeRelationshipService to create the relationship
    result = await relationship_service.create_relationship(
        session=session,
        source_id=source_id,
        target_id=target_id,
        relationship_type=relationship_type,
        properties=relationship_data
    )
    
    return result

@mutation_resolvers.field("updateThemeRelationship")
@handle_resolver_errors()
@log_resolver_call
async def resolve_update_theme_relationship(_, info, id, input):
    """
    Resolver for updateThemeRelationship mutation.
    
    Args:
        id: Relationship ID
        input: Dictionary containing updated relationship details
        
    Returns:
        Updated relationship details
    """
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Prepare update data
    update_data = {}
    if "strength" in input:
        update_data["strength"] = input["strength"]
    if "bidirectional" in input:
        update_data["bidirectional"] = input["bidirectional"]
    if "description" in input:
        update_data["description"] = input["description"]
    
    # Add updated timestamp
    update_data["updated_at"] = datetime.now().isoformat()
    
    # Use the ThemeRelationshipService to update the relationship
    result = await relationship_service.update_relationship(
        session=session,
        relationship_id=id,
        properties=update_data
    )
    
    return result

@mutation_resolvers.field("deleteThemeRelationship")
@handle_resolver_errors()
@log_resolver_call
async def resolve_delete_theme_relationship(_, info, id):
    """
    Resolver for deleteThemeRelationship mutation.
    
    Args:
        id: Relationship ID
        
    Returns:
        Success status
    """
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use the ThemeRelationshipService to delete the relationship
    success = await relationship_service.delete_relationship(
        session=session,
        relationship_id=id
    )
    
    return {"success": success, "id": id} 