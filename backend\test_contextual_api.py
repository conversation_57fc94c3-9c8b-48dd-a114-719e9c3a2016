#!/usr/bin/env python3
"""
Test script for Contextual Theme API

Tests the contextual theme mapping system with real AniList data
for The Apothecary Diaries to verify the API integration works correctly.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.contextual_theme_mapper import ContextualThemeMapper
from app.services.anilist import AniListService


async def test_contextual_api_integration():
    """Test the contextual theme API integration with real AniList data."""
    
    print("🧪 Testing Contextual Theme API Integration")
    print("=" * 60)
    
    # Initialize services
    anilist_service = AniListService()
    theme_mapper = ContextualThemeMapper()
    
    # Test with The Apothecary Diaries
    test_anime_id = "161645"  # The Apothecary Diaries
    
    try:
        print(f"📡 Fetching enhanced metadata for anime ID: {test_anime_id}")
        
        # Fetch anime details with enhanced metadata (same as API would do)
        anime_data = await anilist_service.get_anime_details(test_anime_id)
        
        if not anime_data:
            print("❌ Failed to fetch anime data")
            return
        
        # Transform to story format to get enhanced metadata
        story_data = await anilist_service.transform_to_story(anime_data)
        story_metadata = story_data.get("story_metadata", {})
        
        print(f"📺 Anime: {story_data.get('title_english', story_data.get('title_romaji'))}")
        print(f"📊 Genres: {', '.join(story_metadata.get('genres', []))}")
        
        # Analyze character demographics (same as API would do)
        characters = story_metadata.get("characters", [])
        main_chars = [c for c in characters if c.get('role') == 'MAIN']
        female_main = [c for c in main_chars if c.get('gender') == 'Female']
        
        character_analysis = {
            "total_characters": len(characters),
            "main_characters": len(main_chars),
            "female_main_characters": len(female_main),
            "has_female_mc": len(female_main) > 0,
            "female_mc_names": [c.get('name') for c in female_main]
        }
        
        print(f"\n👥 Character Analysis:")
        print(f"   Total Characters: {character_analysis['total_characters']}")
        print(f"   Main Characters: {character_analysis['main_characters']}")
        print(f"   Female Main Characters: {character_analysis['female_main_characters']}")
        print(f"   Female MC Names: {character_analysis['female_mc_names']}")
        
        # Analyze staff composition (same as API would do)
        staff = story_metadata.get("staff", [])
        directors = [s for s in staff if 'Director' in s.get('role', '')]
        female_staff = [s for s in staff if s.get('gender') == 'Female']
        
        staff_analysis = {
            "total_staff": len(staff),
            "directors": len(directors),
            "female_staff": len(female_staff),
            "female_staff_names": [s.get('name') for s in female_staff[:5]]
        }
        
        print(f"\n🎬 Staff Analysis:")
        print(f"   Total Staff: {staff_analysis['total_staff']}")
        print(f"   Directors: {staff_analysis['directors']}")
        print(f"   Female Staff: {staff_analysis['female_staff']}")
        print(f"   Female Staff Names: {staff_analysis['female_staff_names']}")
        
        # Perform contextual theme analysis (core API functionality)
        print(f"\n🎯 Performing Contextual Theme Analysis...")
        theme_matches = theme_mapper.analyze_story_themes(story_metadata)
        
        print(f"\n✅ Found {len(theme_matches)} theme matches:")
        print("-" * 50)
        
        for i, match in enumerate(theme_matches, 1):
            print(f"\n{i}. {match.theme_name}")
            print(f"   Confidence: {match.confidence * 100:.1f}%")
            if match.base_indicators:
                print(f"   Base Indicators: {', '.join(match.base_indicators)}")
            if match.context_boosts:
                print(f"   Context Boosts:")
                for boost_name, boost_value in match.context_boosts.items():
                    print(f"     • {boost_name}: +{boost_value:.2f}")
            if match.reasoning:
                print(f"   Reasoning:")
                for reason in match.reasoning:
                    print(f"     • {reason}")
        
        # Create analysis summary (same as API would do)
        analysis_summary = {
            "total_themes_detected": len(theme_matches),
            "high_confidence_themes": len([m for m in theme_matches if m.confidence >= 0.8]),
            "medium_confidence_themes": len([m for m in theme_matches if 0.5 <= m.confidence < 0.8]),
            "contextual_factors_detected": {
                "female_protagonist": character_analysis["has_female_mc"],
                "historical_setting": any("historical" in g.lower() for g in story_metadata.get("genres", [])),
                "mystery_elements": "Mystery" in story_metadata.get("genres", []),
                "female_creative_input": len(female_staff) > 0
            }
        }
        
        print(f"\n📊 Analysis Summary:")
        print(f"   Total Themes Detected: {analysis_summary['total_themes_detected']}")
        print(f"   High Confidence (≥80%): {analysis_summary['high_confidence_themes']}")
        print(f"   Medium Confidence (50-79%): {analysis_summary['medium_confidence_themes']}")
        print(f"   Contextual Factors:")
        for factor, detected in analysis_summary['contextual_factors_detected'].items():
            status = "✅" if detected else "❌"
            print(f"     {status} {factor.replace('_', ' ').title()}: {detected}")
        
        # Test specific expectations for The Apothecary Diaries
        print(f"\n🔍 Validation Tests:")
        print("-" * 30)
        
        # Test 1: Should detect Female MC
        female_mc_detected = character_analysis["has_female_mc"]
        print(f"✅ Female MC Detected: {female_mc_detected}")
        
        # Test 2: Should have high confidence for Female Agency theme
        female_agency_theme = next((m for m in theme_matches if "Female Agency" in m.theme_name), None)
        if female_agency_theme:
            confidence_pct = female_agency_theme.confidence * 100
            print(f"✅ Female Agency Theme: {confidence_pct:.1f}% confidence")
            if confidence_pct >= 80:
                print(f"   🎯 HIGH CONFIDENCE as expected!")
            else:
                print(f"   ⚠️  Lower than expected confidence")
        else:
            print(f"❌ Female Agency Theme: Not detected")
        
        # Test 3: Should detect historical context
        historical_detected = analysis_summary['contextual_factors_detected']['historical_setting']
        print(f"✅ Historical Setting: {historical_detected}")
        
        # Save detailed API response format
        api_response = {
            "anime_title": story_data.get('title_english', story_data.get('title_romaji')),
            "anime_id": test_anime_id,
            "genres": story_metadata.get("genres", []),
            "character_analysis": character_analysis,
            "staff_analysis": staff_analysis,
            "theme_matches": [
                {
                    "theme_id": match.theme_id,
                    "theme_name": match.theme_name,
                    "confidence": match.confidence,
                    "confidence_percent": f"{match.confidence * 100:.1f}%",
                    "base_indicators": match.base_indicators,
                    "context_boosts": match.context_boosts,
                    "reasoning": match.reasoning
                }
                for match in theme_matches
            ],
            "analysis_summary": analysis_summary
        }
        
        # Save results
        with open("contextual_api_test_results.json", "w", encoding="utf-8") as f:
            json.dump(api_response, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 API response format saved to contextual_api_test_results.json")
        print(f"\n🎉 Contextual Theme API Integration Test PASSED!")
        print(f"   The API would return {len(theme_matches)} themes with detailed analysis")
        
    except Exception as e:
        print(f"❌ Error in contextual API integration test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_contextual_api_integration())
