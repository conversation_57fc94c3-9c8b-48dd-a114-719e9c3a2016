"""
Factory module for creating ThemeMapper instances.
"""
from typing import Dict, Any, Optional
import logging
from app.core.config import settings
from .interface import ThemeMapper
from .anime_mapper import AnimeThemeMapperManual, AnimeThemeMapperAgent
from .tag_combination_analyzer import TagCombinationAnalyzer

logger = logging.getLogger(__name__)

# Media type to mapper class mappings
_MANUAL_MAPPERS = {
    "anime": AnimeThemeMapperManual,
    # Add more media types as they are implemented
    # "book": BookThemeMapperManual,
    # "movie": MovieThemeMapperManual,
}

_AGENT_MAPPERS = {
    "anime": AnimeThemeMapperAgent,
    # Add more media types as they are implemented
    # "book": BookThemeMapperAgent,
    # "movie": MovieThemeMapperAgent,
}

# Cache for mapper instances
_mapper_cache: Dict[str, ThemeMapper] = {}

# Cache for tag analyzers
_analyzer_cache: Dict[str, TagCombinationAnalyzer] = {}


def get_tag_analyzer() -> TagCombinationAnalyzer:
    """
    Get a shared instance of the TagCombinationAnalyzer.
    
    Returns:
        An instance of TagCombinationAnalyzer
    """
    if "default" not in _analyzer_cache:
        logger.debug("Creating new TagCombinationAnalyzer instance")
        _analyzer_cache["default"] = TagCombinationAnalyzer()
    
    return _analyzer_cache["default"]


def get_theme_mapper(media_type: str = "anime", use_agent: bool = None) -> ThemeMapper:
    """
    Factory function to get the appropriate theme mapper.
    
    Args:
        media_type: Type of media to map ("anime", "book", "movie", etc.)
        use_agent: Whether to use the agent-based mapper. If None, uses the configured default.
        
    Returns:
        An instance of the appropriate ThemeMapper implementation
    
    Raises:
        ValueError: If the requested media type is not supported
    """
    # Standardize media type
    media_type = media_type.lower()
    
    # Determine whether to use agent-based mapping
    if use_agent is None:
        use_agent = settings.USE_AGENT_MAPPING
    
    # Generate cache key
    cache_key = f"{media_type}_{use_agent}"
    
    # Return cached instance if available
    if cache_key in _mapper_cache:
        return _mapper_cache[cache_key]
    
    # Get the appropriate mapper class
    if use_agent:
        if media_type not in _AGENT_MAPPERS:
            logger.warning(f"No agent mapper found for {media_type}, falling back to manual mapper")
            if media_type not in _MANUAL_MAPPERS:
                raise ValueError(f"Unsupported media type: {media_type}")
            mapper_class = _MANUAL_MAPPERS[media_type]
        else:
            mapper_class = _AGENT_MAPPERS[media_type]
    else:
        if media_type not in _MANUAL_MAPPERS:
            raise ValueError(f"Unsupported media type: {media_type}")
        mapper_class = _MANUAL_MAPPERS[media_type]
    
    # Create and cache a new instance
    logger.debug(f"Creating new {media_type} theme mapper (agent: {use_agent})")
    mapper = mapper_class()
    _mapper_cache[cache_key] = mapper
    
    return mapper 