"""
The mapping module provides functionality for mapping media-specific metadata
to universal themes across different media types.

It uses a pluggable architecture with interfaces for different media types and
implementations that can be either manual (rule-based) or agent-powered.
"""

from .factory import get_theme_mapper, get_tag_analyzer
from .interface import ThemeMapper
from .tag_combination_analyzer import TagCombinationAnalyzer

__all__ = ["get_theme_mapper", "ThemeMapper", "TagCombinationAnalyzer", "get_tag_analyzer"] 