"""
Scheduled background tasks for the application.

This module defines background tasks that run on a schedule.
"""
import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Callable, Any, Coroutine, Optional

from app.core.logging import get_logger
from app.db.neo4j_session import get_db_session
from app.services.theme_redis import theme_redis

logger = get_logger("scheduled_tasks")

class ScheduledTaskManager:
    """Manager for scheduled background tasks."""
    
    def __init__(self):
        """Initialize the task manager."""
        self.tasks = {}
        self.running = False
        self.running_tasks = {}
        logger.info("Initialized ScheduledTaskManager")
    
    async def start(self):
        """Start all scheduled tasks."""
        logger.info("Starting scheduled tasks")
        self.running = True
        
        # Start task loops
        for task_name, task_info in self.tasks.items():
            logger.info(f"Starting task: {task_name}")
            self.running_tasks[task_name] = asyncio.create_task(
                self._run_task_loop(
                    task_name, 
                    task_info['func'], 
                    task_info['interval'], 
                    task_info['kwargs']
                )
            )
    
    async def stop(self):
        """Stop all scheduled tasks."""
        logger.info("Stopping all scheduled tasks")
        self.running = False
        
        # Wait for all tasks to complete
        for task_name, task in self.running_tasks.items():
            logger.info(f"Stopping task: {task_name}")
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.debug(f"Task {task_name} cancelled")
        
        self.running_tasks = {}
        logger.info("All scheduled tasks stopped")
    
    def register_task(
        self, 
        name: str, 
        func: Callable[..., Coroutine[Any, Any, Any]], 
        interval: int, 
        **kwargs
    ):
        """
        Register a scheduled task.
        
        Args:
            name: Name of the task
            func: Async function to run
            interval: Interval in seconds
            **kwargs: Additional keyword arguments to pass to the function
        """
        logger.info(f"Registering scheduled task: {name} (interval: {interval}s)")
        self.tasks[name] = {
            'func': func,
            'interval': interval,
            'kwargs': kwargs
        }
    
    async def _run_task_loop(
        self, 
        task_name: str, 
        func: Callable[..., Coroutine[Any, Any, Any]], 
        interval: int, 
        kwargs: Dict[str, Any]
    ):
        """
        Run a task in a loop at specified intervals.
        
        Args:
            task_name: Name of the task
            func: Async function to run
            interval: Interval in seconds
            kwargs: Additional keyword arguments to pass to the function
        """
        logger.info(f"Starting task loop: {task_name}")
        
        while self.running:
            start_time = time.time()
            logger.debug(f"Running scheduled task: {task_name}")
            
            try:
                await func(**kwargs)
                logger.debug(f"Task {task_name} completed successfully")
            except Exception as e:
                logger.error(f"Error in scheduled task {task_name}: {str(e)}")
            
            # Calculate sleep time
            elapsed = time.time() - start_time
            sleep_time = max(0, interval - elapsed)
            
            logger.debug(f"Task {task_name} took {elapsed:.2f}s, sleeping for {sleep_time:.2f}s")
            await asyncio.sleep(sleep_time)

# Define scheduled tasks

async def refresh_stale_themes_task(max_themes: int = 50):
    """Refresh stale theme caches."""
    logger.info(f"Running scheduled task to refresh stale themes (max: {max_themes})")
    
    async for session in get_db_session():
        try:
            stats = await theme_redis.refresh_stale_themes(session, max_themes)
            logger.info(f"Stale theme refresh completed: {stats}")
        except Exception as e:
            logger.error(f"Error refreshing stale themes: {str(e)}")
        finally:
            # Ensure session is closed
            session.close()

# Initialize task manager
task_manager = ScheduledTaskManager()

# Register tasks
task_manager.register_task(
    "refresh_stale_themes", 
    refresh_stale_themes_task, 
    3600,  # Run every hour
    max_themes=100
) 