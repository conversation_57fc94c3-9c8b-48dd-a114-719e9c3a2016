"""
Test script to verify the GraphQL API using Ariadne.
Run this script to test GraphQL queries and mutations.

Usage:
    python -m tests.infrastructure.test_graphql_api
"""
import asyncio
import logging
import httpx
from app.core.logging import get_logger

logger = get_logger("graphql_test")

async def test_theme_query():
    """Test a basic GraphQL theme query."""
    logger.info("Testing GraphQL theme query...")
    
    # GraphQL query
    query = """
    query {
      theme(id: "test_theme") {
        id
        name
        description
      }
    }
    """
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/graphql",
                json={"query": query}
            )
            
            logger.info(f"Response status: {response.status_code}")
            result = response.json()
            
            logger.info(f"Response data: {result}")
            
            if "errors" in result:
                logger.error(f"GraphQL errors: {result['errors']}")
                return False
            
            return True
    except Exception as e:
        logger.error(f"Error testing GraphQL: {str(e)}", exc_info=True)
        return False

async def test_analyze_media_query():
    """Test the analyzeMedia GraphQL query."""
    logger.info("Testing GraphQL analyzeMedia query...")
    
    # GraphQL query for media analysis
    query = """
    query {
      analyzeMedia(sourceType: "anime", sourceId: "1") {
        primaryThemes {
          theme {
            name
          }
          mappingStrength
        }
      }
    }
    """
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/graphql",
                json={"query": query}
            )
            
            logger.info(f"Response status: {response.status_code}")
            result = response.json()
            
            logger.info(f"Response data: {result}")
            
            if "errors" in result:
                logger.error(f"GraphQL errors: {result['errors']}")
                return False
            
            return True
    except Exception as e:
        logger.error(f"Error testing GraphQL: {str(e)}", exc_info=True)
        return False

async def main():
    """Main entry point."""
    logging.basicConfig(level=logging.INFO)
    
    # Wait for server to be up
    logger.info("Waiting for GraphQL server to be available...")
    await asyncio.sleep(5)
    
    # Run tests
    theme_test = await test_theme_query()
    logger.info(f"Theme query test: {'Success' if theme_test else 'Failed'}")
    
    analyze_test = await test_analyze_media_query()
    logger.info(f"Analyze media query test: {'Success' if analyze_test else 'Failed'}")
    
    if theme_test and analyze_test:
        logger.info("All GraphQL tests completed successfully")
    else:
        logger.error("Some GraphQL tests failed")

if __name__ == "__main__":
    asyncio.run(main()) 