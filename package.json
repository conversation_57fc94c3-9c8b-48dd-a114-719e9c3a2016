{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "description": "Cross-Media Theme Discovery Platform", "private": true, "scripts": {"dev": "docker-compose up --build", "dev:frontend": "cd frontend-vue && pnpm run dev", "dev:backend": "cd backend && python run.py", "build": "docker-compose build", "start": "docker-compose up", "stop": "docker-compose down", "clean": "docker-compose down -v", "logs": "docker-compose logs -f", "logs:frontend": "docker-compose logs -f frontend", "logs:backend": "docker-compose logs -f backend", "test": "pnpm run test:backend && pnpm run test:frontend", "test:backend": "cd backend && ./run_tests.ps1", "test:frontend": "cd frontend-vue && pnpm test", "test:neo4j": "cd backend && ./run_tests.ps1 -TestType neo4j", "test:component": "cd backend && ./run_tests.ps1 -TestType component", "test:integration": "cd frontend-vue && pnpm run test -- --run integration", "test:coverage": "cd frontend-vue && pnpm run test:coverage", "lint:frontend": "cd frontend-vue && pnpm run lint", "format:frontend": "cd frontend-vue && pnpm run format", "setup": "echo 'Setting up Tahimoto development environment...' && docker-compose up --build -d && echo 'Setup complete! Frontend: http://localhost:3000, Backend: http://localhost:8000'"}, "repository": {"type": "git", "url": "https://github.com/calmren/Tahimoto.git"}, "keywords": ["themes", "anime", "recommendation", "vue", "<PERSON><PERSON><PERSON>", "neo4j", "graphql"], "author": "SuMoKo", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}