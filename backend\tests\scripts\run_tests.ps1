#!/usr/bin/env pwsh
# Consolidated test runner script for Tahimoto backend tests

param (
    [ValidateSet("unit", "component", "infrastructure", "integration", "all")]
    [string]$TestType = "unit",
    
    [switch]$RebuildContainer,
    [switch]$ReuseContainers,
    [switch]$Coverage,
    [switch]$Verbose,
    [switch]$WithDocker,
    
    [string]$TestPattern = "",
    [string]$TestModule = "",
    [string]$AdditionalArgs = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Configurable settings
$ProjectRoot = (Get-Item $PSScriptRoot).Parent.Parent.FullName
$DockerContainerName = "tahimoto-backend"
$DockerNetworkName = "tahimoto-network"
$PytestCommand = "pytest"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($Message) {
    Write-ColorOutput Green $Message
}

function Write-Info($Message) {
    Write-ColorOutput Cyan $Message
}

function Write-Warning($Message) {
    Write-ColorOutput Yellow $Message
}

function Write-Error($Message) {
    Write-ColorOutput Red $Message
}

# Print banner
function Print-Banner {
    Write-Info "======================================================"
    Write-Info "           TAHIMOTO BACKEND TEST RUNNER               "
    Write-Info "======================================================"
    Write-Info "Test Type: $TestType"
    if ($TestPattern) {
        Write-Info "Test Pattern: $TestPattern"
    }
    if ($TestModule) {
        Write-Info "Test Module: $TestModule"
    }
    Write-Info "======================================================"
}

# Check if Docker is running
function Test-DockerRunning {
    try {
        $dockerInfo = docker info 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Docker is not running. Please start Docker and try again."
            exit 1
        }
        return $true
    }
    catch {
        Write-Error "Docker is not running or not installed. Please install Docker and try again."
        exit 1
    }
}

# Build Docker container if needed
function Build-DockerContainer {
    if ($RebuildContainer) {
        Write-Info "Rebuilding Docker container..."
        docker-compose build backend
    }
}

# Ensure Docker network exists
function Ensure-DockerNetwork {
    $networkExists = docker network ls --filter name=$DockerNetworkName -q
    if (-not $networkExists) {
        Write-Info "Creating Docker network '$DockerNetworkName'..."
        docker network create $DockerNetworkName
    }
}

# Ensure Docker container is running
function Ensure-DockerContainer {
    $containerExists = docker ps -a --filter name=$DockerContainerName -q
    
    if (-not $containerExists) {
        Write-Info "Creating and starting Docker container '$DockerContainerName'..."
        docker-compose up -d backend
    }
    else {
        $containerRunning = docker ps --filter name=$DockerContainerName -q
        if (-not $containerRunning) {
            Write-Info "Starting existing Docker container '$DockerContainerName'..."
            docker start $DockerContainerName
        }
    }
    
    # Wait for container to be ready
    Start-Sleep -Seconds 5
}

# Build pytest command based on test type and options
function Build-PytestCommand {
    $command = $PytestCommand
    
    # Add test type filtering
    switch ($TestType) {
        "unit" { $command += " -m unit" }
        "component" { $command += " -m component" }
        "infrastructure" { $command += " -m infrastructure" }
        "integration" { 
            $command += " -m integration" 
            if ($WithDocker) {
                $command += " --run-container-tests"
                if ($ReuseContainers) {
                    $command += " --reuse-containers"
                }
            }
        }
        "all" { $command += "" }  # No marker filter for all tests
    }
    
    # Add test pattern if provided
    if ($TestPattern) {
        $command += " -k `"$TestPattern`""
    }
    
    # Add test module if provided
    if ($TestModule) {
        $command += " tests/$TestModule"
    }
    else {
        # Use default test directory for each test type if no module specified
        switch ($TestType) {
            "unit" { $command += " tests/unit/" }
            "component" { $command += " tests/components/" }
            "infrastructure" { $command += " tests/infrastructure/" }
            "integration" { $command += " tests/integration/" }
            "all" { $command += " tests/" }
        }
    }
    
    # Add coverage options
    if ($Coverage) {
        $command += " --cov=app --cov-report=term-missing"
    }
    
    # Add verbosity
    if ($Verbose) {
        $command += " -v"
    }
    
    # Add additional args
    if ($AdditionalArgs) {
        $command += " $AdditionalArgs"
    }
    
    return $command
}

# Run tests in Docker container
function Run-DockerTests {
    $pytestCommand = Build-PytestCommand
    Write-Info "Running tests in Docker container with command: $pytestCommand"
    
    # Run the tests in the container
    docker exec $DockerContainerName bash -c "cd /app && python -m $pytestCommand"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Tests completed successfully!"
    }
    else {
        Write-Error "Tests failed with exit code $LASTEXITCODE"
        exit $LASTEXITCODE
    }
}

# Run tests locally
function Run-LocalTests {
    $pytestCommand = Build-PytestCommand
    Write-Info "Running tests locally with command: $pytestCommand"
    
    # Run the tests locally
    Invoke-Expression "python -m $pytestCommand"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Tests completed successfully!"
    }
    else {
        Write-Error "Tests failed with exit code $LASTEXITCODE"
        exit $LASTEXITCODE
    }
}

# Main function
function Main {
    Print-Banner
    
    if ($WithDocker) {
        # Check Docker prerequisites
        Test-DockerRunning
        Build-DockerContainer
        Ensure-DockerNetwork
        Ensure-DockerContainer
        
        # Run tests in Docker
        Run-DockerTests
    }
    else {
        # Run tests locally
        Run-LocalTests
    }
}

# Execute main function
Main 