from typing import Optional, Any, <PERSON>, Dict, Tuple, Union
import redis.asyncio as redis_async
import redis.exceptions
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("core_redis")

# Re-export Redis errors for convenience
RedisError = redis.exceptions.RedisError
ConnectionError = redis.exceptions.ConnectionError
TimeoutError = redis.exceptions.TimeoutError
ResponseError = redis.exceptions.ResponseError

class RedisConnection:
    _instance: Optional[redis_async.Redis] = None
    
    @classmethod
    async def get(cls) -> Optional[redis_async.Redis]:
        """Get Redis instance."""
        if cls._instance is None:
            await cls._connect()
        return cls._instance
    
    @classmethod
    async def get_instance(cls) -> Optional[redis_async.Redis]:
        """Alias for get() method for backward compatibility."""
        return await cls.get()
    
    @classmethod
    async def _connect(cls) -> None:
        """Connect to Redis with retry logic."""
        logger.info(f"Initializing Redis connection at {settings.REDIS_HOST}:{settings.REDIS_PORT}")
        max_retries = 5
        retry_count = 0
        retry_delay = 1  # Initial delay in seconds
        
        while retry_count < max_retries:
            try:
                cls._instance = redis_async.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    decode_responses=True,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # Test connection with ping
                await cls._instance.ping()
                
                logger.info("Redis connection established successfully")
                return
            except (ConnectionError, TimeoutError) as e:
                retry_count += 1
                logger.warning(f"Redis connection attempt {retry_count}/{max_retries} failed: {str(e)}")
                if retry_count < max_retries:
                    import asyncio
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
        
        logger.error(f"Failed to connect to Redis after {max_retries} attempts")
        cls._instance = None

    @classmethod
    async def ping(cls) -> bool:
        """Check if Redis connection is available."""
        try:
            instance = await cls.get()
            if instance:
                await instance.ping()
                return True
            return False
        except Exception as e:
            logger.warning(f"Redis ping failed: {str(e)}")
            return False
    
    @classmethod
    async def get_keys(cls, pattern: str) -> List[str]:
        """Get Redis keys matching pattern."""
        try:
            instance = await cls.get()
            if instance:
                keys = await instance.keys(pattern)
                return keys
            return []
        except Exception as e:
            logger.error(f"Error getting Redis keys: {str(e)}")
            return []
    
    @classmethod
    async def set(cls, key: str, value: Any, ex: Optional[int] = None) -> bool:
        """Set a key-value pair in Redis."""
        try:
            instance = await cls.get()
            if instance:
                await instance.set(key, value, ex=ex)
                return True
            return False
        except Exception as e:
            logger.error(f"Error setting Redis key {key}: {str(e)}")
            return False
    
    @classmethod
    async def get_value(cls, key: str) -> Optional[str]:
        """Get a value from Redis by key."""
        try:
            instance = await cls.get()
            if instance:
                return await instance.get(key)
            return None
        except Exception as e:
            logger.error(f"Error getting Redis key {key}: {str(e)}")
            return None
    
    @classmethod
    async def delete(cls, key: str) -> bool:
        """Delete a key from Redis."""
        try:
            instance = await cls.get()
            if instance:
                await instance.delete(key)
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting Redis key {key}: {str(e)}")
            return False
    
    @classmethod
    async def exists(cls, key: str) -> bool:
        """Check if a key exists in Redis."""
        try:
            instance = await cls.get()
            if instance:
                return bool(await instance.exists(key))
            return False
        except Exception as e:
            logger.error(f"Error checking if Redis key {key} exists: {str(e)}")
            return False
    
    @classmethod
    async def close(cls) -> None:
        """Close Redis connection."""
        try:
            if cls._instance:
                await cls._instance.close()
                cls._instance = None
                logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {str(e)}")
            
    @classmethod
    async def pipeline(cls) -> Optional[redis_async.client.Pipeline]:
        """Get a Redis pipeline for batch operations.
        
        A pipeline allows you to queue multiple commands and execute them
        as a single transaction, significantly reducing network overhead
        for multiple operations.
        
        Example usage:
            pipe = await RedisConnection.pipeline()
            if pipe:
                pipe.set("key1", "value1")
                pipe.set("key2", "value2") 
                pipe.get("key3")
                results = await pipe.execute()
        
        Returns:
            A Redis pipeline object or None if Redis is not available
        """
        try:
            instance = await cls.get()
            if instance:
                return instance.pipeline()
            return None
        except Exception as e:
            logger.error(f"Error creating Redis pipeline: {str(e)}")
            return None
    
    @classmethod
    async def bulk_set(cls, key_value_map: Dict[str, str], ex: Optional[int] = None) -> bool:
        """Set multiple key-value pairs in Redis efficiently.
        
        Args:
            key_value_map: Dictionary of key-value pairs to set
            ex: Optional expiration time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        if not key_value_map:
            return True  # Nothing to do
            
        try:
            pipe = await cls.pipeline()
            if not pipe:
                return False
                
            # Queue all SET commands
            for key, value in key_value_map.items():
                if ex:
                    pipe.set(key, value, ex=ex)
                else:
                    pipe.set(key, value)
                    
            # Execute pipeline
            await pipe.execute()
            logger.debug(f"Bulk set {len(key_value_map)} keys in Redis")
            return True
        except Exception as e:
            logger.error(f"Error in bulk set operation: {str(e)}")
            return False
    
    @classmethod
    async def bulk_get(cls, keys: List[str]) -> Dict[str, Optional[str]]:
        """Get multiple values from Redis efficiently.
        
        Args:
            keys: List of keys to retrieve
            
        Returns:
            Dictionary mapping keys to their values (or None if not found)
        """
        if not keys:
            return {}  # Nothing to do
            
        result: Dict[str, Optional[str]] = {key: None for key in keys}
        
        try:
            pipe = await cls.pipeline()
            if not pipe:
                return result
                
            # Queue all GET commands
            for key in keys:
                pipe.get(key)
                
            # Execute pipeline
            values = await pipe.execute()
            
            # Map results back to keys
            for i, key in enumerate(keys):
                if i < len(values):
                    result[key] = values[i]
            
            logger.debug(f"Bulk get {len(keys)} keys from Redis")
            return result
        except Exception as e:
            logger.error(f"Error in bulk get operation: {str(e)}")
            return result
    
    @classmethod
    async def bulk_delete(cls, keys: List[str]) -> int:
        """Delete multiple keys from Redis efficiently.
        
        Args:
            keys: List of keys to delete
            
        Returns:
            Number of keys successfully deleted
        """
        if not keys:
            return 0  # Nothing to do
            
        try:
            pipe = await cls.pipeline()
            if not pipe:
                return 0
                
            # Queue all DELETE commands
            for key in keys:
                pipe.delete(key)
                
            # Execute pipeline
            results = await pipe.execute()
            deleted_count = sum(1 for result in results if result)
            
            logger.debug(f"Bulk deleted {deleted_count}/{len(keys)} keys from Redis")
            return deleted_count
        except Exception as e:
            logger.error(f"Error in bulk delete operation: {str(e)}")
            return 0

# Global Redis connection instance
redis_connection = RedisConnection()

# Export Redis exceptions for external use
exceptions = redis.exceptions