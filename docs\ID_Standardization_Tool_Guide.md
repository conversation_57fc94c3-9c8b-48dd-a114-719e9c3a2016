# ID Standardization Tool Guide

This guide explains how to use the ID standardization tools built into the Tahimoto backend API. These tools help ensure consistent ID formats across the application by analyzing and correcting ID formats in the Neo4j database.

## Available Tools

The backend provides two main endpoints for ID standardization:

1. **ID Analysis**: Analyze ID formats in the Neo4j database
2. **ID Standardization**: Fix inconsistent ID formats in the Neo4j database

Both tools are available as REST endpoints in the `/api/v1/stories/debug/` namespace.

## ID Analysis Tool

### Endpoint Details

- **URL**: `/api/v1/stories/debug/neo4j-id-analysis`
- **Method**: `GET`
- **Description**: Analyzes ID formats in the Neo4j database, focusing on Story nodes

### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | integer | 100 | Number of stories to analyze (1-1000) |

### Example Usage

```
GET http://localhost:8000/api/v1/stories/debug/neo4j-id-analysis?limit=500
```

### Example Response

```json
{
  "total_analyzed": 500,
  "id_formats": {
    "with_prefix": 455,
    "without_prefix": 45,
    "non_numeric": 0,
    "external_id_with_prefix": 389,
    "external_id_without_prefix": 26,
    "external_id_missing": 85,
    "inconsistent_format": 0
  },
  "inconsistent_examples": []
}
```

### Interpretation

- `with_prefix`: IDs starting with "story_" (should be converted to database format)
- `without_prefix`: IDs without prefix (correct database format)
- `external_id_with_prefix`: external_ids starting with "story_" (correct application format)
- `external_id_without_prefix`: external_ids without prefix (should be converted to application format)
- `external_id_missing`: Records missing external_id field (should be added)

## ID Standardization Tool

### Endpoint Details

- **URL**: `/api/v1/stories/debug/standardize-neo4j-ids`
- **Method**: `POST`
- **Description**: Standardizes ID formats in the Neo4j database

### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `dry_run` | boolean | true | If true, only show what would be changed without making changes |
| `limit` | integer | 100 | Number of stories to process (1-5000) |
| `fix_double_prefixes` | boolean | false | If true, also fix IDs with double prefixes (story_story_XXX) |
| `conflict_suffix` | string | _dup | Suffix to add to duplicate IDs to resolve conflicts |

### Example Usage

For dry run (analyze without making changes):

```
POST http://localhost:8000/api/v1/stories/debug/standardize-neo4j-ids?dry_run=true&limit=1000&fix_double_prefixes=true
```

For actual standardization:

```
POST http://localhost:8000/api/v1/stories/debug/standardize-neo4j-ids?dry_run=false&limit=1000&fix_double_prefixes=true&conflict_suffix=_dup
```

### Example Response

```json
{
  "before": {
    "total_analyzed": 100,
    "id_formats": {
      "with_prefix": 63,
      "without_prefix": 25,
      "non_numeric": 0,
      "external_id_with_prefix": 67,
      "external_id_without_prefix": 12,
      "external_id_missing": 21,
      "inconsistent_format": 0
    },
    "inconsistent_examples": []
  },
  "updates": {
    "add_prefix_to_id": 0,
    "remove_prefix_from_id": 930,
    "fix_double_prefixed_id": 0,
    "add_prefix_to_external_id": 16,
    "remove_prefix_from_external_id": 0,
    "set_missing_external_id": 262,
    "updated_inconsistent_ids": 0,
    "resolved_conflicts": 15
  },
  "examples": [
    {
      "old_id": "story_1",
      "new_id": "1",
      "type": "remove_prefix_from_id"
    },
    // Additional examples...
  ],
  "summary": {
    "total_changes": 1223,
    "changes_applied": true
  },
  "dry_run": false,
  "fix_double_prefixes": true,
  "conflicts_found": true,
  "conflict_resolution_strategy": "Appending '_dup' to duplicate IDs"
}
```

### Standardization Steps

The tool performs the following standardization steps:

1. **Fix Double-Prefixed IDs**: Convert "story_story_XXX" to "XXX"
2. **Remove Prefixes**: Convert "story_XXX" to "XXX" for database format
3. **Add Missing External IDs**: Set "story_XXX" as external_id where missing
4. **Fix External ID Format**: Ensure all external_ids use "story_XXX" format

### Conflict Handling

When a conflict is detected (e.g., both "11061" and "story_11061" exist), the tool:

1. Keeps the unprefixed ID as is (e.g., "11061")
2. Adds a suffix to the previously prefixed ID (e.g., "story_11061" → "11061_dup")

This approach preserves all data while resolving the uniqueness constraint.

## Recommended Process for ID Standardization

Follow these steps to safely standardize your database:

1. **Analysis**: Run the analysis endpoint first to understand the state of your database:
   ```
   GET /api/v1/stories/debug/neo4j-id-analysis?limit=500
   ```

2. **Dry Run**: Run the standardization in dry run mode to preview changes:
   ```
   POST /api/v1/stories/debug/standardize-neo4j-ids?dry_run=true&limit=1000&fix_double_prefixes=true
   ```

3. **Check for Conflicts**: Review the response for any conflicts that would be resolved.

4. **Make a Backup**: Before proceeding, create a database backup if possible.

5. **Run Standardization**: Perform the actual standardization:
   ```
   POST /api/v1/stories/debug/standardize-neo4j-ids?dry_run=false&limit=1000&fix_double_prefixes=true
   ```

6. **Verify Results**: Run the analysis endpoint again to check the new state.

7. **Clear Caches**: Clear any Redis caches to rebuild with the new ID formats.

8. **Test Functionality**: Verify that ID-dependent features like recommendations work properly.

## Best Practices

- **Run in Batches**: If you have a large database, run the standardization in batches (e.g., 1000 records at a time)
- **Preserve Suffixes**: Once you've added suffixes to resolve conflicts, maintain those suffixes
- **Clear Caches**: Always clear caches after standardization to prevent stale data
- **Monitor Logs**: Watch the application logs for any ID-related errors after standardization
- **Update Documentation**: Document any suffix-modified IDs for future reference 