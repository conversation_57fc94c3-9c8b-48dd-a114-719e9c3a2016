"""
Theme API Endpoints

This module provides REST API endpoints for managing themes in the Tahimoto system.
The endpoints enable retrieval, creation, updating, and deletion of themes, as well as
retrieving theme relationships and categories.

Key features:
- Get all themes (GET /themes)
- Get a specific theme by ID (GET /themes/{theme_id})
- Create a new theme (POST /themes)
- Update an existing theme (PUT /themes/{theme_id})
- Delete a theme (DELETE /themes/{theme_id})
- Get theme relationships (GET /themes/{theme_id}/relationships)
- Get theme categories (GET /themes/categories)

These endpoints work with the Neo4j database through the theme CRUD operations
and utilize Redis caching through the ThemeRedisService for improved performance.
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi import status as http_status
from neo4j import AsyncSession
from neo4j.exceptions import Neo4jError
from pydantic import BaseModel

from app.core.logging import get_logger
from app.crud.neo4j import theme as theme_neo4j
from app.db.neo4j_session import get_db_session
from app.schemas.theme import Theme, ThemeCreate, ThemeUpdate, ThemeCategory, ThemeAnalysis
from app.services.theme_redis import theme_redis
from app.services.theme_analysis import ThemeAnalysisService

logger = get_logger("api_themes")

router = APIRouter()

# Define a simplified theme model for listing themes
class ThemeListItem(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    
    class Config:
        orm_mode = True

@router.get("/categories/list", response_model=List[ThemeCategory])
async def get_theme_categories() -> List[ThemeCategory]:
    """
    Retrieve all theme categories.
    
    Returns:
        List of theme categories
    """
    logger.info("Retrieving theme categories")
    # This endpoint doesn't need database access as categories are defined in the schema
    from app.schemas.theme_category import get_all_categories
    return get_all_categories()

@router.get("", response_model=List[ThemeListItem])
async def get_themes(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    status: Optional[str] = None,
    session: AsyncSession = Depends(get_db_session),
) -> List[Dict[str, Any]]:
    """
    Retrieve a list of themes with optional filtering by category and status.
    
    Args:
        skip: Number of themes to skip (for pagination)
        limit: Maximum number of themes to return
        category: Optional filter by theme category
        status: Optional filter by theme status
        session: Neo4j database session
        
    Returns:
        List of theme objects
        
    Raises:
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Retrieving themes (skip={skip}, limit={limit}, category={category}, status={status})")
        # Currently, the get_multi method only supports filtering by status
        themes = await theme_neo4j.get_multi(
            session, 
            skip=skip, 
            limit=limit,
            status=status
        )
        
        # If category filter is provided, filter the results in memory
        if category and themes:
            themes = [theme for theme in themes if theme.get("category") == category]
            
        return themes
    except Neo4jError as e:
        logger.error(f"Neo4j error retrieving themes: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving themes"
        )
    except Exception as e:
        logger.error(f"Unexpected error retrieving themes: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

@router.post("", status_code=http_status.HTTP_201_CREATED, response_model=Theme)
async def create_theme(
    theme: ThemeCreate,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Create a new theme.
    
    Args:
        theme: Theme data to create
        session: Neo4j database session
        
    Returns:
        Created theme object
        
    Raises:
        HTTPException(400): If the theme data is invalid
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Creating new theme: {theme.name}")
        created_theme = await theme_neo4j.create(session, obj_in=theme)
        
        # Cache the new theme
        await theme_redis.set_theme(created_theme["id"], created_theme)
        
        return created_theme
    except Neo4jError as e:
        logger.error(f"Neo4j error creating theme: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while creating theme"
        )
    except Exception as e:
        logger.error(f"Unexpected error creating theme: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

@router.get("/{theme_id}", response_model=Theme)
async def get_theme(
    theme_id: str,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Retrieve a specific theme by ID.
    
    This endpoint first checks the Redis cache for the theme. If found and not stale,
    it returns the cached data. Otherwise, it retrieves the theme from Neo4j and
    updates the cache.
    
    Args:
        theme_id: ID of the theme to retrieve
        session: Neo4j database session
        
    Returns:
        Theme object
        
    Raises:
        HTTPException(404): If the theme is not found
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Retrieving theme {theme_id}")
        # Use the Redis service with fallback to Neo4j
        theme = await theme_redis.get_theme_with_fallback(session, theme_id)
        
        if not theme:
            logger.warning(f"Theme {theme_id} not found")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Theme with ID {theme_id} not found"
            )
            
        return theme
    except HTTPException:
        raise
    except Neo4jError as e:
        logger.error(f"Neo4j error retrieving theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving theme"
        )
    except Exception as e:
        logger.error(f"Unexpected error retrieving theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

@router.put("/{theme_id}", response_model=Theme)
async def update_theme(
    theme_id: str,
    theme_update: ThemeUpdate,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Update an existing theme.
    
    Args:
        theme_id: ID of the theme to update
        theme_update: Theme data to update
        session: Neo4j database session
        
    Returns:
        Updated theme object
        
    Raises:
        HTTPException(404): If the theme is not found
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Updating theme {theme_id}")
        # Check if theme exists
        existing_theme = await theme_neo4j.get(session, theme_id)
        if not existing_theme:
            logger.warning(f"Theme {theme_id} not found for update")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Theme with ID {theme_id} not found"
            )
            
        updated_theme = await theme_neo4j.update(session, id=theme_id, obj_in=theme_update)
        
        # Update the cache
        await theme_redis.set_theme(theme_id, updated_theme)
        
        return updated_theme
    except HTTPException:
        raise
    except Neo4jError as e:
        logger.error(f"Neo4j error updating theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while updating theme"
        )
    except Exception as e:
        logger.error(f"Unexpected error updating theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

@router.delete("/{theme_id}", status_code=http_status.HTTP_200_OK, response_model=Dict[str, Any])
async def delete_theme(
    theme_id: str,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Delete a theme.
    
    Args:
        theme_id: ID of the theme to delete
        session: Neo4j database session
        
    Returns:
        Dictionary with deletion status
        
    Raises:
        HTTPException(404): If the theme is not found
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Deleting theme {theme_id}")
        # Check if theme exists
        existing_theme = await theme_neo4j.get(session, theme_id)
        if not existing_theme:
            logger.warning(f"Theme {theme_id} not found for deletion")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Theme with ID {theme_id} not found"
            )
            
        result = await theme_neo4j.remove(session, id=theme_id)
        
        # Invalidate the cache
        await theme_redis.invalidate_related_caches("theme", theme_id)
        
        return {"success": True, "message": f"Theme {theme_id} deleted successfully"}
    except HTTPException:
        raise
    except Neo4jError as e:
        logger.error(f"Neo4j error deleting theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while deleting theme"
        )
    except Exception as e:
        logger.error(f"Unexpected error deleting theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

@router.get("/{theme_id}/relationships", response_model=Dict[str, Any])
async def get_theme_relationships(
    theme_id: str,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Retrieve relationships for a specific theme.
    
    Args:
        theme_id: ID of the theme to retrieve relationships for
        session: Neo4j database session
        
    Returns:
        Dictionary with theme relationships
        
    Raises:
        HTTPException(404): If the theme is not found
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Retrieving relationships for theme {theme_id}")
        # Check if theme exists
        existing_theme = await theme_neo4j.get(session, theme_id)
        if not existing_theme:
            logger.warning(f"Theme {theme_id} not found for relationship retrieval")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Theme with ID {theme_id} not found"
            )
            
        relationships = await theme_neo4j.get_theme_relationships(session, theme_id)
        
        return {
            "theme_id": theme_id,
            "theme_name": existing_theme.get("name", ""),
            "relationships": relationships
        }
    except HTTPException:
        raise
    except Neo4jError as e:
        logger.error(f"Neo4j error retrieving relationships for theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving theme relationships"
        )
    except Exception as e:
        logger.error(f"Unexpected error retrieving relationships for theme {theme_id}: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )

# Input model for theme analysis request
class ThemeAnalysisRequest(BaseModel):
    source_type: str
    source_id: str
    genres: List[str] = []
    tags: List[Dict[str, Any]] = []
    description: Optional[str] = None

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_themes(
    analysis_request: ThemeAnalysisRequest,
    session: AsyncSession = Depends(get_db_session),
) -> Dict[str, Any]:
    """
    Analyze themes for a story based on its genres, tags, and description.
    
    Args:
        analysis_request: Theme analysis request data
        session: Neo4j database session
        
    Returns:
        Dictionary with analysis results including themes
        
    Raises:
        HTTPException(500): If a database error occurs
    """
    try:
        logger.info(f"Analyzing themes for {analysis_request.source_type}:{analysis_request.source_id}")
        
        # Create analysis service
        theme_analysis_service = ThemeAnalysisService()
        
        # Call the analyze_story method
        analysis_result = await theme_analysis_service.analyze_story(
            source_type=analysis_request.source_type,
            source_id=analysis_request.source_id,
            genres=analysis_request.genres,
            tags=analysis_request.tags,
            description=analysis_request.description
        )
        
        return analysis_result
        
    except Neo4jError as e:
        logger.error(f"Neo4j error analyzing themes: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while analyzing themes"
        )
    except Exception as e:
        logger.error(f"Unexpected error analyzing themes: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        ) 