import logging
import sys
from typing import Any, Optional, Dict
from datetime import datetime

# Create formatters
default_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

detailed_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
)

# Create console handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(detailed_formatter)

# Configure logging levels for different components
LOGGING_LEVELS = {
    "redis_cache": logging.DEBUG,
    "theme_redis": logging.DEBUG,
    "graphql": logging.DEBUG,
    "stories_endpoint": logging.INFO,
    "debug_endpoint": logging.DEBUG,
    "default": logging.INFO
}

def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name and appropriate logging level."""
    logger = logging.getLogger(name)
    
    # Set appropriate logging level
    level = LOGGING_LEVELS.get(name, LOGGING_LEVELS["default"])
    logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicates
    logger.handlers = []
    
    # Add the console handler
    logger.addHandler(console_handler)
    
    return logger

def log_request_details(logger: logging.Logger, method: str, url: str, **kwargs: Any) -> None:
    """Log HTTP request details."""
    logger.debug(f"Request: {method} {url}")
    if "headers" in kwargs:
        # Filter out sensitive headers
        safe_headers = {k: v for k, v in kwargs["headers"].items() 
                       if k.lower() not in ["authorization", "cookie"]}
        logger.debug(f"Headers: {safe_headers}")
    if "json" in kwargs:
        logger.debug(f"Body: {kwargs['json']}")

def log_response_details(logger: logging.Logger, response: Any) -> None:
    """Log HTTP response details."""
    logger.debug(f"Response Status: {response.status_code}")
    logger.debug(f"Response Headers: {dict(response.headers)}")
    try:
        logger.debug(f"Response Body: {response.json()}")
    except Exception as e:
        logger.debug(f"Response Body: {response.text}")
        logger.warning(f"Failed to parse response as JSON: {e}")

def log_redis_operation(
    logger: logging.Logger,
    operation: str,
    key: str,
    success: bool,
    error: Optional[Exception] = None,
    **kwargs: Any
) -> None:
    """Log Redis operations with consistent formatting."""
    message = f"Redis {operation} - Key: {key} - Success: {success}"
    if kwargs:
        message += f" - Details: {kwargs}"
    
    if success:
        logger.debug(message)
    else:
        logger.error(f"{message} - Error: {str(error)}")

def log_graphql_operation(
    logger: logging.Logger,
    operation_name: str,
    variables: Optional[Dict] = None,
    error: Optional[Exception] = None,
    duration_ms: Optional[float] = None
) -> None:
    """Log GraphQL operations with consistent formatting."""
    timestamp = datetime.utcnow().isoformat()
    message = f"GraphQL {operation_name}"
    
    if variables:
        message += f" - Variables: {variables}"
    if duration_ms is not None:
        message += f" - Duration: {duration_ms:.2f}ms"
    
    if error:
        logger.error(f"{message} - Error: {str(error)}")
    else:
        logger.debug(message)