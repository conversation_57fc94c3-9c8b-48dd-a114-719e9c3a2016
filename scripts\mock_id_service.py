#!/usr/bin/env python3
"""
Mock ID Service Class

This is a standalone version of the IdService for demonstration purposes.
It implements the core functionality without requiring the backend imports.
"""

import re
import uuid
from typing import Any, Dict, List, Optional, Tuple, Union


class IdService:
    """Service for standardizing and managing entity IDs across the application."""
    
    # Supported entity types and their prefixes
    ENTITY_PREFIXES = {
        "user": "user",
        "theme": "theme",
        "story": "story",
        "character": "character",
        "location": "location",
        "item": "item",
        "event": "event",
        "quest": "quest",
        "note": "note",
        "tag": "tag",
        "mapping": "mapping",
        "relationship": "rel",
    }
    
    # Pattern for UUIDs
    UUID_PATTERN = r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    
    # Pattern for relationship IDs
    REL_ID_PATTERN = r"^rel_(.+)_([A-Z_]+)_(.+)$"
    
    @classmethod
    def standardize_id(cls, id_value: Optional[str], entity_type: str) -> str:
        """
        Standardize an ID to ensure it has the correct prefix.
        
        Args:
            id_value: The ID to standardize
            entity_type: The type of entity (user, theme, story, etc.)
            
        Returns:
            The standardized ID with the correct prefix
        """
        if id_value is None:
            return ""
            
        # Clean the ID value
        id_value = str(id_value).strip()
        
        # Handle empty IDs
        if not id_value:
            return ""
            
        # Get the prefix for this entity type
        if entity_type not in cls.ENTITY_PREFIXES:
            raise ValueError(f"Unknown entity type: {entity_type}")
            
        prefix = cls.ENTITY_PREFIXES[entity_type]
        prefix_with_underscore = f"{prefix}_"
        
        # If ID already has the correct prefix, return it as is
        if id_value.startswith(prefix_with_underscore):
            return id_value
            
        # Otherwise, add the prefix
        return f"{prefix}_{id_value}"
    
    @classmethod
    def to_database_id(cls, id_value: str, entity_type: str) -> str:
        """
        Convert an ID to the format used in the database.
        
        Args:
            id_value: The ID to convert
            entity_type: The type of entity
            
        Returns:
            The ID in database format
        """
        # Standardize the ID first to ensure it has the correct prefix
        std_id = cls.standardize_id(id_value, entity_type)
        
        # For most entities, we use the standardized ID in the database
        return std_id
    
    @classmethod
    def is_uuid(cls, id_value: str) -> bool:
        """
        Check if an ID is a UUID.
        
        Args:
            id_value: The ID to check
            
        Returns:
            True if the ID is a UUID, False otherwise
        """
        # Extract the ID part (without prefix)
        parts = id_value.split('_', 1)
        id_part = parts[1] if len(parts) > 1 else id_value
        
        # Check if the ID part is a UUID
        return bool(re.match(cls.UUID_PATTERN, id_part))
    
    @classmethod
    def is_semantic_id(cls, id_value: str) -> bool:
        """
        Check if an ID is a semantic ID (i.e., not a UUID).
        
        Args:
            id_value: The ID to check
            
        Returns:
            True if the ID is a semantic ID, False otherwise
        """
        return not cls.is_uuid(id_value)
    
    @classmethod
    def validate_id(cls, id_value: str, entity_type: str) -> bool:
        """
        Validate that an ID follows the standard format.
        
        Args:
            id_value: The ID to validate
            entity_type: The type of entity
            
        Returns:
            True if the ID is valid, False otherwise
        """
        if not id_value:
            return False
            
        # Get the prefix for this entity type
        if entity_type not in cls.ENTITY_PREFIXES:
            return False
            
        prefix = cls.ENTITY_PREFIXES[entity_type]
        prefix_with_underscore = f"{prefix}_"
        
        # Check if ID has the correct prefix
        return id_value.startswith(prefix_with_underscore)
    
    @classmethod
    def generate_id(cls, entity_type: str, use_uuid: bool = True) -> str:
        """
        Generate a new ID for an entity.
        
        Args:
            entity_type: The type of entity
            use_uuid: Whether to use a UUID or a random string
            
        Returns:
            A new ID with the correct prefix
        """
        if entity_type not in cls.ENTITY_PREFIXES:
            raise ValueError(f"Unknown entity type: {entity_type}")
            
        prefix = cls.ENTITY_PREFIXES[entity_type]
        
        if use_uuid:
            # Generate a UUID
            id_part = str(uuid.uuid4())
        else:
            # Generate a random string
            id_part = str(uuid.uuid4())[:8]
            
        return f"{prefix}_{id_part}"
    
    @classmethod
    def find_by_id_or_name(cls, value: str, entity_type: str) -> Dict[str, str]:
        """
        Determine how to query for an entity based on the provided value.
        
        Args:
            value: The ID or name to search for
            entity_type: The type of entity
            
        Returns:
            A dictionary with the parameter name, value, and condition
        """
        if not value:
            return {
                "param_name": "id",
                "param_value": "",
                "condition": "="
            }
            
        # Standardize the value if it's an ID
        std_value = cls.standardize_id(value, entity_type)
        
        # Check if the standardized value is a valid ID
        if cls.validate_id(std_value, entity_type):
            return {
                "param_name": "id",
                "param_value": std_value,
                "condition": "="
            }
            
        # If not a valid ID, treat it as a name/title
        return {
            "param_name": "name",
            "param_value": value,
            "condition": "CONTAINS"
        }
    
    @classmethod
    def get_cache_key(cls, id_value: str, entity_type: str, suffix: str = "") -> str:
        """
        Generate a standardized cache key for an entity.
        
        Args:
            id_value: The ID of the entity
            entity_type: The type of entity
            suffix: An optional suffix to add to the cache key
            
        Returns:
            A cache key for the entity
        """
        # Standardize the ID
        std_id = cls.standardize_id(id_value, entity_type)
        
        # Create the cache key
        if suffix:
            return f"{std_id}:{suffix}"
        else:
            return std_id
    
    @classmethod
    def create_relationship_id(cls, source_id: str, rel_type: str, target_id: str) -> str:
        """
        Create a relationship ID from source ID, relationship type, and target ID.
        
        Args:
            source_id: The ID of the source entity
            rel_type: The type of relationship
            target_id: The ID of the target entity
            
        Returns:
            A relationship ID
        """
        # Clean the IDs and relationship type
        source_id = str(source_id).strip()
        rel_type = str(rel_type).strip()
        target_id = str(target_id).strip()
        
        # Create the relationship ID
        return f"rel_{source_id}_{rel_type}_{target_id}"
    
    @classmethod
    def parse_relationship_id(cls, rel_id: str) -> Tuple[str, str, str]:
        """
        Parse a relationship ID into source ID, relationship type, and target ID.
        
        Args:
            rel_id: The relationship ID to parse
            
        Returns:
            A tuple of (source_id, rel_type, target_id)
        """
        # Check if the relationship ID matches the pattern
        match = re.match(cls.REL_ID_PATTERN, rel_id)
        if not match:
            # For debugging
            print(f"Failed to match relationship ID: {rel_id}")
            print(f"Pattern used: {cls.REL_ID_PATTERN}")
            raise ValueError(f"Invalid relationship ID: {rel_id}")
            
        # Extract the parts
        source_id = match.group(1)
        rel_type = match.group(2)
        target_id = match.group(3)
        
        return source_id, rel_type, target_id 