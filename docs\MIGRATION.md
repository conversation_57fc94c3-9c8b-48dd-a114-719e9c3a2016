# Migration from PostgreSQL/SQLAlchemy to Neo4j

This document outlines the migration process from PostgreSQL with SQLAlchemy to Neo4j, and from Strawberry to Ariadne for GraphQL implementation.

## Migration Steps

1. Set up Neo4j database and connection
2. Create Neo4j schema and constraints
3. Migrate CRUD operations from SQLAlchemy to Neo4j
4. Update GraphQL implementation from Strawberry to Ariadne
5. Update API endpoints to use Neo4j
6. Test and validate the migration

## Key Changes

- **Neo4j Session Management**: Replace SQLAlchemy session with Neo4j session
- **GraphQL Implementation**: Replace Strawberry with Ariadne
- **API Modifications**: Update API endpoints to use Neo4j

## CRUD Operations Migration Checklist

### Base CRUD Operations
- [x] Create Neo4j version of base.py (`neo4j_base.py`)
- [x] Implement basic CRUD operations using Neo4j
- [x] Update session handling for Neo4j
- [x] Create Neo4j module structure

### Theme CRUD Operations
- [x] Create Neo4j version of theme.py (`neo4j_theme.py`)
- [x] Update get_by_name to use Cypher
- [x] Update get_multi to use Cypher
- [x] Redefine theme mappings as Neo4j relationships
- [x] Implement relationship creation/update/delete methods
- [x] Add methods for theme analysis using graph traversal
- [x] Add methods for finding similar media based on themes

### Story CRUD Operations
- [x] Create Neo4j version of story.py (`neo4j_story.py`)
- [x] Update get_by_external_id to use Cypher
- [x] Convert story creation to use Neo4j node creation
- [x] Convert story update to use Neo4j node properties
- [x] Implement create_or_update using Neo4j MERGE operations
- [x] Update the stale data checking logic for Neo4j

### Integration with Resolvers
- [x] Update GraphQL resolvers to use Neo4j CRUD operations
- [x] Implement theme resolvers with Neo4j
- [x] Implement theme mapping resolvers with Neo4j
- [x] Implement media analysis resolvers with Neo4j
- [x] Add new graph-specific resolvers (similar media, theme network)
- [x] Create test script for GraphQL resolvers
- [x] Fix Cypher query issues in theme analysis resolver

## Ariadne GraphQL Implementation

Ariadne is a schema-first GraphQL library for Python that replaces Strawberry in our implementation. Key aspects of the Ariadne implementation include:

### Schema Definition
- [x] Define GraphQL schema in SDL (Schema Definition Language)
- [x] Create type definitions for all entities (Theme, Story, ThemeMapping, etc.)
- [x] Define queries and mutations

### Resolver Implementation
- [x] Create resolver functions that map to GraphQL operations
- [x] Implement context-based Neo4j session injection
- [x] Connect resolvers to schema using `make_executable_schema`

### Resolver Flow
1. Client sends GraphQL query to the `/graphql` endpoint
2. Ariadne parses the query and identifies the requested fields
3. Ariadne calls the appropriate resolver functions for each field
4. Resolvers receive context with Neo4j session and execute Cypher queries
5. Results are transformed to match the GraphQL schema
6. Ariadne assembles the response and returns it to the client

## Testing the Migration

1. Test Neo4j connection
2. Test basic CRUD operations
3. Test GraphQL API with Ariadne
4. Validate theme mapping functionality
5. Test AniList integration with Neo4j

## Validation Results

- [x] Neo4j connection successful
- [x] Basic CRUD operations working
- [x] GraphQL schema introspection successful
- [x] Docker containers communicating properly
- [x] Theme mapping functionality validated
- [x] GraphQL resolver tests passing
- [x] AniList integration test created

## Recommendations System Fixes

### Filter Alignment Issue
- [x] Align Neo4j recommendation filter with API semantics
- [x] Fix min_score filter by using `r.strength` instead of `rec.average_score`
- [x] Fix the scale conversion (0-100 in API, 0-1 in Neo4j)

### Response Structure Update
- [x] Update response structure to use `filters_applied` instead of `filters`
- [x] Ensure consistent response format between Neo4j and AniList sources

### Test Coverage
- [x] Update test cases to verify filter application
- [x] Test both generic stories and One Piece use cases
- [x] Validate filter operation at different strength thresholds

## Workflow Analysis: Search to Display

The application follows this workflow for searching stories and displaying recommendations:

1. **Search Initiation**:
   - User submits a search query to `/api/v1/stories/search`
   - FastAPI endpoint validates query parameters (search term, page, items per page)

2. **Search Processing**:
   - System checks cache for existing results using the search query as key
   - If not in cache, AniList service is queried for matching anime titles
   - Results are transformed to our internal Story format

3. **Data Storage**:
   - Each search result is saved/updated in Neo4j using `story_crud.create_or_update()`
   - Ensures data consistency and reduces future API calls

4. **Result Presentation**:
   - Results are returned as a paginated StorySearchResults object
   - Results are cached for 15 minutes to improve performance

5. **Recommendations Flow**:
   - When a specific story is selected, recommendations are requested from `/api/v1/stories/{story_id}/recommendations`
   - System first checks Neo4j for existing recommendations (using get_neo4j_recommendations)
   - Recommendations are filtered based on user parameters (genres, min_score, etc.)
   - If no Neo4j recommendations exist, AniList API is queried and results are stored in Neo4j for future use
   - Filtered recommendations are returned with metadata about which filters were applied

6. **Filtering Process**:
   - Neo4j filtering: Cypher query filters using relationship properties and node attributes
   - AniList filtering: Results are filtered in-memory after retrieval
   - min_score is scaled appropriately (0-100 in API to 0-1 in Neo4j)

## Theme System Integration

The theme mapping system integrates with the core workflow in these ways:

1. **Story Theme Analysis**:
   - When stories are fetched, their themes are analyzed through `ThemeAnalysisService`
   - Genre and tag data are mapped to universal themes with confidence scores
   - Theme relationships are stored in Neo4j with properties like mapping_strength and mapping_type

2. **Redis Caching**:
   - Theme data and analysis results are cached in Redis for performance
   - IDs follow standardized prefixes (theme_, story_, mapping_)
   - Various TTLs are used based on data volatility

3. **GraphQL Access**:
   - Theme data is accessible through GraphQL queries
   - Resolvers use Neo4j operations to fetch and manipulate theme data
   - Theme analysis can be triggered explicitly through GraphQL

4. **Recommendations Enhancement**:
   - Theme relationships are used for enhancing recommendations
   - Cross-media recommendations leverage shared themes
   - Theme strength influences recommendation ranking

## Redis/Neo4j Integration Enhancements

The ThemeRedisService has been enhanced with the following improvements to better integrate with Neo4j:

- [x] Added `get_theme_with_fallback` method to retrieve theme data from cache or Neo4j with automatic caching
- [x] Added `get_analysis_with_fallback` method to retrieve theme analysis from cache or Neo4j with automatic caching
- [x] Implemented `set_stats` method to store theme statistics in Redis
- [x] Added `warm_popular_theme_cache` method to pre-load frequently accessed themes from Neo4j into the cache
- [x] Created comprehensive test suite for cache operations and Neo4j integration
- [x] Standardized TTL values across different cache types as per optimization recommendations

These enhancements ensure that the ThemeRedisService can properly interact with Neo4j for data retrieval while maintaining optimal performance through caching.

## Remaining Tasks

- [ ] Remove legacy SQLAlchemy theme CRUD operations (crud/theme.py)
- [x] Update unit tests to use Neo4j
- [x] Add more comprehensive tests for graph-specific operations
- [x] Document Neo4j schema and constraints
- [x] Create a test script to validate all GraphQL resolvers
- [ ] Test the admin interface with the new Neo4j backend
- [x] Ensure AniList API integration works with Neo4j backend
- [ ] Standardize ID handling across all services
- [x] Improve Redis caching and Neo4j integration
- [ ] Implement Agent Service for LLM-based theme mapping

## Documentation

- [x] Create README for Neo4j CRUD operations
- [x] Document Neo4j schema and relationships
- [x] Document Cypher query examples
- [x] Provide usage examples for common operations
- [ ] Create API documentation for the GraphQL endpoints
- [x] Document ID standardization practices
- [ ] Create documentation for theme mapping administration

## Dependency Management

- [x] Update requirements.txt to include Neo4j and Ariadne dependencies
- [ ] Remove SQLAlchemy and Strawberry dependencies
- [x] Update Docker configuration for Neo4j

## Benefits of the Migration

- Better representation of relationships between entities
- More intuitive data model for theme mappings
- Improved performance for relationship-heavy queries
- Simplified code for managing theme mappings
- New capabilities for graph-based analysis and recommendations

## Current Status

- ✅ Neo4j connection and basic CRUD operations are working
- ✅ GraphQL resolvers are functioning correctly with Neo4j
- ✅ Theme mapping functionality is validated
- ✅ AniList integration test created and ready for execution
- ⏳ Admin interface needs to be tested with Neo4j backend
- ✅ Recommendations filter logic corrected and tests passing
- ✅ Core story fetch and caching functionality is working
- ⏳ Legacy theme CRUD operations still need removal
- ⏳ ID standardization needs to be enforced across all services

## Next Steps

1. **Complete Legacy Code Removal**:
   - Remove or update `crud/theme.py` to use Neo4j operations exclusively
   - Ensure all code paths use the Neo4j implementations
   - Standardize ID usage across all operations

2. **Implement Admin Interface**:
   - Complete the admin interface for theme management
   - Add theme relationship visualization
   - Implement theme editing functionality

3. **Enhance Redis/Neo4j Integration**:
   - Optimize caching strategies for theme data
   - Implement proper cache invalidation
   - Ensure consistent ID handling between Redis and Neo4j

4. **Develop Agent Service**:
   - Begin implementing the agent-based mapper using LLM capabilities
   - Create proper API endpoints for the Agent Service
   - Implement fallback mechanisms for when the Agent Service is unavailable

5. **Document GraphQL API**:
   - Create comprehensive documentation for GraphQL endpoints
   - Provide examples of common queries and mutations
   - Document schema types and relationships

Follow this sequence to ensure a stable and consistent implementation:

1. First stabilize the existing functionality
2. Then remove legacy code
3. Implement the admin interface
4. Finally add the agent-based mapping capability