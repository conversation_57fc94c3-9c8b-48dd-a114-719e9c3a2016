"""Enhanced relations

Revision ID: v3
Revises: v2
Create Date: 2024-12-18 20:40:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'v3'
down_revision = 'v2'
branch_labels = None
depends_on = None

def upgrade():
    # Add new indexes for better query performance
    op.create_index('ix_stories_media_type', 'stories', ['media_type'])
    op.create_index('ix_stories_status', 'stories', ['status'])
    op.create_index('ix_stories_average_score', 'stories', ['average_score'])
    op.create_index('ix_stories_popularity', 'stories', ['popularity'])

def downgrade():
    # Remove the indexes
    op.drop_index('ix_stories_media_type')
    op.drop_index('ix_stories_status')
    op.drop_index('ix_stories_average_score')
    op.drop_index('ix_stories_popularity') 