"""
Tag Combination Analyzer for enhanced thematic mapping.

This module analyzes combinations of tags and genres to extract deeper thematic elements
and provides more accurate theme mapping with confidence scoring. The analyzer is a core
component of the Tahimoto thematic mapping system, enabling the platform to identify
nuanced themes that might not be explicitly labeled.

Key Features:
- Pattern Recognition: Identifies meaningful combinations of tags and genres
- Confidence Scoring: Provides quantitative assessment of mapping reliability
- Contextual Analysis: Considers tag categories, ranks, and relationships
- Flexible Mapping Types: Categorizes themes by type (mood, genre_combination, etc.)
- Extensive Configuration: Supports custom rules and pattern matching
"""
from typing import Dict, List, Any, Optional, Tuple, Set
import logging
from app.core.logging import get_logger

logger = get_logger(__name__)

class TagCombinationAnalyzer:
    """
    Analyzes combinations of tags and genres to extract deeper thematic elements.
    
    This class implements advanced thematic analysis by:
    1. Analyzing weighted combinations of tags
    2. Identifying patterns that indicate specific themes
    3. Providing confidence scores for theme mappings
    4. Supporting integration with existing mappers
    
    The analyzer maintains internal rules for tag combinations and provides methods
    for analyzing media content. It can be used standalone or integrated with specific
    mapper implementations (like `AnimeThemeMapperManual`).
    
    Attributes:
        combination_rules (List[Dict[str, Any]]): Rules that define theme patterns
        tag_weights (Dict[str, float]): Weight coefficients for individual tags
    """
    
    def __init__(self, rules: Optional[List[Dict[str, Any]]] = None):
        """
        Initialize the TagCombinationAnalyzer with optional custom rules.
        
        Args:
            rules: Optional list of custom tag combination rules. If None, default rules are loaded.
        """
        self.combination_rules = rules if rules is not None else self._load_combination_rules()
        self.tag_weights = self._load_tag_weights()
    
    def _load_combination_rules(self) -> List[Dict[str, Any]]:
        """
        Load the tag combination rules.
        
        Each rule defines a pattern of tags/genres that, when found together,
        indicate a specific theme. Rules include confidence scores and theme types.
        
        Returns:
            List of rule dictionaries, each containing:
            - pattern: List[str] - Tags that must be present together
            - result_theme: str - Theme ID to use for the mapping
            - display_name: str - Human-readable theme name
            - confidence: float - Base confidence score (0.0-1.0)
            - theme_type: str - Type of theme (mood, genre_combination, etc.)
            - description: str - Description of the theme
            - category: str - Theme category (MOOD, NARRATIVE_STRUCTURE, etc.)
        """
        # Enhanced rules based on the Core Theme Categories document
        return [
            # Original rules
            {
                "pattern": ["Thriller", "Psychological"],
                "result_theme": "psychological_thriller",
                "display_name": "Psychological Thriller",
                "confidence": 0.85,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Dark exploration of the human mind under extreme stress"
            },
            {
                "pattern": ["School", "Romance", "Comedy"],
                "result_theme": "romantic_comedy_school_life",
                "display_name": "School Romantic Comedy",
                "confidence": 0.80,
                "theme_type": "genre_combination",
                "category": "SETTING_TYPE",
                "description": "Light-hearted romantic stories set in school environments"
            },
            {
                "pattern": ["Tragedy", "Romance"],
                "result_theme": "tragic_romance",
                "display_name": "Tragic Romance",
                "confidence": 0.85,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Romantic stories with sad or bittersweet endings"
            },
            
            # MOOD Category Rules
            {
                "pattern": ["Iyashikei", "Slice of Life"],
                "result_theme": "healing",
                "display_name": "Healing",
                "confidence": 0.90,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Cozy slice of life with minimal conflict in natural settings"
            },
            {
                "pattern": ["Slice of Life", "Countryside", "Relaxing"],
                "result_theme": "cozy",
                "display_name": "Cozy",
                "confidence": 0.85,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Warm, comfortable, safe feelings"
            },
            {
                "pattern": ["Suspense", "Mystery"],
                "result_theme": "tense",
                "display_name": "Tense",
                "confidence": 0.80,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Anxiety-inducing, on-edge atmosphere"
            },
            {
                "pattern": ["Tragedy", "Drama", "Emotional"],
                "result_theme": "melancholic",
                "display_name": "Melancholic",
                "confidence": 0.85,
                "theme_type": "mood",
                "category": "MOOD",
                "description": "Wistful, reflective, tinged with sadness"
            },
            
            # NARRATIVE_STRUCTURE Category Rules
            {
                "pattern": ["Slice of Life", "Educational", "Instructional"],
                "result_theme": "instructional_sol",
                "display_name": "Instructional SoL",
                "confidence": 0.85,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Learning-focused slice of life with detailed activity portrayal"
            },
            {
                "pattern": ["Slice of Life", "Mystery", "School"],
                "result_theme": "mystery_sol",
                "display_name": "Mystery SoL",
                "confidence": 0.80,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Everyday mysteries with analytical characters"
            },
            {
                "pattern": ["Adventure", "Travel"],
                "result_theme": "journey",
                "display_name": "Journey",
                "confidence": 0.85,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Character travels physically and/or metaphorically"
            },
            {
                "pattern": ["Coming of Age", "Drama"],
                "result_theme": "coming_of_age",
                "display_name": "Coming of Age",
                "confidence": 0.90,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Evolution from youth to maturity"
            },
            {
                "pattern": ["Mystery", "Detective"],
                "result_theme": "mystery",
                "display_name": "Mystery",
                "confidence": 0.85,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Incremental revelation of hidden information"
            },
            {
                "pattern": ["Slice of Life", "Anthology"],
                "result_theme": "episodic",
                "display_name": "Episodic",
                "confidence": 0.75,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Self-contained episodes with loose continuity"
            },
            {
                "pattern": ["Sports", "Competition"],
                "result_theme": "tournament",
                "display_name": "Tournament",
                "confidence": 0.90,
                "theme_type": "narrative",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Structured competition with escalating challenges"
            },
            
            # CHARACTER_DYNAMIC Category Rules
            {
                "pattern": ["Found Family", "Friendship"],
                "result_theme": "found_family",
                "display_name": "Found Family",
                "confidence": 0.85,
                "theme_type": "character",
                "category": "CHARACTER_DYNAMIC",
                "description": "Non-blood relations forming familial bonds"
            },
            {
                "pattern": ["Rivalry", "Competition"],
                "result_theme": "rivalry",
                "display_name": "Rivalry",
                "confidence": 0.85,
                "theme_type": "character",
                "category": "CHARACTER_DYNAMIC",
                "description": "Competitive relationship driving mutual growth"
            },
            {
                "pattern": ["Teacher", "Student"],
                "result_theme": "mentor_student",
                "display_name": "Mentor-Student",
                "confidence": 0.90,
                "theme_type": "character",
                "category": "CHARACTER_DYNAMIC",
                "description": "Knowledge/wisdom transfer relationship"
            },
            {
                "pattern": ["Romance", "Love"],
                "result_theme": "romance",
                "display_name": "Romance",
                "confidence": 0.95,
                "theme_type": "character",
                "category": "CHARACTER_DYNAMIC",
                "description": "Romantic relationship development"
            },
            {
                "pattern": ["Ensemble Cast", "Group Dynamic"],
                "result_theme": "ensemble_cast",
                "display_name": "Ensemble Cast",
                "confidence": 0.80,
                "theme_type": "character",
                "category": "CHARACTER_DYNAMIC",
                "description": "Group dynamics with multiple key relationships"
            },
            
            # SETTING_TYPE Category Rules
            {
                "pattern": ["Slice of Life", "Work", "Office"],
                "result_theme": "workplace_sol",
                "display_name": "Workplace SoL",
                "confidence": 0.85,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Professional growth in occupational settings"
            },
            {
                "pattern": ["Slice of Life", "Food", "Cooking"],
                "result_theme": "food_focused_sol",
                "display_name": "Food-focused SoL",
                "confidence": 0.90,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Culinary preparation and appreciation"
            },
            {
                "pattern": ["School", "Academy"],
                "result_theme": "school_life",
                "display_name": "School Life",
                "confidence": 0.95,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Educational institution as primary setting"
            },
            {
                "pattern": ["Fantasy", "Magic"],
                "result_theme": "fantasy_world",
                "display_name": "Fantasy World",
                "confidence": 0.90,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Imaginary setting with its own rules"
            },
            {
                "pattern": ["Historical", "Period Piece"],
                "result_theme": "historical",
                "display_name": "Historical",
                "confidence": 0.90,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Based in real historical period"
            },
            {
                "pattern": ["Urban", "City", "Modern"],
                "result_theme": "urban_modern",
                "display_name": "Urban Modern",
                "confidence": 0.80,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Contemporary city setting"
            },
            {
                "pattern": ["Post-Apocalyptic", "Dystopia"],
                "result_theme": "post_apocalyptic",
                "display_name": "Post-Apocalyptic",
                "confidence": 0.90,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "After societal collapse"
            },
            {
                "pattern": ["Office", "Workplace"],
                "result_theme": "workplace",
                "display_name": "Workplace",
                "confidence": 0.85,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Professional environment focus"
            },
            {
                "pattern": ["Rural", "Countryside", "Village"],
                "result_theme": "rural",
                "display_name": "Rural",
                "confidence": 0.85,
                "theme_type": "setting",
                "category": "SETTING_TYPE",
                "description": "Countryside or small town setting"
            },
            
            # Complex Genre Crossovers
            {
                "pattern": ["Slice of Life", "Dark", "Philosophical"],
                "result_theme": "dark_slice_of_life",
                "display_name": "Dark Slice of Life",
                "confidence": 0.80,
                "theme_type": "genre_combination",
                "category": "MOOD",
                "description": "Slice of life with melancholic or tense mood and philosophical themes"
            },
            {
                "pattern": ["Slice of Life", "Action", "Family"],
                "result_theme": "action_slice_of_life",
                "display_name": "Action Slice of Life",
                "confidence": 0.75,
                "theme_type": "genre_combination",
                "category": "MOOD",
                "description": "Action sequences with domestic focus and found family dynamics"
            },
            {
                "pattern": ["Psychological", "Comedy"],
                "result_theme": "psychological_comedy",
                "display_name": "Psychological Comedy",
                "confidence": 0.75,
                "theme_type": "genre_combination",
                "category": "MOOD",
                "description": "Comedy with psychological depth and inner conflict"
            },
            {
                "pattern": ["Mystery", "Food", "Episodic"],
                "result_theme": "culinary_mystery",
                "display_name": "Culinary Mystery",
                "confidence": 0.80,
                "theme_type": "genre_combination",
                "category": "NARRATIVE_STRUCTURE",
                "description": "Combines food appreciation with mystery solving"
            }
        ]
    
    def _load_tag_weights(self) -> Dict[str, float]:
        """
        Load weights for individual tags to determine their importance.
        
        Higher weights indicate tags that have more significance in determining
        thematic elements. These weights are used when calculating confidence
        scores for theme mappings.
        
        Returns:
            Dictionary mapping tag names to their weight values (default: 1.0)
        """
        # Initially hardcoded, could be moved to a config file or database later
        return {
            # Higher weights for more significant tags
            "Psychological": 1.5,
            "Thriller": 1.3,
            "Drama": 1.2,
            "Romance": 1.2,
            "Action": 1.1,
            "Tragedy": 1.4,
            "Mystery": 1.3,
            "Horror": 1.3,
            "Isekai": 1.2,
            "Fantasy": 1.1,
            "Sci-Fi": 1.2,
            "Slice of Life": 1.1,
            "School": 1.0,
            "Comedy": 1.0,
            "Supernatural": 1.2,
            "Historical": 1.2,
            "War": 1.3,
            "Time Travel": 1.4,
            "Dystopian": 1.3,
            
            # Default weight for tags not explicitly listed
            "DEFAULT": 1.0
        }

    def analyze_combinations(
        self, 
        genres: List[str], 
        tags: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Analyze genres and tags to extract thematic elements.
        
        This is the main entry point for tag combination analysis. It:
        1. Normalizes tags for consistency
        2. Identifies patterns in tag combinations
        3. Analyzes individual tags for additional themes
        4. Combines results with appropriate confidence scores
        
        Args:
            genres: List of genre names
            tags: List of tag dictionaries with name, category, rank
            
        Returns:
            List of theme dictionaries with IDs, names, descriptions, 
            confidence scores, mapping types, and categories
        """
        # Normalize the tags for consistent processing
        normalized_tags = self._normalize_tags(tags)
        
        # Get all elements to match patterns against (both genres and tag names)
        all_elements = set(genres) | {tag["name"] for tag in normalized_tags}
        
        # Match patterns in the combinations
        pattern_themes = self._match_patterns(list(all_elements))
        
        # Calculate tag weights based on rank and other factors
        tag_weights = self._calculate_tag_weights(normalized_tags)
        
        # Analyze individual tags for themes
        individual_themes = self._analyze_individual_tags(normalized_tags, tag_weights)
        
        # Combine and prioritize results
        combined_results = self._combine_results(pattern_themes, individual_themes)
        
        # Add additional context to each theme
        for theme in combined_results:
            if "mapping_type" in theme:
                theme["mapping_type"] = self._get_mapping_type(
                    theme["mapping_type"], 
                    theme.get("confidence", 0.0)
                )
            if "category" not in theme:
                theme["category"] = "UNCATEGORIZED"
        
        logger.debug(f"Analyzed {len(tags)} tags and {len(genres)} genres, found {len(combined_results)} themes")
        return combined_results
    
    def _normalize_tags(self, tags: List[Any]) -> List[Dict[str, Any]]:
        """
        Normalize tags into a consistent dictionary format.
        
        Converts various input formats (strings or dictionaries) into a standardized
        dictionary format with name, category, rank, and description fields.
        
        Args:
            tags: List of tag data (either strings or dictionaries)
            
        Returns:
            List of normalized tag dictionaries with consistent structure
        """
        normalized = []
        for tag in tags:
            if isinstance(tag, str):
                normalized.append({
                    "name": tag,
                    "category": "",
                    "rank": 50,  # Default middle rank
                    "description": None
                })
            else:
                # Handle dictionary format
                name = tag.get("name", "")
                if name:
                    normalized.append({
                        "name": name,
                        "category": tag.get("category", ""),
                        "rank": tag.get("rank", 50) or 50,  # Default to 50 if None
                        "description": tag.get("description")
                    })
        return normalized
    
    def _match_patterns(self, elements: List[str]) -> List[Dict[str, Any]]:
        """
        Match elements against pattern rules and return matching themes.
        
        Args:
            elements: List of normalized tag/genre elements to match
            
        Returns:
            List of themes derived from matched patterns
        """
        result_themes = []
        elements_set = set(elements)
        
        # Try each rule pattern
        for rule in self.combination_rules:
            pattern = set(rule["pattern"])
            
            # If all elements in the pattern are present
            if pattern.issubset(elements_set):
                result_themes.append({
                    "theme_id": rule["result_theme"],
                    "name": rule["display_name"],
                    "description": rule["description"],
                    "confidence": rule["confidence"],
                    "mapping_type": rule["theme_type"],
                    "category": rule.get("category", "UNCATEGORIZED")
                })
                logger.debug(f"Matched pattern: {rule['pattern']} -> {rule['display_name']}")
        
        return result_themes

    def normalize_tags(self, genres: List[str], tags: List[Dict[str, Any]]) -> Set[str]:
        """
        Normalize genres and tags into a unified set of strings for pattern matching.
        
        This public method provides access to the tag normalization logic for external use.
        It combines genres and tag names into a single set of normalized strings.
        
        Args:
            genres: List of genre strings
            tags: List of tag dictionaries
            
        Returns:
            Set of normalized tag strings
        """
        normalized_tags = self._normalize_tags(tags)
        tag_names = [tag["name"] for tag in normalized_tags]
        return set(genres + tag_names)
        
    def match_patterns(self, normalized_tags: Set[str]) -> List[Dict[str, Any]]:
        """
        Match tag patterns against defined rules.
        
        This public method provides access to the pattern matching logic for external use.
        
        Args:
            normalized_tags: Set of normalized tag names
            
        Returns:
            List of matched rules with theme information
        """
        return self._match_patterns(list(normalized_tags))
        
    def analyze_context(self, genres: List[str], tags: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze the context of tags and genres for more complex theme extraction.
        
        This method considers the relationships between tags, their categories,
        and their ranks to provide additional contextual information for theme mapping.
        
        Args:
            genres: List of genre strings
            tags: List of tag dictionaries
            
        Returns:
            Dictionary with contextual analysis information including:
            - primary_tags: List of the most significant tags
            - tag_categories: Dictionary mapping categories to tags
            - rank_distribution: Information about tag rank distribution
        """
        normalized_tags = self._normalize_tags(tags)
        
        # Get tag categories
        tag_categories = {}
        for tag in normalized_tags:
            category = tag.get("category", "Uncategorized")
            if category not in tag_categories:
                tag_categories[category] = []
            tag_categories[category].append(tag["name"])
            
        # Determine primary tags (highest rank or weight)
        ranked_tags = sorted(normalized_tags, key=lambda t: t.get("rank", 0), reverse=True)
        primary_tags = [t["name"] for t in ranked_tags[:3]] if ranked_tags else []
        
        # Calculate rank distribution
        ranks = [t.get("rank", 0) for t in normalized_tags]
        avg_rank = sum(ranks) / len(ranks) if ranks else 0
        
        return {
            "primary_tags": primary_tags,
            "tag_categories": tag_categories,
            "rank_distribution": {
                "average": avg_rank,
                "max": max(ranks) if ranks else 0,
                "min": min(ranks) if ranks else 0
            },
            "genres": genres
        }
        
    def get_confidence_scores(self, mappings: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Extract confidence scores from theme mappings.
        
        Args:
            mappings: List of theme mapping dictionaries from analyze_combinations
            
        Returns:
            Dictionary mapping theme_id to confidence score
            (e.g., {"psychological_thriller": 0.85})
        """
        return {mapping["theme_id"]: mapping["confidence"] for mapping in mappings}
        
    def get_mapping_types(self, mappings: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Extract mapping types from theme mappings.
        
        Args:
            mappings: List of theme mapping dictionaries from analyze_combinations
            
        Returns:
            Dictionary mapping theme_id to mapping type
            (e.g., {"psychological_thriller": "mood"})
        """
        return {mapping["theme_id"]: mapping["mapping_type"] for mapping in mappings}

    def _calculate_tag_weights(self, tags: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate weights for each tag based on rank and predefined importance.
        
        Args:
            tags: List of normalized tag dictionaries
            
        Returns:
            Dictionary mapping tag names to calculated weights
        """
        weights = {}
        for tag in tags:
            name = tag["name"]
            # Base weight from predefined values
            base_weight = self.tag_weights.get(name, self.tag_weights["DEFAULT"])
            
            # Adjust by tag rank (0-100)
            rank_factor = tag["rank"] / 50.0  # Normalize to center at 1.0
            
            # Calculate final weight
            weights[name] = base_weight * rank_factor
            
        return weights
    
    def _analyze_individual_tags(
        self, 
        tags: List[Dict[str, Any]], 
        tag_weights: Dict[str, float]
    ) -> List[Dict[str, Any]]:
        """
        Analyze individual tags with their calculated weights.
        
        Args:
            tags: List of normalized tag dictionaries
            tag_weights: Dictionary of calculated tag weights
            
        Returns:
            List of theme dictionaries derived from individual tags
        """
        results = []
        
        # Simplified mapping for demonstration - could be more complex in practice
        for tag in tags:
            name = tag["name"]
            weight = tag_weights.get(name, 1.0)
            category = tag.get("category", "").lower()
            
            # Determine mapping type based on category
            if category in ["genre", "demographic"]:
                mapping_type = "primary"
            elif category in ["theme", "setting"]:
                mapping_type = "setting"
            elif category in ["character", "cast"]:
                mapping_type = "character"
            elif category in ["plot", "story"]:
                mapping_type = "plot"
            else:
                mapping_type = "secondary"
            
            # Base confidence from weight, scaled to 0.4-0.75 range
            base_confidence = 0.4 + (min(weight, 2.0) / 2.0) * 0.35
            
            # Create theme entry (simplified for demonstration)
            theme_id = f"{name.lower().replace(' ', '_')}"
            
            results.append({
                "theme_id": theme_id,
                "name": name,
                "description": tag.get("description", f"Based on {name} tag"),
                "confidence": base_confidence,
                "mapping_type": mapping_type,
                "context": f"Individual tag analysis: {name} (weight: {weight:.2f})"
            })
        
        return results
    
    def _combine_results(
        self, 
        pattern_themes: List[Dict[str, Any]], 
        individual_themes: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Combine pattern-matched themes with individual tag-based themes.
        
        Args:
            pattern_themes: Themes derived from tag combination patterns
            individual_themes: Themes derived from individual tag analysis
            
        Returns:
            Combined list of themes with proper confidence and deduplication
        """
        # Create a dict of themes by ID for easy access and deduplication
        themes_by_id = {}
        
        # Process pattern themes first (they generally have higher confidence)
        for theme in pattern_themes:
            theme_id = theme.get("theme_id", None)
            if not theme_id:
                continue
                
            themes_by_id[theme_id] = {
                "theme_id": theme_id,
                "name": theme.get("name", ""),
                "description": theme.get("description", ""),
                "confidence": theme.get("confidence", 0.5),
                "mapping_type": theme.get("mapping_type", "unknown"),
                "category": theme.get("category", "UNCATEGORIZED")
            }
            
        # Then process individual themes, only adding them if they don't conflict
        for theme in individual_themes:
            theme_id = theme.get("theme_id", None)
            if not theme_id:
                continue
                
            # If theme already exists from pattern matching, keep the higher confidence
            if theme_id in themes_by_id:
                existing_confidence = themes_by_id[theme_id]["confidence"]
                new_confidence = theme.get("confidence", 0.0)
                
                if new_confidence > existing_confidence:
                    themes_by_id[theme_id]["confidence"] = new_confidence
            else:
                # Add the new theme
                themes_by_id[theme_id] = {
                    "theme_id": theme_id,
                    "name": theme.get("name", ""),
                    "description": theme.get("description", ""),
                    "confidence": theme.get("confidence", 0.5),
                    "mapping_type": theme.get("mapping_type", "unknown"),
                    "category": theme.get("category", "UNCATEGORIZED")
                }
        
        # Sort the combined themes by confidence (descending)
        return sorted(
            list(themes_by_id.values()),
            key=lambda x: x["confidence"],
            reverse=True
        ) 

    def _get_mapping_type(self, theme_type: str, confidence: float) -> str:
        """
        Determine the appropriate mapping type based on theme type and confidence.
        
        Args:
            theme_type: The type of theme (mood, narrative, character, setting, etc.)
            confidence: The confidence score for the theme (0.0-1.0)
            
        Returns:
            The appropriate mapping type (primary, secondary, tertiary, etc.)
        """
        # Very high confidence themes become primary
        if confidence > 0.85:
            return "primary"
            
        # High confidence mood and narrative themes become secondary
        if confidence > 0.75 and theme_type in ["mood", "narrative"]:
            return "secondary"
            
        # Mid-range setting and character themes become secondary
        if confidence > 0.70 and theme_type in ["setting", "character"]:
            return "secondary"
            
        # Everything else becomes tertiary
        return "tertiary" 

    def _extract_keywords_from_description(self, description: Optional[str]) -> Set[str]:
        """
        Extract meaningful keywords from a story description that can indicate themes.
        
        Args:
            description: Story description text
            
        Returns:
            Set of extracted keywords
        """
        if not description:
            return set()
            
        # Lowercase and split by spaces
        words = description.lower().split()
        
        # Filter out common stop words and short words
        stop_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "with", "by", "about", "as"}
        keywords = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Extract key phrases (can be expanded with NLP libraries like spaCy in the future)
        phrases = []
        for i in range(len(keywords) - 1):
            if keywords[i] + " " + keywords[i+1] in description.lower():
                phrases.append(keywords[i] + "_" + keywords[i+1])
        
        return set(keywords + phrases)
    
    def _detect_narrative_structure(self, tags: List[Dict[str, Any]], keywords: Set[str]) -> Dict[str, float]:
        """
        Detect narrative structure patterns based on tags and description keywords.
        
        Args:
            tags: List of tag dictionaries
            keywords: Set of extracted keywords from description
            
        Returns:
            Dictionary mapping narrative structure types to confidence scores
        """
        narrative_structures = {
            "Journey": 0.0,
            "Coming_of_Age": 0.0,
            "Mystery": 0.0,
            "Episodic": 0.0,
            "Tournament": 0.0,
            "Ensemble": 0.0,
            "Tragedy": 0.0
        }
        
        # Tag-based indicators
        tag_names = {tag.get("name", "").lower() for tag in tags}
        
        # Journey indicators
        journey_tags = {"adventure", "quest", "travel", "exploration"}
        journey_keywords = {"journey", "adventure", "quest", "travel", "explore", "expedition", "discovery", "destination"}
        narrative_structures["Journey"] = self._calculate_pattern_confidence(
            tag_names, keywords, journey_tags, journey_keywords
        )
        
        # Coming of Age indicators  
        coming_of_age_tags = {"coming of age", "growing up", "bildungsroman", "school", "youth"}
        coming_of_age_keywords = {"grow", "mature", "youth", "adolescence", "coming_of_age", "transform", "identity"}
        narrative_structures["Coming_of_Age"] = self._calculate_pattern_confidence(
            tag_names, keywords, coming_of_age_tags, coming_of_age_keywords
        )
        
        # Mystery indicators
        mystery_tags = {"mystery", "detective", "criminal", "suspense", "puzzle"}  
        mystery_keywords = {"mystery", "solve", "case", "clue", "detective", "investigation", "reveal", "uncover"}
        narrative_structures["Mystery"] = self._calculate_pattern_confidence(
            tag_names, keywords, mystery_tags, mystery_keywords
        )
        
        # Episodic indicators
        episodic_tags = {"episodic", "slice of life", "anthology"}
        episodic_keywords = {"episodic", "everyday", "daily", "anthology", "standalone", "vignette"}
        narrative_structures["Episodic"] = self._calculate_pattern_confidence(
            tag_names, keywords, episodic_tags, episodic_keywords
        )
        
        # Tournament indicators
        tournament_tags = {"tournament", "competition", "contest", "sports", "battle"}
        tournament_keywords = {"tournament", "competition", "contest", "championship", "battle", "match", "versus", "challenge"}
        narrative_structures["Tournament"] = self._calculate_pattern_confidence(
            tag_names, keywords, tournament_tags, tournament_keywords
        )
        
        # Ensemble indicators
        ensemble_tags = {"ensemble cast", "large cast", "multiple protagonists", "group focus"}
        ensemble_keywords = {"ensemble", "group", "team", "cast", "community", "multiple", "characters", "together"}
        narrative_structures["Ensemble"] = self._calculate_pattern_confidence(
            tag_names, keywords, ensemble_tags, ensemble_keywords
        )
        
        # Tragedy indicators
        tragedy_tags = {"tragedy", "tragic", "dark", "downfall", "despair"}
        tragedy_keywords = {"tragedy", "tragic", "downfall", "fall", "doom", "despair", "inevitable", "fate"}
        narrative_structures["Tragedy"] = self._calculate_pattern_confidence(
            tag_names, keywords, tragedy_tags, tragedy_keywords
        )
        
        return narrative_structures
    
    def _calculate_pattern_confidence(
        self, 
        tag_names: Set[str], 
        keywords: Set[str], 
        relevant_tags: Set[str], 
        relevant_keywords: Set[str],
        tag_weight: float = 0.6,
        keyword_weight: float = 0.4,
        base_threshold: float = 0.3
    ) -> float:
        """
        Calculate confidence score for a pattern based on tag and keyword matches.
        
        Args:
            tag_names: Set of tag names 
            keywords: Set of extracted keywords
            relevant_tags: Set of tags relevant to this pattern
            relevant_keywords: Set of keywords relevant to this pattern
            tag_weight: Weight given to tag matches
            keyword_weight: Weight given to keyword matches
            base_threshold: Minimum threshold for any confidence
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        # Get matching tags and keywords
        matching_tags = tag_names.intersection(relevant_tags)
        matching_keywords = keywords.intersection(relevant_keywords)
        
        # Calculate scores
        tag_score = len(matching_tags) / max(1, len(relevant_tags)) 
        keyword_score = len(matching_keywords) / max(1, min(len(keywords), len(relevant_keywords)))
        
        # Combine scores with weights
        combined_score = (tag_score * tag_weight) + (keyword_score * keyword_weight)
        
        # Apply threshold and return
        return max(0.0, combined_score) if combined_score > base_threshold else 0.0
    
    def _detect_sub_genre(self, 
                         tags: List[Dict[str, Any]], 
                         keywords: Set[str],
                         genres: List[str]) -> List[Dict[str, Any]]:
        """
        Detect implicit sub-genres based on tag combinations and description keywords.
        
        Args:
            tags: List of tag dictionaries
            keywords: Set of extracted keywords
            genres: List of genre strings
            
        Returns:
            List of detected sub-genres with confidence scores
        """
        results = []
        tag_names = {tag.get("name", "").lower() for tag in tags}
        
        # Slice of Life sub-genres
        if "slice of life" in [g.lower() for g in genres] or "slice of life" in tag_names:
            # Instructional SoL
            instructional_tags = {"educational", "hobbies", "club", "learning", "skill"}
            instructional_keywords = {"learn", "hobby", "technique", "skill", "craft", "club", "activity", "teach"}
            instructional_score = self._calculate_pattern_confidence(
                tag_names, keywords, instructional_tags, instructional_keywords
            )
            
            if instructional_score > 0:
                results.append({
                    "theme": "Instructional",
                    "category": "NARRATIVE_STRUCTURE",
                    "confidence": instructional_score,
                    "pattern_type": "sub_genre_detection",
                    "reason": "Instructional content focus detected from tags and keywords"
                })
            
            # Healing (Iyashikei)
            healing_tags = {"relaxing", "calming", "peaceful", "nature", "rural", "soothing"}
            healing_keywords = {"peaceful", "calm", "quiet", "relax", "gentle", "soft", "heal", "nature"}
            healing_score = self._calculate_pattern_confidence(
                tag_names, keywords, healing_tags, healing_keywords
            )
            
            if healing_score > 0:
                results.append({
                    "theme": "Healing",
                    "category": "MOOD",
                    "confidence": healing_score,
                    "pattern_type": "sub_genre_detection",
                    "reason": "Healing (Iyashikei) elements detected from mood and setting indicators"
                })
                
            # Workplace SoL
            workplace_tags = {"workplace", "office", "work", "career", "professional"}
            workplace_keywords = {"work", "office", "career", "business", "company", "profession", "colleague", "workplace"}
            workplace_score = self._calculate_pattern_confidence(
                tag_names, keywords, workplace_tags, workplace_keywords
            )
            
            if workplace_score > 0:
                results.append({
                    "theme": "Workplace",
                    "category": "SETTING_TYPE",
                    "confidence": workplace_score,
                    "pattern_type": "sub_genre_detection",
                    "reason": "Workplace setting detected from environment indicators"
                })
                
            # Food-focused SoL
            food_tags = {"cooking", "food", "culinary", "gourmet"}
            food_keywords = {"food", "cook", "meal", "recipe", "dish", "taste", "flavor", "eat", "restaurant", "chef"}
            food_score = self._calculate_pattern_confidence(
                tag_names, keywords, food_tags, food_keywords
            )
            
            if food_score > 0:
                results.append({
                    "theme": "Food-focused",
                    "category": "SETTING_TYPE",
                    "confidence": food_score,
                    "pattern_type": "sub_genre_detection",
                    "reason": "Food-focused content detected from culinary indicators"
                })
                
        # Mystery/Detective SoL (can exist even without explicit SoL genre)
        mystery_tags = {"mystery", "detective", "puzzle", "crime"} 
        everyday_tags = {"daily life", "everyday", "slice of life", "school", "mundane"}
        
        # Check for intersection of mystery and everyday tags
        mystery_tag_matches = tag_names.intersection(mystery_tags)
        everyday_tag_matches = tag_names.intersection(everyday_tags)
        
        if mystery_tag_matches and everyday_tag_matches:
            mystery_keywords = {"mystery", "case", "clue", "solve", "puzzle", "everyday", "ordinary", "simple"}
            mystery_score = self._calculate_pattern_confidence(
                tag_names, keywords, mystery_tags.union(everyday_tags), mystery_keywords
            )
            
            if mystery_score > 0:
                results.append({
                    "theme": "Everyday Mystery",
                    "category": "NARRATIVE_STRUCTURE",
                    "confidence": mystery_score,
                    "pattern_type": "sub_genre_detection",
                    "reason": "Everyday mystery elements detected from combination of mystery and slice of life indicators"
                })
        
        return results

    def analyze_with_description(
        self, 
        genres: List[str], 
        tags: List[Dict[str, Any]],
        description: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Enhanced analysis that also uses story description for theme detection.
        
        Args:
            genres: List of genre strings
            tags: List of tag dictionaries
            description: Optional story description text
            
        Returns:
            List of detected themes with metadata
        """
        # Get keywords from description
        keywords = self._extract_keywords_from_description(description)
        
        # Get standard tag-based analysis results
        standard_results = self.analyze_combinations(genres, tags)
        
        # Detect narrative structure
        narrative_structures = self._detect_narrative_structure(tags, keywords)
        narrative_results = []
        
        for structure, confidence in narrative_structures.items():
            if confidence > 0:
                narrative_results.append({
                    "theme": structure.replace("_", " "),
                    "category": "NARRATIVE_STRUCTURE",
                    "confidence": confidence,
                    "pattern_type": "narrative_structure_detection",
                    "reason": f"{structure} narrative structure detected from content indicators"
                })
        
        # Detect sub-genres
        sub_genre_results = self._detect_sub_genre(tags, keywords, genres)
        
        # Combine all results
        combined_results = standard_results + narrative_results + sub_genre_results
        
        # Sort by confidence
        combined_results.sort(key=lambda x: x.get("confidence", 0), reverse=True)
        
        return combined_results 