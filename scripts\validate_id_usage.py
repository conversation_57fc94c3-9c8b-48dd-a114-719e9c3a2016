#!/usr/bin/env python3
"""
ID Standardization Validator

This script scans the codebase for potential ID standardization issues,
detecting patterns that might indicate non-standardized ID usage.

Usage:
    python validate_id_usage.py [--path=<path>] [--fix] [--report=<filename>]

Options:
    --path=<path>        Path to scan (default: current directory)
    --fix                Attempt to fix issues automatically
    --report=<filename>  Save report to file (default: id_report.json)
"""

import os
import re
import json
import argparse
from typing import Dict, List, Tuple, Any
import importlib.util
import sys

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import IdService (this should work now)
try:
    from app.services.id_service import IdService
except ImportError:
    print("Unable to import IdService. Make sure the app module is in your Python path.")
    print("You may need to run this script from the project root using:")
    print("  python scripts/validate_id_usage.py --path=backend")
    sys.exit(1)

# Define patterns to look for
ID_PATTERNS = {
    'theme': [
        r'["\'](theme_[a-zA-Z0-9\-_]+)["\']',  # Quoted string with theme_ prefix
        r'["\']([a-zA-Z0-9\-_]+)["\'].*theme',  # ID without prefix near word "theme"
        r'id\s*=\s*["\'](theme_[a-zA-Z0-9\-_]+)["\']',  # id = "theme_..."
        r'id\s*=\s*["\']((?!theme_)[a-zA-Z0-9\-_]+)["\'].*theme',  # id without prefix for themes
    ],
    'story': [
        r'["\'](story_[a-zA-Z0-9\-_]+)["\']',  # Quoted string with story_ prefix
        r'["\']([a-zA-Z0-9\-_]+)["\'].*story',  # ID without prefix near word "story"
        r'id\s*=\s*["\'](story_[a-zA-Z0-9\-_]+)["\']',  # id = "story_..."
        r'id\s*=\s*["\']((?!story_)[a-zA-Z0-9\-_]+)["\'].*story',  # id without prefix for stories
    ],
    'mapping': [
        r'["\'](mapping_[a-zA-Z0-9\-_]+)["\']',  # Quoted string with mapping_ prefix
        r'["\']([a-zA-Z0-9\-_]+)["\'].*mapping',  # ID without prefix near word "mapping"
        r'id\s*=\s*["\'](mapping_[a-zA-Z0-9\-_]+)["\']',  # id = "mapping_..."
        r'id\s*=\s*["\']((?!mapping_)[a-zA-Z0-9\-_]+)["\'].*mapping',  # id without prefix for mappings
    ],
    'relationship': [
        r'["\']([a-zA-Z0-9\-_]+-[A-Z_]+-[a-zA-Z0-9\-_]+)["\']',  # Relationship composite ID
        r'["\'](rel_[a-zA-Z0-9\-_]+)["\']',  # Quoted string with rel_ prefix
    ]
}

# List of file extensions to scan
FILE_EXTENSIONS = ['.py', '.ts', '.tsx', '.js', '.jsx', '.graphql', '.sql', '.json']

# Files to ignore
IGNORE_FILES = ['id_service.py', 'id_standardization.py', 'validate_id_usage.py']

# Directories to ignore
IGNORE_DIRS = ['node_modules', 'venv', '.git', '__pycache__', 'dist', 'build']

def should_process_file(filepath: str) -> bool:
    """Determine if a file should be processed."""
    filename = os.path.basename(filepath)
    extension = os.path.splitext(filepath)[1]
    
    # Check if we should ignore this file
    if filename in IGNORE_FILES:
        return False
    
    # Check if file extension is in our list
    if extension not in FILE_EXTENSIONS:
        return False
    
    # Check if file is in an ignored directory
    for ignore_dir in IGNORE_DIRS:
        if ignore_dir in filepath.split(os.sep):
            return False
    
    return True

def scan_file(filepath: str) -> List[Dict[str, Any]]:
    """
    Scan a file for potential ID issues.
    
    Args:
        filepath: Path to the file to scan
        
    Returns:
        List of issues found
    """
    issues = []
    
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            content = file.read()
            line_number = 0
            
            for line in content.split('\n'):
                line_number += 1
                
                # Check each entity type
                for entity_type, patterns in ID_PATTERNS.items():
                    for pattern in patterns:
                        matches = re.findall(pattern, line)
                        
                        for match in matches:
                            # Skip if this is a valid standardized ID
                            if entity_type == 'theme' and match.startswith('theme_'):
                                if IdService.validate_id(match, entity_type):
                                    continue
                            elif entity_type == 'story' and match.startswith('story_'):
                                if IdService.validate_id(match, entity_type):
                                    continue
                            elif entity_type == 'mapping' and match.startswith('mapping_'):
                                if IdService.validate_id(match, entity_type):
                                    continue
                            
                            # This is a potential issue
                            issues.append({
                                'file': filepath,
                                'line': line_number,
                                'content': line.strip(),
                                'match': match,
                                'entity_type': entity_type,
                                'standardized_id': IdService.standardize_id(match, entity_type) if not '-' in match else match,
                                'is_valid': IdService.validate_id(match, entity_type) if not '-' in match else True
                            })
    except Exception as e:
        issues.append({
            'file': filepath,
            'line': 0,
            'content': str(e),
            'match': '',
            'entity_type': '',
            'standardized_id': '',
            'is_valid': False,
            'error': str(e)
        })
    
    return issues

def scan_directory(path: str) -> List[Dict[str, Any]]:
    """
    Recursively scan a directory for ID standardization issues.
    
    Args:
        path: Directory path to scan
        
    Returns:
        List of issues found
    """
    all_issues = []
    
    for root, dirs, files in os.walk(path):
        # Skip ignored directories
        for ignore_dir in IGNORE_DIRS:
            if ignore_dir in dirs:
                dirs.remove(ignore_dir)
        
        for filename in files:
            filepath = os.path.join(root, filename)
            
            if should_process_file(filepath):
                issues = scan_file(filepath)
                all_issues.extend(issues)
    
    return all_issues

def fix_issues(issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Attempt to fix ID standardization issues.
    
    Args:
        issues: List of issues found
        
    Returns:
        List of fixing results
    """
    results = []
    
    # Group issues by file
    file_issues = {}
    for issue in issues:
        file_path = issue['file']
        if file_path not in file_issues:
            file_issues[file_path] = []
        file_issues[file_path].append(issue)
    
    # Process each file
    for file_path, file_issue_list in file_issues.items():
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.readlines()
            
            modified = False
            
            # Sort issues by line number in reverse order to avoid shifting line numbers
            sorted_issues = sorted(file_issue_list, key=lambda x: x['line'], reverse=True)
            
            for issue in sorted_issues:
                line_idx = issue['line'] - 1
                original_line = content[line_idx]
                
                # Skip if this isn't a standardization issue
                if issue.get('is_valid', False):
                    continue
                
                # Skip relationship IDs for now (more complex to fix)
                if '-' in issue['match']:
                    continue
                
                # Replace the ID with standardized version
                standardized_id = issue['standardized_id']
                new_line = original_line.replace(issue['match'], standardized_id)
                
                if new_line != original_line:
                    content[line_idx] = new_line
                    modified = True
                    
                    results.append({
                        'file': file_path,
                        'line': issue['line'],
                        'original': original_line.strip(),
                        'modified': new_line.strip(),
                        'success': True
                    })
            
            # Write back the file if modified
            if modified:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.writelines(content)
        
        except Exception as e:
            results.append({
                'file': file_path,
                'error': str(e),
                'success': False
            })
    
    return results

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='ID Standardization Validator')
    parser.add_argument('--path', type=str, default='.', help='Path to scan (default: current directory)')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix issues automatically')
    parser.add_argument('--report', type=str, default='id_report.json', help='Save report to file')
    
    args = parser.parse_args()
    
    print(f"Scanning {args.path} for ID standardization issues...")
    issues = scan_directory(args.path)
    
    # Count issues by type
    issue_counts = {}
    for issue in issues:
        entity_type = issue['entity_type']
        if entity_type not in issue_counts:
            issue_counts[entity_type] = {'total': 0, 'invalid': 0}
        
        issue_counts[entity_type]['total'] += 1
        if not issue.get('is_valid', False):
            issue_counts[entity_type]['invalid'] += 1
    
    # Display summary
    print("\nID Standardization Issues Summary:")
    print("---------------------------------")
    for entity_type, counts in issue_counts.items():
        print(f"{entity_type.capitalize()}: {counts['total']} total, {counts['invalid']} invalid")
    
    # Fix issues if requested
    fix_results = []
    if args.fix and issues:
        print("\nFixing issues...")
        fix_results = fix_issues(issues)
        
        # Count fixes
        successful_fixes = sum(1 for result in fix_results if result.get('success', False))
        print(f"Fixed {successful_fixes} issues out of {len(issues)} total issues.")
    
    # Save report
    report = {
        'issues': issues,
        'summary': issue_counts,
        'fixes': fix_results if args.fix else []
    }
    
    with open(args.report, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved to {args.report}")
    
    # Return error code if issues found
    return 1 if issues else 0

if __name__ == '__main__':
    sys.exit(main()) 