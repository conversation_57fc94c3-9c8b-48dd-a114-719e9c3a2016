<template>
  <div class="px-4 py-6 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
      <p class="mt-2 text-gray-600">
        Manage themes, analyze media content, and configure the system.
      </p>
    </div>

    <!-- Admin Navigation -->
    <div class="mb-8">
      <nav class="flex space-x-8" aria-label="Tabs">
        <router-link
          to="/admin/themes"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          :class="
            $route.name === 'AdminThemes'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          "
        >
          Theme Management
        </router-link>
        <router-link
          to="/admin/theme-story-connector"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
          :class="
            $route.name === 'AdminThemeStoryConnector'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          "
        >
          Theme-Story Connector
        </router-link>
      </nav>
    </div>

    <!-- Admin Content -->
    <router-view />
  </div>
</template>

<script setup lang="ts">
// Admin layout component
</script>
