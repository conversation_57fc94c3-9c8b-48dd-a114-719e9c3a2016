"""
Integration tests for Neo4j recommendation functionality.

This module contains tests for the Neo4j recommendation functionality,
focusing on the direct testing of the get_neo4j_recommendations function.
"""
import pytest
import asyncio
from typing import Dict, Any, List

from app.db.neo4j_session import get_db_session
from app.api.v1.endpoints.stories import get_neo4j_recommendations
from app.db.neo4j.indexes import create_indexes

# Test helper functions
async def create_test_recommendations_data(session):
    """Create test stories and themes with relationships for recommendation testing."""
    # Create the main story
    main_query = """
    CREATE (s:Story {
        id: $id,
        external_id: $external_id,
        title_english: $title_english,
        title_romaji: $title_romaji,
        title_native: $title_native,
        synopsis: $synopsis,
        media_type: $media_type,
        status: $status,
        average_score: $average_score,
        popularity: $popularity,
        genres: $genres,
        created_at: datetime(),
        updated_at: datetime()
    })
    """
    
    main_params = {
        "id": "story_test_main",
        "external_id": "test_main",
        "title_english": "Test Main Story",
        "title_romaji": "Test Main Story",
        "title_native": "テストメインストーリー",
        "synopsis": "A main story for testing recommendations",
        "media_type": "ANIME",
        "status": "FINISHED",
        "average_score": 85,
        "popularity": 1000,
        "genres": ["Action", "Adventure", "Drama"]
    }
    
    await session.run(main_query, main_params)
    
    # Create explicit recommendation stories
    for i in range(1, 4):
        rec_query = """
        CREATE (s:Story {
            id: $id,
            external_id: $external_id,
            title_english: $title_english,
            title_romaji: $title_romaji,
            title_native: $title_native,
            synopsis: $synopsis,
            media_type: $media_type,
            status: $status,
            average_score: $average_score,
            popularity: $popularity,
            genres: $genres,
            created_at: datetime(),
            updated_at: datetime()
        })
        """
        
        rec_params = {
            "id": f"story_test_explicit_{i}",
            "external_id": f"test_explicit_{i}",
            "title_english": f"Test Explicit Recommendation {i}",
            "title_romaji": f"Test Explicit Recommendation {i}",
            "title_native": f"テスト明示的なレコメンデーション {i}",
            "synopsis": f"An explicit recommendation for testing #{i}",
            "media_type": "ANIME",
            "status": "FINISHED" if i % 2 == 0 else "RELEASING",
            "average_score": 75 + (i * 5),  # 80, 85, 90
            "popularity": 500 - (i * 50),   # 450, 400, 350
            "genres": ["Action", "Comedy"] if i % 2 == 0 else ["Drama", "Fantasy"]
        }
        
        await session.run(rec_query, rec_params)
        
        # Create recommendation relationship
        rel_query = """
        MATCH (s:Story {id: $source_id}), (r:Story {id: $target_id})
        CREATE (s)-[rel:RECOMMENDS {strength: $strength, source: $source}]->(r)
        """
        
        rel_params = {
            "source_id": "story_test_main",
            "target_id": f"story_test_explicit_{i}",
            "strength": 0.25 * i,  # 0.25, 0.5, 0.75
            "source": "explicit"
        }
        
        await session.run(rel_query, rel_params)
    
    # Create thematic recommendation stories
    for i in range(1, 4):
        theme_rec_query = """
        CREATE (s:Story {
            id: $id,
            external_id: $external_id,
            title_english: $title_english,
            title_romaji: $title_romaji,
            title_native: $title_native,
            synopsis: $synopsis,
            media_type: $media_type,
            status: $status,
            average_score: $average_score,
            popularity: $popularity,
            genres: $genres,
            created_at: datetime(),
            updated_at: datetime()
        })
        """
        
        theme_rec_params = {
            "id": f"story_test_thematic_{i}",
            "external_id": f"test_thematic_{i}",
            "title_english": f"Test Thematic Recommendation {i}",
            "title_romaji": f"Test Thematic Recommendation {i}",
            "title_native": f"テスト主題的なレコメンデーション {i}",
            "synopsis": f"A thematic recommendation for testing #{i}",
            "media_type": "ANIME",
            "status": "FINISHED" if i % 2 == 1 else "RELEASING",
            "average_score": 70 + (i * 5),  # 75, 80, 85
            "popularity": 700 - (i * 100),  # 600, 500, 400
            "genres": ["Drama", "Psychological"] if i % 2 == 0 else ["Action", "Adventure"]
        }
        
        await session.run(theme_rec_query, theme_rec_params)
    
    # Create themes
    theme_query = """
    CREATE (t:Theme {
        id: $id,
        external_id: $external_id,
        name: $name,
        description: $description,
        status: $status
    })
    """
    
    themes = [
        {
            "id": "theme_test_1",
            "external_id": "test_theme_1",
            "name": "Coming of Age",
            "description": "The transition from childhood to adulthood",
            "status": "ACTIVE"
        },
        {
            "id": "theme_test_2",
            "external_id": "test_theme_2",
            "name": "Redemption",
            "description": "Seeking forgiveness or atoning for past mistakes",
            "status": "ACTIVE"
        },
        {
            "id": "theme_test_3",
            "external_id": "test_theme_3",
            "name": "Power of Friendship",
            "description": "The strength derived from bonds between friends",
            "status": "ACTIVE"
        }
    ]
    
    for theme_params in themes:
        await session.run(theme_query, theme_params)
    
    # Connect main story to themes
    for i, theme_id in enumerate(["theme_test_1", "theme_test_2", "theme_test_3"]):
        theme_rel_query = """
        MATCH (s:Story {id: $story_id}), (t:Theme {id: $theme_id})
        CREATE (s)-[r:HAS_THEME {
            mapping_type: $mapping_type,
            mapping_strength: $mapping_strength,
            source: $source
        }]->(t)
        """
        
        theme_rel_params = {
            "story_id": "story_test_main",
            "theme_id": theme_id,
            "mapping_type": "primary" if i == 0 else "secondary",
            "mapping_strength": 0.9 - (i * 0.2),  # 0.9, 0.7, 0.5
            "source": "manual"
        }
        
        await session.run(theme_rel_query, theme_rel_params)
    
    # Connect thematic stories to themes to create shared theme recommendations
    for i in range(1, 4):
        for j, theme_id in enumerate(["theme_test_1", "theme_test_2", "theme_test_3"]):
            if i == j:  # Only connect certain stories to certain themes for variety
                continue
                
            theme_rel_query = """
            MATCH (s:Story {id: $story_id}), (t:Theme {id: $theme_id})
            CREATE (s)-[r:HAS_THEME {
                mapping_type: $mapping_type,
                mapping_strength: $mapping_strength,
                source: $source
            }]->(t)
            """
            
            theme_rel_params = {
                "story_id": f"story_test_thematic_{i}",
                "theme_id": theme_id,
                "mapping_type": "primary" if j == 0 else "secondary",
                "mapping_strength": 0.8 - (j * 0.2),  # 0.8, 0.6, 0.4
                "source": "manual"
            }
            
            await session.run(theme_rel_query, theme_rel_params)


async def cleanup_test_recommendations_data(session):
    """Clean up test recommendation data."""
    # Delete all test stories and their relationships
    delete_query = """
    MATCH (s:Story)
    WHERE s.id STARTS WITH 'story_test_'
    DETACH DELETE s
    """
    await session.run(delete_query)
    
    # Delete all test themes and their relationships
    delete_themes_query = """
    MATCH (t:Theme)
    WHERE t.id STARTS WITH 'theme_test_'
    DETACH DELETE t
    """
    await session.run(delete_themes_query)


@pytest.mark.integration
class TestNeo4jRecommendations:
    """Integration tests for Neo4j recommendation functionality."""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Set up test data before each test and clean up afterward."""
        # Create indexes for optimal performance
        await create_indexes()
        
        # Wait for indexes to be built
        await asyncio.sleep(2)
        
        # Get a Neo4j session
        async for session in get_db_session():
            # Create test data
            await create_test_recommendations_data(session)
            yield
            # Clean up test data
            await cleanup_test_recommendations_data(session)
    
    async def test_get_neo4j_recommendations_basic(self):
        """Test the basic functionality of get_neo4j_recommendations."""
        async for session in get_db_session():
            # Call the function with basic parameters
            result = await get_neo4j_recommendations(
                session, "story_test_main", limit=10
            )
            
            # Check basic structure
            assert "recommendations" in result
            assert "count" in result
            assert result["count"] > 0
            assert len(result["recommendations"]) == result["count"]
            
            # Check recommendation structure
            rec = result["recommendations"][0]
            assert "id" in rec
            assert "title_english" in rec
            assert "recommendation_strength" in rec
            assert "recommendation_source" in rec
    
    async def test_get_neo4j_recommendations_with_genre_filter(self):
        """Test get_neo4j_recommendations with genre filtering."""
        async for session in get_db_session():
            # Call the function with genre filter
            result = await get_neo4j_recommendations(
                session, "story_test_main", genres=["Action"], limit=10
            )
            
            # Check that results contain only Action genre
            for rec in result["recommendations"]:
                assert "Action" in rec["genres"]
    
    async def test_get_neo4j_recommendations_with_score_filter(self):
        """Test get_neo4j_recommendations with minimum score filtering."""
        async for session in get_db_session():
            min_score = 85
            
            # Call the function with score filter
            result = await get_neo4j_recommendations(
                session, "story_test_main", min_score=min_score, limit=10
            )
            
            # Check that all results have score >= min_score
            for rec in result["recommendations"]:
                assert rec["average_score"] >= min_score
    
    async def test_get_neo4j_recommendations_with_status_filter(self):
        """Test get_neo4j_recommendations with status filtering."""
        async for session in get_db_session():
            status = "FINISHED"
            
            # Call the function with status filter
            result = await get_neo4j_recommendations(
                session, "story_test_main", status=status, limit=10
            )
            
            # Check that all results have the specified status
            for rec in result["recommendations"]:
                assert rec["status"] == status
    
    async def test_get_neo4j_recommendations_with_combined_filters(self):
        """Test get_neo4j_recommendations with multiple filters combined."""
        async for session in get_db_session():
            # Call the function with combined filters
            result = await get_neo4j_recommendations(
                session, 
                "story_test_main", 
                genres=["Action"],
                min_score=80,
                status="FINISHED",
                limit=10
            )
            
            # Check that all results match all filters
            for rec in result["recommendations"]:
                assert "Action" in rec["genres"]
                assert rec["average_score"] >= 80
                assert rec["status"] == "FINISHED"
    
    async def test_get_neo4j_recommendations_sorting(self):
        """Test that recommendations are properly sorted by priority and strength."""
        async for session in get_db_session():
            # Call the function
            result = await get_neo4j_recommendations(
                session, "story_test_main", limit=10
            )
            
            # Check if explicit recommendations come before thematic ones
            recommendations = result["recommendations"]
            
            # Group recommendations by source
            explicit_recs = [r for r in recommendations if r["recommendation_source"] == "explicit"]
            thematic_recs = [r for r in recommendations if r["recommendation_source"] == "thematic"]
            
            # If we have both types, explicit should appear first in the results
            if explicit_recs and thematic_recs:
                explicit_indices = [recommendations.index(r) for r in explicit_recs]
                thematic_indices = [recommendations.index(r) for r in thematic_recs]
                
                assert min(explicit_indices) < min(thematic_indices)
    
    async def test_get_neo4j_recommendations_limit(self):
        """Test that the limit parameter restricts the number of results."""
        async for session in get_db_session():
            # Test with different limits
            for limit in [1, 3, 5]:
                result = await get_neo4j_recommendations(
                    session, "story_test_main", limit=limit
                )
                
                assert len(result["recommendations"]) <= limit 