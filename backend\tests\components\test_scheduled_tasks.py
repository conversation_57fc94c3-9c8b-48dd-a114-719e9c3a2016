"""
Test the scheduled tasks functionality.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from app.core.scheduled_tasks import ScheduledTaskManager

@pytest.fixture
def task_manager():
    """Create a fresh task manager for each test."""
    return ScheduledTaskManager()

@pytest.mark.asyncio
class TestScheduledTasks:
    """Test suite for scheduled tasks functionality."""
    
    async def test_register_task(self, task_manager):
        """Test registering a task."""
        # Create a mock task
        mock_task = AsyncMock()
        
        # Register the task
        task_manager.register_task("test_task", mock_task, 60)
        
        # Verify task is registered
        assert "test_task" in task_manager.tasks
        assert task_manager.tasks["test_task"]["func"] == mock_task
        assert task_manager.tasks["test_task"]["interval"] == 60
    
    async def test_start_stop_tasks(self, task_manager):
        """Test starting and stopping tasks."""
        # Create a mock task
        mock_task = AsyncMock()
        
        # Register the task
        task_manager.register_task("test_task", mock_task, 60)
        
        # Start tasks
        await task_manager.start()
        
        # Verify task is running
        assert task_manager.running is True
        assert "test_task" in task_manager.running_tasks
        
        # Allow task to run
        await asyncio.sleep(0.1)
        
        # Stop tasks
        await task_manager.stop()
        
        # Verify tasks are stopped
        assert task_manager.running is False
        assert not task_manager.running_tasks
    
    async def test_task_execution(self, task_manager):
        """Test that tasks are executed with their arguments."""
        # Create a counter
        counter = {"count": 0}
        
        # Create a task that increments the counter
        async def increment_counter(amount=1):
            counter["count"] += amount
        
        # Register the task with a very short interval
        task_manager.register_task("counter_task", increment_counter, 0.1, amount=2)
        
        # Start tasks
        await task_manager.start()
        
        # Wait for a few iterations
        await asyncio.sleep(0.3)  # Should allow for 2-3 executions
        
        # Stop tasks
        await task_manager.stop()
        
        # Verify counter was incremented
        assert counter["count"] >= 2  # At least once (might run more based on timing)
    
    async def test_task_exception_handling(self, task_manager):
        """Test that exceptions in tasks don't stop the task loop."""
        # Create a counter
        counter = {"count": 0, "errors": 0}
        
        # Create a task that sometimes fails
        async def unstable_task():
            counter["count"] += 1
            if counter["count"] % 2 == 0:
                counter["errors"] += 1
                raise ValueError("Simulated error")
        
        # Register the task with a very short interval
        task_manager.register_task("unstable_task", unstable_task, 0.1)
        
        # Start tasks
        await task_manager.start()
        
        # Wait for a few iterations
        await asyncio.sleep(0.5)  # Should allow for 4-5 executions
        
        # Stop tasks
        await task_manager.stop()
        
        # Verify task ran multiple times despite errors
        assert counter["count"] >= 3  # At least 3 attempts
        assert counter["errors"] >= 1  # At least 1 error

    @patch('app.core.scheduled_tasks.get_db_session')
    @patch('app.core.scheduled_tasks.theme_redis')
    async def test_refresh_stale_themes_task(self, mock_theme_redis, mock_get_db_session, task_manager):
        """Test the refresh_stale_themes_task."""
        # Import here to avoid circular imports during testing
        from app.core.scheduled_tasks import refresh_stale_themes_task
        
        # Create mock session
        mock_session = AsyncMock()
        mock_session.close = MagicMock()
        
        # Make get_db_session yield our mock session
        mock_get_db_session.return_value.__aiter__.return_value = [mock_session]
        
        # Mock refresh_stale_themes
        mock_theme_redis.refresh_stale_themes = AsyncMock(return_value={
            "checked": 5,
            "refreshed": 2,
            "errors": 0,
            "not_found": 0
        })
        
        # Run the task
        await refresh_stale_themes_task(max_themes=10)
        
        # Verify refresh_stale_themes was called
        mock_theme_redis.refresh_stale_themes.assert_called_once_with(mock_session, 10)
        
        # Verify session was closed
        mock_session.close.assert_called_once() 