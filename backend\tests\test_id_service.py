"""
Comprehensive tests for IdService class.

These tests verify the IdService functionality for all entity types
and ensure consistent ID handling throughout the application.
"""
import pytest
from datetime import datetime
from uuid import uuid4

from app.services.id_service import IdService


class TestIdService:
    """Test suite for IdService class."""

    def test_standardize_id_for_all_entity_types(self):
        """Test ID standardization for all entity types."""
        # Test theme IDs - both raw and already prefixed
        assert IdService.standardize_id("adventure", "theme") == "theme_adventure"
        assert IdService.standardize_id("theme_adventure", "theme") == "theme_adventure"
        
        # Test story IDs - both numeric and with prefix
        assert IdService.standardize_id("12345", "story") == "story_12345"
        assert IdService.standardize_id("story_12345", "story") == "story_12345"
        
        # Test mapping IDs
        assert IdService.standardize_id("aot_hero", "mapping") == "mapping_aot_hero"
        assert IdService.standardize_id("mapping_aot_hero", "mapping") == "mapping_aot_hero"
        
        # Test relationship IDs
        rel_id = "relationship_theme_hero_PARENT_OF_theme_protagonist"
        assert IdService.standardize_id(rel_id, "relationship") == rel_id
        
        # Test UUID-based IDs
        uuid_str = str(uuid4())
        assert IdService.standardize_id(uuid_str, "theme") == f"theme_{uuid_str}"
        assert IdService.standardize_id(uuid_str, "story") == f"story_{uuid_str}"
        
        # Test with empty string (should return empty string)
        assert IdService.standardize_id("", "theme") == ""
        
        # Test with invalid entity type
        with pytest.raises(ValueError):
            IdService.standardize_id("test", "invalid_type")

    def test_to_database_id(self):
        """Test conversion from application ID to database ID."""
        # Test theme IDs
        assert IdService.to_database_id("theme_adventure", "theme") == "adventure"
        
        # Test story IDs
        assert IdService.to_database_id("story_12345", "story") == "12345"
        
        # Test mapping IDs
        assert IdService.to_database_id("mapping_aot_hero", "mapping") == "aot_hero"
        
        # Test with IDs lacking prefix (should be stripped first)
        assert IdService.to_database_id("adventure", "theme") == "adventure"
        
        # Test with empty string
        assert IdService.to_database_id("", "theme") == ""
        
        # Test with invalid entity type
        with pytest.raises(ValueError):
            IdService.to_database_id("test", "invalid_type")

    def test_is_uuid(self):
        """Test UUID detection."""
        # Valid UUIDs in different formats
        uuid_str = str(uuid4())
        assert IdService.is_uuid(uuid_str)
        assert IdService.is_uuid(uuid_str.replace("-", ""))
        
        # Invalid UUIDs
        assert not IdService.is_uuid("not-a-uuid")
        assert not IdService.is_uuid("12345")
        assert not IdService.is_uuid("")

    def test_is_semantic_id(self):
        """Test semantic ID detection."""
        # Valid semantic IDs
        assert IdService.is_semantic_id("adventure")
        assert IdService.is_semantic_id("hero_journey")
        
        # UUIDs are not semantic IDs
        uuid_str = str(uuid4())
        assert not IdService.is_semantic_id(uuid_str)
        
        # Empty string is not a semantic ID
        assert not IdService.is_semantic_id("")

    def test_validate_id(self):
        """Test ID validation for all entity types."""
        # Valid theme IDs
        assert IdService.validate_id("theme_adventure", "theme")
        
        # Valid story IDs
        assert IdService.validate_id("story_12345", "story")
        
        # Valid mapping IDs
        assert IdService.validate_id("mapping_aot_hero", "mapping")
        
        # Valid relationship IDs
        rel_id = "relationship_theme_hero_PARENT_OF_theme_protagonist"
        assert IdService.validate_id(rel_id, "relationship")
        
        # Invalid IDs (missing prefix)
        assert not IdService.validate_id("adventure", "theme")
        assert not IdService.validate_id("12345", "story")
        
        # Invalid entity type
        with pytest.raises(ValueError):
            IdService.validate_id("test", "invalid_type")

    def test_generate_id(self):
        """Test ID generation for all entity types."""
        # Test theme ID generation
        theme_id = IdService.generate_id("theme")
        assert theme_id.startswith("theme_")
        assert IdService.validate_id(theme_id, "theme")
        
        # Test story ID generation
        story_id = IdService.generate_id("story")
        assert story_id.startswith("story_")
        assert IdService.validate_id(story_id, "story")
        
        # Test mapping ID generation
        mapping_id = IdService.generate_id("mapping")
        assert mapping_id.startswith("mapping_")
        assert IdService.validate_id(mapping_id, "mapping")
        
        # Test relationship ID generation
        relationship_id = IdService.generate_id("relationship")
        assert relationship_id.startswith("relationship_")
        assert IdService.validate_id(relationship_id, "relationship")
        
        # Test with invalid entity type
        with pytest.raises(ValueError):
            IdService.generate_id("invalid_type")

    def test_get_cache_key(self):
        """Test cache key generation."""
        # Basic cache key
        assert IdService.get_cache_key("theme_adventure", "theme") == "theme:adventure"
        assert IdService.get_cache_key("story_12345", "story") == "story:12345"
        
        # With suffix
        assert IdService.get_cache_key("theme_adventure", "theme", "metadata") == "theme:adventure:metadata"
        
        # Using raw ID (should be standardized)
        assert IdService.get_cache_key("adventure", "theme") == "theme:adventure"
        
        # Using UUID
        uuid_str = str(uuid4())
        assert IdService.get_cache_key(f"theme_{uuid_str}", "theme") == f"theme:{uuid_str}"
        
        # Invalid entity type
        with pytest.raises(ValueError):
            IdService.get_cache_key("test", "invalid_type")

    def test_create_relationship_id(self):
        """Test relationship ID creation."""
        # Basic relationship ID
        source_id = "theme_hero"
        target_id = "theme_journey"
        rel_type = "ASSOCIATED_WITH"
        
        expected = "relationship_theme_hero_ASSOCIATED_WITH_theme_journey"
        result = IdService.create_relationship_id(source_id, rel_type, target_id)
        assert result == expected
        
        # With non-standardized IDs (should be standardized)
        source_id = "hero"  # missing prefix
        target_id = "journey"  # missing prefix
        
        expected = "relationship_theme_hero_ASSOCIATED_WITH_theme_journey"
        result = IdService.create_relationship_id(source_id, rel_type, target_id)
        assert result == expected

    def test_parse_relationship_id(self):
        """Test relationship ID parsing."""
        # Parse valid relationship ID
        rel_id = "relationship_theme_hero_ASSOCIATED_WITH_theme_journey"
        source_id, rel_type, target_id = IdService.parse_relationship_id(rel_id)
        
        assert source_id == "theme_hero"
        assert rel_type == "ASSOCIATED_WITH"
        assert target_id == "theme_journey"
        
        # Invalid format should raise ValueError
        with pytest.raises(ValueError):
            IdService.parse_relationship_id("invalid_relationship_id")

    def test_integration_standardize_and_database_id(self):
        """Test the integration of standardize_id and to_database_id."""
        # Theme IDs
        raw_id = "adventure"
        std_id = IdService.standardize_id(raw_id, "theme")
        db_id = IdService.to_database_id(std_id, "theme")
        assert std_id == "theme_adventure"
        assert db_id == "adventure"
        
        # Story IDs
        raw_id = "12345"
        std_id = IdService.standardize_id(raw_id, "story")
        db_id = IdService.to_database_id(std_id, "story")
        assert std_id == "story_12345"
        assert db_id == "12345"
        
        # UUID IDs
        uuid_str = str(uuid4())
        std_id = IdService.standardize_id(uuid_str, "theme")
        db_id = IdService.to_database_id(std_id, "theme")
        assert std_id == f"theme_{uuid_str}"
        assert db_id == uuid_str

    def test_relationship_id_roundtrip(self):
        """Test creating and parsing relationship IDs."""
        # Create a relationship ID
        source_id = "theme_hero"
        rel_type = "ASSOCIATED_WITH"
        target_id = "theme_journey"
        
        rel_id = IdService.create_relationship_id(source_id, rel_type, target_id)
        
        # Parse it back
        parsed_source, parsed_rel_type, parsed_target = IdService.parse_relationship_id(rel_id)
        
        # Verify roundtrip
        assert parsed_source == source_id
        assert parsed_rel_type == rel_type
        assert parsed_target == target_id 