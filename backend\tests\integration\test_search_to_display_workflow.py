"""
Integration tests for the search-to-display workflow.
This file tests the entire workflow from search to recommendations.
"""
import pytest
from httpx import AsyncClient
from typing import Dict, Any, List
from unittest.mock import AsyncMock, MagicMock, patch

@pytest.mark.asyncio
class TestSearchToDisplayWorkflow:
    """Test suite for the search-to-display workflow."""
    
    @pytest.fixture
    def setup_test_data(self, monkeypatch):
        """Set up test data for the workflow tests."""
        # Mock AniList service to return controlled data
        anilist_mock = AsyncMock()
        
        # Mock search results
        anilist_mock.search_anime.return_value = {
            "Page": {
                "pageInfo": {
                    "total": 3,
                    "perPage": 10,
                    "currentPage": 1,
                    "lastPage": 1,
                    "hasNextPage": False
                },
                "media": [
                    {
                        "id": 123,
                        "title": {
                            "english": "Test Anime",
                            "romaji": "Test Anime",
                            "native": "テストアニメ"
                        },
                        "type": "ANIME",
                        "format": "TV",
                        "description": "Test description",
                        "coverImage": {
                            "large": "https://example.com/large.jpg",
                            "medium": "https://example.com/medium.jpg"
                        },
                        "bannerImage": "https://example.com/banner.jpg",
                        "status": "FINISHED",
                        "averageScore": 85,
                        "popularity": 1000,
                        "source": "ORIGINAL",
                        "genres": ["Action", "Adventure"]
                    },
                    {
                        "id": 456,
                        "title": {
                            "english": "Another Anime",
                            "romaji": "Another Anime",
                            "native": "アナザーアニメ"
                        },
                        "type": "ANIME",
                        "format": "TV",
                        "description": "Another description",
                        "coverImage": {
                            "large": "https://example.com/another_large.jpg",
                            "medium": "https://example.com/another_medium.jpg"
                        },
                        "bannerImage": "https://example.com/another_banner.jpg",
                        "status": "RELEASING",
                        "averageScore": 75,
                        "popularity": 500,
                        "source": "MANGA",
                        "genres": ["Comedy", "Drama"]
                    }
                ]
            }
        }
        
        # Mock story details
        anilist_mock.get_anime_details.return_value = {
            "id": 123,
            "title": {
                "english": "Test Anime",
                "romaji": "Test Anime",
                "native": "テストアニメ"
            },
            "type": "ANIME",
            "format": "TV",
            "description": "Test description",
            "coverImage": {
                "large": "https://example.com/large.jpg",
                "medium": "https://example.com/medium.jpg"
            },
            "bannerImage": "https://example.com/banner.jpg",
            "status": "FINISHED",
            "averageScore": 85,
            "popularity": 1000,
            "source": "ORIGINAL",
            "genres": ["Action", "Adventure"],
            "tags": [
                {"name": "Shounen", "rank": 90},
                {"name": "Fighting", "rank": 80}
            ],
            "studios": {
                "edges": [
                    {"node": {"name": "Test Studio"}}
                ]
            },
            "relations": {
                "edges": []
            }
        }
        
        # Mock recommendations
        anilist_mock.get_anime_recommendations.return_value = {
            "Media": {
                "id": 123,
                "title": {
                    "english": "Test Anime",
                    "romaji": "Test Anime",
                    "native": "テストアニメ"
                },
                "recommendations": {
                    "edges": [
                        {
                            "node": {
                                "rating": 80,
                                "mediaRecommendation": {
                                    "id": 789,
                                    "title": {
                                        "english": "Recommended Anime 1",
                                        "romaji": "Recommended Anime 1",
                                        "native": "レコメンデッドアニメ1"
                                    },
                                    "type": "ANIME",
                                    "format": "TV",
                                    "status": "FINISHED",
                                    "averageScore": 90,
                                    "genres": ["Action", "Adventure"],
                                    "coverImage": {
                                        "medium": "https://example.com/rec1_medium.jpg"
                                    }
                                }
                            }
                        },
                        {
                            "node": {
                                "rating": 60,
                                "mediaRecommendation": {
                                    "id": 101,
                                    "title": {
                                        "english": "Recommended Anime 2",
                                        "romaji": "Recommended Anime 2",
                                        "native": "レコメンデッドアニメ2"
                                    },
                                    "type": "ANIME",
                                    "format": "TV",
                                    "status": "RELEASING",
                                    "averageScore": 70,
                                    "genres": ["Comedy", "Drama"],
                                    "coverImage": {
                                        "medium": "https://example.com/rec2_medium.jpg"
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        }
        
        # Mock transform method
        anilist_mock.transform_to_story.side_effect = lambda media: {
            "id": f"story_{media['id']}",
            "external_id": str(media["id"]),
            "title_english": media["title"]["english"],
            "title_romaji": media["title"]["romaji"],
            "title_native": media["title"]["native"],
            "synopsis": media.get("description", ""),
            "media_type": media["type"],
            "cover_image_large": media["coverImage"]["large"],
            "cover_image_medium": media["coverImage"]["medium"],
            "banner_image": media.get("bannerImage", ""),
            "status": media["status"],
            "average_score": media["averageScore"],
            "popularity": media["popularity"],
            "source": media["source"]
        }
        
        # Patch AniList service
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: anilist_mock)
        
        # Mock Neo4j recommendations to return empty results (to force AniList fallback)
        async def empty_neo4j_recommendations(*args, **kwargs):
            return {"recommendations": [], "count": 0}
        
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_neo4j_recommendations", empty_neo4j_recommendations)
        
        # Return the mock for use in tests
        return anilist_mock
    
    async def test_search_to_recommendations_flow(self, client: AsyncClient, setup_test_data):
        """Test the entire workflow from search to recommendations."""
        # 1. Search for a story
        search_response = await client.get(
            "/api/v1/stories/search",
            params={"query": "Test Anime", "page": 1, "per_page": 10}
        )
        assert search_response.status_code == 200
        search_data = search_response.json()
        
        # Verify search results
        assert "total" in search_data
        assert "items" in search_data
        assert len(search_data["items"]) > 0
        
        # Get the first result's ID
        story_id = search_data["items"][0]["id"].replace("story_", "")
        
        # 2. Get story details
        story_response = await client.get(f"/api/v1/stories/{story_id}")
        assert story_response.status_code == 200
        story_data = story_response.json()
        
        # Verify story details
        assert story_data["id"] == f"story_{story_id}"
        assert "title_english" in story_data
        assert "media_type" in story_data
        
        # 3. Get recommendations with filters
        rec_response = await client.get(
            f"/api/v1/stories/{story_id}/recommendations",
            params={"genres": ["Action"], "min_score": 75}
        )
        assert rec_response.status_code == 200
        rec_data = rec_response.json()
        
        # Verify recommendations
        assert "recommendations" in rec_data
        assert "count" in rec_data
        assert "filters_applied" in rec_data
        
        # Verify filters were applied
        assert rec_data["filters_applied"]["genres"] == ["Action"]
        assert rec_data["filters_applied"]["min_score"] == 75
        
        # Verify at least one recommendation matches the filter
        if rec_data["count"] > 0:
            for rec in rec_data["recommendations"]:
                if "Action" in rec.get("genres", []) and rec.get("average_score", 0) >= 75:
                    assert True
                    break
            else:
                assert False, "No recommendations match the applied filters"
    
    async def test_cache_usage_in_workflow(self, client: AsyncClient, setup_test_data, monkeypatch):
        """Test cache usage throughout the workflow."""
        # Mock cache to track hits and misses
        original_get = AsyncMock(return_value=None)  # Always miss on first call
        original_set = AsyncMock()
        
        cache_hits = {}
        
        # Create a wrapper that simulates cache behavior
        async def mock_cache_get(self, key):
            if key in cache_hits:
                return cache_hits[key]
            result = await original_get(self, key)
            return result
        
        async def mock_cache_set(self, key, value, **kwargs):
            cache_hits[key] = value
            await original_set(self, key, value, **kwargs)
        
        # Patch cache methods
        monkeypatch.setattr("app.core.cache.CacheManager.get", mock_cache_get)
        monkeypatch.setattr("app.core.cache.CacheManager.set", mock_cache_set)
        
        # 1. First search - should miss cache
        await client.get(
            "/api/v1/stories/search", 
            params={"query": "Test Anime", "page": 1, "per_page": 10}
        )
        
        # 2. Second search with same query - should hit cache
        await client.get(
            "/api/v1/stories/search", 
            params={"query": "Test Anime", "page": 1, "per_page": 10}
        )
        
        # Verify cache was used throughout the workflow
        assert len(cache_hits) > 0 