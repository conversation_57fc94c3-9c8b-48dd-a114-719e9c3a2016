"""
Neo4j index management for the <PERSON><PERSON><PERSON> application.

This module provides functions to create and manage Neo4j indexes
for optimizing theme and story queries.
"""
import logging
from neo4j import AsyncGraphDatabase
from neo4j.exceptions import ClientError
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# Index creation statements
INDEX_STATEMENTS = [
    # Story indexes
    "CREATE INDEX story_id IF NOT EXISTS FOR (s:Story) ON (s.id)",
    "CREATE INDEX story_external_id IF NOT EXISTS FOR (s:Story) ON (s.external_id)",
    "CREATE INDEX story_media_type IF NOT EXISTS FOR (s:Story) ON (s.media_type)",
    "CREATE INDEX story_title IF NOT EXISTS FOR (s:Story) ON (s.title_english)",
    
    # Theme indexes
    "CREATE INDEX theme_id IF NOT EXISTS FOR (t:Theme) ON (t.id)",
    "CREATE INDEX theme_name IF NOT EXISTS FOR (t:Theme) ON (t.name)",
    "CREATE INDEX theme_category IF NOT EXISTS FOR (t:Theme) ON (t.category)",
    
    # Combined indexes for commonly filtered properties
    "CREATE INDEX story_media_score IF NOT EXISTS FOR (s:Story) ON (s.media_type, s.score)",
    
    # Full-text indexes for search functionality
    "CREATE FULLTEXT INDEX story_title_search IF NOT EXISTS FOR (s:Story) ON EACH [s.title_english, s.title_romaji]",
    "CREATE FULLTEXT INDEX theme_text_search IF NOT EXISTS FOR (t:Theme) ON EACH [t.name, t.description]",

    # New indexes for performance optimization
    "CREATE INDEX story_status_score IF NOT EXISTS FOR (s:Story) ON (s.status, s.average_score)",
    "CREATE INDEX story_genres IF NOT EXISTS FOR (s:Story) ON (s.genres)",
    "CREATE INDEX story_popularity IF NOT EXISTS FOR (s:Story) ON (s.popularity)",
    "CREATE INDEX theme_status IF NOT EXISTS FOR (t:Theme) ON (t.status)",
    "CREATE INDEX story_updated_at IF NOT EXISTS FOR (s:Story) ON (s.updated_at)"
]

# Relationship indexes for theme-story connections
RELATIONSHIP_INDEXES = [
    "CREATE INDEX theme_story_strength IF NOT EXISTS FOR ()-[r:HAS_THEME]->() ON (r.strength)",
    "CREATE INDEX theme_story_type IF NOT EXISTS FOR ()-[r:HAS_THEME]->() ON (r.type)",

    # New relationship indexes for recommendation queries
    "CREATE INDEX recommends_strength IF NOT EXISTS FOR ()-[r:RECOMMENDS]->() ON (r.strength)",
    "CREATE INDEX recommends_source IF NOT EXISTS FOR ()-[r:RECOMMENDS]->() ON (r.source)",
    "CREATE INDEX has_theme_mapping_type IF NOT EXISTS FOR ()-[r:HAS_THEME]->() ON (r.mapping_type)",
    "CREATE INDEX has_theme_mapping_strength IF NOT EXISTS FOR ()-[r:HAS_THEME]->() ON (r.mapping_strength)",
    "CREATE INDEX has_theme_source IF NOT EXISTS FOR ()-[r:HAS_THEME]->() ON (r.source)"
]

async def create_indexes(uri=None, username=None, password=None):
    """
    Create all required indexes for optimal Neo4j performance.
    
    Args:
        uri: Neo4j URI (optional, uses config if not provided)
        username: Neo4j username (optional, uses config if not provided)  
        password: Neo4j password (optional, uses config if not provided)
        
    Returns:
        int: Number of indexes processed
    """
    uri = uri or settings.NEO4J_URI
    username = username or settings.NEO4J_USER
    password = password or settings.NEO4J_PASSWORD
    
    # Counter for successful operations
    total_processed = 0
    
    async with AsyncGraphDatabase.driver(uri, auth=(username, password)) as driver:
        async with driver.session() as session:
            # Create node indexes
            for statement in INDEX_STATEMENTS:
                try:
                    await session.run(statement)
                    logger.info(f"Successfully executed: {statement}")
                    total_processed += 1
                except ClientError as e:
                    # Log the error but continue with other indexes
                    logger.error(f"Error creating index: {str(e)}")
                    
            # Create relationship indexes
            for statement in RELATIONSHIP_INDEXES:
                try:
                    await session.run(statement)
                    logger.info(f"Successfully executed: {statement}")
                    total_processed += 1
                except ClientError as e:
                    # Some Neo4j versions may not support certain index types
                    logger.error(f"Error creating relationship index: {str(e)}")
            
            # Verify indexes were created
            result = await session.run("SHOW INDEXES")
            indexes = []
            async for record in result:
                indexes.append(record)
            
            logger.info(f"Total indexes in database: {len(indexes)}")
            return total_processed

async def drop_all_indexes():
    """
    Drop all indexes for testing or reset purposes.
    
    Returns:
        int: Number of indexes dropped
    """
    # Counter for successful operations
    total_dropped = 0
    
    async with AsyncGraphDatabase.driver(
        settings.NEO4J_URI, 
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    ) as driver:
        async with driver.session() as session:
            # Get all indexes
            result = await session.run("SHOW INDEXES")
            indexes = []
            async for record in result:
                indexes.append(record)
            
            # Drop each index
            for index in indexes:
                try:
                    # Format depends on Neo4j version
                    index_name = index.get("name", index.get("indexName", ""))
                    await session.run(f"DROP INDEX {index_name} IF EXISTS")
                    logger.info(f"Dropped index: {index_name}")
                    total_dropped += 1
                except Exception as e:
                    logger.error(f"Error dropping index: {str(e)}")
            
            return total_dropped 