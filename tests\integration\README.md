# Integration Tests

This directory contains integration tests that test the interaction between different components of the Tahimoto application, including API endpoints, frontend-backend communication, and end-to-end workflows.

## Test Files

### API Integration Tests
- **`test_admin_api.py`** - Tests the admin theme relations API endpoints and complete workflow
- **`test_contextual_themes.py`** - Tests contextual theme analysis and recommendation endpoints
- **`test_theme_mapping.py`** - Tests theme mapping functionality and cross-media mapping

### Frontend Integration Tests
- **`test_frontend_api.py`** - Tests frontend API communication and data flow
- **`test_frontend_direct.py`** - Tests direct frontend functionality and components
- **`test_fixed_frontend.py`** - Tests for frontend fixes and specific UI scenarios

## Running Tests

All integration tests should be run from the project root directory:

```bash
# Run all integration tests
python -m pytest tests/integration/

# Run specific test file
python tests/integration/test_admin_api.py
python tests/integration/test_contextual_themes.py

# Run with verbose output
python -m pytest tests/integration/ -v
```

## Prerequisites

- Backend server running on `http://localhost:8000`
- Frontend server running (if testing frontend integration)
- Database properly seeded with test data
- Redis server running (for caching tests)
- All required dependencies installed

## Test Environment

These tests require a running application environment and may modify data. It's recommended to run them against a test database rather than production data.

## Notes

- Tests may take longer to run as they involve actual HTTP requests and database operations
- Some tests may require specific test data to be present in the database
- Tests should be run in isolation to avoid interference between test cases
