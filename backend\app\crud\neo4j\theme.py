"""
Theme CRUD operations for Neo4j database.
This module provides operations for managing themes and theme mappings in Neo4j.
"""
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4
from neo4j import AsyncSession
from app.core.logging import get_logger
from app.crud.neo4j.base import CRUDBase
from app.schemas.theme import ThemeCreate, ThemeUpdate
from app.services.id_service import IdService
from datetime import datetime

logger = get_logger("crud_neo4j_theme")

class CRUDTheme(CRUDBase[ThemeCreate, ThemeUpdate]):
    """
    CRUD operations for themes in Neo4j.
    Extends the base CRUD class with theme-specific operations.
    """
    
    def __init__(self):
        """Initialize with the 'Theme' label."""
        super().__init__("Theme")
    
    async def get(self, session: AsyncSession, id: str) -> Optional[Dict[str, Any]]:
        """
        Get a theme by ID with standardized ID handling.
        
        Args:
            session: Neo4j session
            id: Theme ID (with or without 'theme_' prefix)
            
        Returns:
            Theme properties as a dictionary, or None if not found
        """
        # Standardize ID using IdService
        standard_id = IdService.standardize_id(id, "theme")
        
        # Convert to database format for query
        db_id = IdService.to_database_id(standard_id, "theme")
        
        try:
            logger.debug(f"Fetching theme with ID: {standard_id} (DB ID: {db_id})")
            
            query = """
            MATCH (t:Theme {id: $id})
            RETURN t
            """
            
            result = await session.run(query, {"id": db_id})
            record = await result.single()
            
            if record:
                node_data = dict(record["t"])
                
                # Ensure ID is standardized in response
                if "id" in node_data:
                    node_data["id"] = IdService.standardize_id(node_data["id"], "theme")
                
                logger.debug(f"Found theme: {standard_id}")
                return node_data
            
            logger.debug(f"No theme found with ID: {standard_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error fetching theme {standard_id}: {str(e)}")
            raise
    
    async def get_by_name(self, session: AsyncSession, name: str) -> Optional[Dict[str, Any]]:
        """
        Get a theme by name.
        
        Args:
            session: Neo4j session
            name: Theme name
            
        Returns:
            Theme properties as a dictionary, or None if not found
        """
        try:
            logger.debug(f"Fetching theme with name: {name}")
            
            query = """
            MATCH (t:Theme {name: $name})
            RETURN t
            """
            
            result = await session.run(query, {"name": name})
            record = await result.single()
            
            if record:
                node_data = dict(record["t"])
                
                # Ensure ID is standardized in response
                if "id" in node_data:
                    node_data["id"] = IdService.standardize_id(node_data["id"], "theme")
                
                logger.debug(f"Found theme: {name}")
                return node_data
            
            logger.debug(f"No theme found with name: {name}")
            return None
            
        except Exception as e:
            logger.error(f"Error fetching theme by name {name}: {str(e)}")
            raise
    
    async def get_multi(
        self, 
        session: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get multiple themes with optional status filtering.
        
        Args:
            session: Neo4j session
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Optional status filter
            
        Returns:
            List of theme dictionaries
        """
        try:
            logger.debug(f"Fetching themes: skip={skip}, limit={limit}, status={status}")
            
            where_clause = ""
            params = {
                "skip": skip,
                "limit": limit
            }
            
            if status:
                where_clause = "WHERE t.status = $status"
                params["status"] = status
            
            query = f"""
            MATCH (t:Theme)
            {where_clause}
            RETURN t
            ORDER BY t.name
            SKIP $skip
            LIMIT $limit
            """
            
            result = await session.run(query, params)
            records = await result.fetch(limit)
            
            themes = []
            for record in records:
                theme_data = dict(record["t"])
                
                # Ensure ID is standardized in response
                if "id" in theme_data:
                    theme_data["id"] = IdService.standardize_id(theme_data["id"], "theme")
                
                themes.append(theme_data)
            
            logger.debug(f"Found {len(themes)} themes")
            return themes
            
        except Exception as e:
            logger.error(f"Error fetching themes: {str(e)}")
            raise
    
    async def create(self, session: AsyncSession, *, obj_in: Union[Dict[str, Any], ThemeCreate]) -> Dict[str, Any]:
        """
        Create a new theme.
        
        Args:
            session: Neo4j session
            obj_in: Theme creation object
            
        Returns:
            Created theme as a dictionary
        """
        try:
            # Convert to dict if it's a Pydantic model
            obj_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump()
            
            # Generate ID if not provided
            if "id" not in obj_data or not obj_data["id"]:
                raw_id = obj_data.get("name", "").lower().replace(" ", "_")
                if not raw_id:
                    raw_id = str(uuid4())
                
                # Standardize ID using IdService
                obj_data["id"] = IdService.standardize_id(raw_id, "theme")
            else:
                # Ensure ID is standardized
                obj_data["id"] = IdService.standardize_id(obj_data["id"], "theme")
            
            # Convert to database format for storing
            db_id = IdService.to_database_id(obj_data["id"], "theme")
            
            # Set metadata
            obj_data["created_at"] = datetime.now().isoformat()
            obj_data["updated_at"] = obj_data["created_at"]
            
            # Check if theme with same ID already exists
            existing = await self.get(session, obj_data["id"])
            if existing:
                logger.warning(f"Theme with ID {obj_data['id']} already exists")
                return existing
                
            # Set default values if not provided
            if "status" not in obj_data:
                obj_data["status"] = "active"
                
            if "source" not in obj_data:
                obj_data["source"] = "manual"
                
            # Prepare object for database (using DB ID)
            db_obj = obj_data.copy()
            db_obj["id"] = db_id
                
            logger.debug(f"Creating theme: {obj_data}")
            
            query = """
            CREATE (t:Theme $theme)
            RETURN t
            """
            
            result = await session.run(query, {"theme": db_obj})
            record = await result.single()
            
            if record:
                theme_data = dict(record["t"])
                
                # Ensure ID is standardized in response
                theme_data["id"] = IdService.standardize_id(theme_data["id"], "theme")
                
                logger.debug(f"Created theme: {theme_data['id']}")
                return theme_data
            
            logger.error(f"Failed to create theme")
            raise ValueError("Failed to create theme")
            
        except Exception as e:
            logger.error(f"Error creating theme: {str(e)}")
            raise
    
    async def update(self, session: AsyncSession, *, id: str, obj_in: Union[Dict[str, Any], ThemeUpdate]) -> Optional[Dict[str, Any]]:
        """
        Update an existing theme.
        
        Args:
            session: Neo4j session
            id: Theme ID
            obj_in: Theme update object
            
        Returns:
            Updated theme as a dictionary, or None if not found
        """
        try:
            # Standardize ID using IdService
            standard_id = IdService.standardize_id(id, "theme")
            
            # Convert to database format for query
            db_id = IdService.to_database_id(standard_id, "theme")
            
            # Convert to dict if it's a Pydantic model
            update_data = obj_in if isinstance(obj_in, dict) else obj_in.model_dump(exclude_unset=True)
            
            # Don't allow ID to be updated
            if "id" in update_data:
                del update_data["id"]
                
            # Set updated_at timestamp
            update_data["updated_at"] = datetime.now().isoformat()
            
            logger.debug(f"Updating theme {standard_id} with data: {update_data}")
            
            # Build update SET clause
            set_parts = []
            params = {"id": db_id}
            
            for key, value in update_data.items():
                set_parts.append(f"t.{key} = ${key}")
                params[key] = value
                
            set_clause = ", ".join(set_parts)
            
            query = f"""
            MATCH (t:Theme {{id: $id}})
            SET {set_clause}
            RETURN t
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                logger.warning(f"Theme {standard_id} not found for update")
                return None
                
            theme_data = dict(record["t"])
            
            # Ensure ID is standardized in response
            theme_data["id"] = IdService.standardize_id(theme_data["id"], "theme")
            
            logger.debug(f"Updated theme: {standard_id}")
            return theme_data
            
        except Exception as e:
            logger.error(f"Error updating theme {id}: {str(e)}")
            raise
    
    async def remove(self, session: AsyncSession, *, id: str) -> bool:
        """
        Remove a theme.
        
        Args:
            session: Neo4j session
            id: Theme ID
            
        Returns:
            True if successfully removed, False otherwise
        """
        try:
            # Standardize ID using IdService
            standard_id = IdService.standardize_id(id, "theme")
            
            # Convert to database format for query
            db_id = IdService.to_database_id(standard_id, "theme")
            
            logger.debug(f"Removing theme: {standard_id} (DB ID: {db_id})")
            
            query = """
            MATCH (t:Theme {id: $id})
            DETACH DELETE t
            """
            
            result = await session.run(query, {"id": db_id})
            summary = await result.summary()
            
            # Check if any nodes were deleted
            counters = summary.counters
            deleted = counters.nodes_deleted
            
            success = deleted > 0
            logger.debug(f"Theme removal result: deleted={deleted}, success={success}")
            return success
            
        except Exception as e:
            logger.error(f"Error removing theme {id}: {str(e)}")
            raise
    
    async def create_relationship(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        theme_id: str,
        mapping_type: str = "primary",
        mapping_strength: float = 0.5,
        source: str = "manual",
        notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a relationship between a source entity and a theme.
        
        Args:
            session: Neo4j session
            source_type: Type of source entity (e.g., "Story")
            source_id: ID of the source entity
            theme_id: ID of the theme
            mapping_type: Type of mapping (e.g., "primary", "secondary")
            mapping_strength: Strength of the mapping (0.0 to 1.0)
            source: Source of the mapping (e.g., "manual", "ai")
            notes: Optional notes about the mapping
            
        Returns:
            Created relationship as a dictionary
        """
        try:
            # Standardize IDs using IdService
            if source_type.lower() == "story":
                standard_source_id = IdService.standardize_id(source_id, "story")
                db_source_id = IdService.to_database_id(standard_source_id, "story")
            else:
                standard_source_id = source_id
                db_source_id = source_id
                
            standard_theme_id = IdService.standardize_id(theme_id, "theme")
            db_theme_id = IdService.to_database_id(standard_theme_id, "theme")
            
            logger.info(f"Creating theme mapping: {source_type}:{standard_source_id} -> {standard_theme_id}")
            
            # Generate a mapping ID
            mapping_id = f"mapping_{source_type.lower()}_{db_source_id}_{db_theme_id}"
            
            # Set relationship properties
            props = {
                "id": mapping_id,
                "type": mapping_type,
                "strength": mapping_strength,
                "source": source,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }
            
            if notes:
                props["notes"] = notes
            
            # Convert keys to strings
            props_str = "{"
            props_str += ", ".join([f"{k}: ${k}" for k in props.keys()])
            props_str += "}"
            
            query = f"""
            MATCH (s:{source_type} {{id: $source_id}}), (t:Theme {{id: $theme_id}})
            MERGE (s)-[r:HAS_THEME {props_str}]->(t)
            RETURN s, r, t
            """
            
            params = {
                "source_id": db_source_id,
                "theme_id": db_theme_id,
                **props
            }
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                logger.error(f"Failed to create theme mapping")
                raise ValueError("Failed to create theme mapping")
                
            source_node = dict(record["s"])
            relationship = dict(record["r"])
            theme_node = dict(record["t"])
            
            # Standardize IDs in response
            if "id" in source_node:
                source_node["id"] = standard_source_id
                
            if "id" in theme_node:
                theme_node["id"] = standard_theme_id
                
            # Create response with standardized IDs
            response = {
                "id": mapping_id,
                "source_id": standard_source_id,
                "theme_id": standard_theme_id,
                "source_type": source_type,
                **relationship
            }
            
            logger.debug(f"Created theme mapping: {mapping_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error creating relationship: {str(e)}")
            raise
    
    async def update_relationship(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        theme_id: str,
        mapping_type: Optional[str] = None,
        mapping_strength: Optional[float] = None,
        source: Optional[str] = None,
        notes: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing theme relationship.
        
        Args:
            session: Neo4j session
            source_type: Type of source entity (e.g., "Story")
            source_id: ID of the source entity
            theme_id: ID of the theme
            mapping_type: Optional type of mapping to update
            mapping_strength: Optional strength of the mapping to update
            source: Optional source of the mapping to update
            notes: Optional notes about the mapping to update
            
        Returns:
            Updated relationship as a dictionary, or None if not found
        """
        try:
            # Standardize IDs using IdService
            if source_type.lower() == "story":
                standard_source_id = IdService.standardize_id(source_id, "story")
                db_source_id = IdService.to_database_id(standard_source_id, "story")
            else:
                standard_source_id = source_id
                db_source_id = source_id
                
            standard_theme_id = IdService.standardize_id(theme_id, "theme")
            db_theme_id = IdService.to_database_id(standard_theme_id, "theme")
            
            logger.info(f"Updating theme mapping: {source_type}:{standard_source_id} -> {standard_theme_id}")
            
            # Prepare update SET clause
            updates = {}
            if mapping_type is not None:
                updates["type"] = mapping_type
            if mapping_strength is not None:
                updates["strength"] = mapping_strength
            if source is not None:
                updates["source"] = source
            if notes is not None:
                updates["notes"] = notes
                
            # Always update the updated_at timestamp
            updates["updated_at"] = datetime.now().isoformat()
            
            if not updates:
                logger.warning("No updates provided for theme mapping")
                return await self.get_relationship(
                    session, 
                    source_type=source_type, 
                    source_id=source_id, 
                    theme_id=theme_id
                )
                
            # Build SET clause
            set_parts = []
            params = {
                "source_id": db_source_id,
                "theme_id": db_theme_id
            }
            
            for key, value in updates.items():
                set_parts.append(f"r.{key} = ${key}")
                params[key] = value
                
            set_clause = ", ".join(set_parts)
            
            query = f"""
            MATCH (s:{source_type} {{id: $source_id}})-[r:HAS_THEME]->(t:Theme {{id: $theme_id}})
            SET {set_clause}
            RETURN s, r, t
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                logger.warning(f"No relationship found to update: {source_type}:{standard_source_id} -> {standard_theme_id}")
                return None
                
            source_node = dict(record["s"])
            relationship = dict(record["r"])
            theme_node = dict(record["t"])
            
            # Standardize IDs in response
            if "id" in source_node:
                source_node["id"] = standard_source_id
                
            if "id" in theme_node:
                theme_node["id"] = standard_theme_id
                
            # Generate mapping ID
            mapping_id = f"mapping_{source_type.lower()}_{db_source_id}_{db_theme_id}"
            
            # Create response with standardized IDs
            response = {
                "id": mapping_id,
                "source_id": standard_source_id,
                "theme_id": standard_theme_id,
                "source_type": source_type,
                **relationship
            }
            
            logger.debug(f"Updated theme mapping: {mapping_id}")
            return response
            
        except Exception as e:
            logger.error(f"Error updating relationship: {str(e)}")
            raise
    
    async def remove_relationship(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        theme_id: str
    ) -> bool:
        """
        Remove a HAS_THEME relationship.
        
        Args:
            session: Neo4j session
            source_type: Type of source (anime, book, movie, etc.)
            source_id: ID of the source
            theme_id: ID of the theme
            
        Returns:
            True if the relationship was deleted, False if it didn't exist
        """
        try:
            # Standardize IDs
            if source_type.lower() == "story":
                source_id = IdService.standardize_id(source_id, "story")
            theme_id = IdService.standardize_id(theme_id, "theme")
            
            logger.info(f"Removing theme mapping: {source_type}:{source_id} -> {theme_id}")
            
            query = f"""
            MATCH (s:{source_type} {{id: $source_id}})-[r:HAS_THEME]->(t:Theme {{id: $theme_id}})
            DELETE r
            RETURN count(r) as deleted
            """
            
            result = await session.run(
                query, 
                {
                    "source_id": source_id,
                    "theme_id": theme_id
                }
            )
            record = await result.single()
            
            deleted = record["deleted"] if record else 0
            
            if deleted > 0:
                logger.info(f"Removed theme mapping: {source_type}:{source_id} -> {theme_id}")
                
                # Invalidate related caches
                from app.services.theme_redis import theme_redis
                await theme_redis.invalidate_related_caches(source_type, source_id)
                await theme_redis.invalidate_related_caches("theme", theme_id)
                
                return True
            else:
                logger.warning(f"No theme mapping found to remove")
                return False
                
        except Exception as e:
            logger.error(f"Error removing theme relationship: {str(e)}")
            raise
    
    async def get_relationship(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        theme_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a HAS_THEME relationship between a story/media and a theme.
        
        Args:
            session: Neo4j session
            source_type: Type of source (anime, book, movie, etc.)
            source_id: ID of the source
            theme_id: ID of the theme
            
        Returns:
            Dictionary with source, theme, and relationship properties,
            or None if the relationship doesn't exist
        """
        try:
            # Standardize IDs
            if source_type.lower() == "story":
                source_id = IdService.standardize_id(source_id, "story")
            theme_id = IdService.standardize_id(theme_id, "theme")
            
            logger.debug(f"Fetching theme mapping: {source_type}:{source_id} -> {theme_id}")
            
            query = f"""
            MATCH (s:{source_type} {{id: $source_id}})-[r:HAS_THEME]->(t:Theme {{id: $theme_id}})
            RETURN s, t, r
            """
            
            result = await session.run(
                query, 
                {
                    "source_id": source_id,
                    "theme_id": theme_id
                }
            )
            record = await result.single()
            
            if not record:
                logger.debug(f"No theme mapping found")
                return None
                
            mapping = {
                "source": dict(record["s"]),
                "theme": dict(record["t"]),
                "relationship": dict(record["r"])
            }
            
            # Ensure IDs have proper prefixes
            if "id" in mapping["theme"] and not mapping["theme"]["id"].startswith("theme_"):
                mapping["theme"]["id"] = f"theme_{mapping['theme']['id']}"
                
            if "id" in mapping["source"] and source_type.lower() == "story" and not mapping["source"]["id"].startswith("story_"):
                mapping["source"]["id"] = f"story_{mapping['source']['id']}"
            
            logger.debug(f"Found theme mapping: {source_type}:{source_id} -> {theme_id}")
            return mapping
            
        except Exception as e:
            logger.error(f"Error fetching theme mapping: {str(e)}")
            raise
    
    async def get_analysis(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get theme analysis for a source.
        
        Args:
            session: Neo4j session
            source_type: Type of source (anime, book, movie, etc.)
            source_id: ID of the source
            
        Returns:
            Dictionary with theme mappings grouped by mapping type
        """
        try:
            # Standardize ID
            if source_type.lower() == "story":
                source_id = IdService.standardize_id(source_id, "story")
                
            logger.debug(f"Getting theme analysis for {source_type}:{source_id}")
            
            # Use string formatting for the node label
            query = f"""
            MATCH (s:{source_type} {{id: $source_id}})-[r:HAS_THEME]->(t:Theme)
            RETURN s, t, r
            ORDER BY r.mapping_strength DESC
            """
            
            result = await session.run(
                query, 
                {
                    "source_id": source_id
                }
            )
            records = await result.fetch(100)  # Fetch up to 100 records
            
            # Convert records to mappings
            mappings = []
            for record in records:
                mapping = {
                    "source": dict(record["s"]),
                    "theme": dict(record["t"]),
                    "relationship": dict(record["r"])
                }
                
                # Ensure IDs have proper prefixes
                if "id" in mapping["theme"] and not mapping["theme"]["id"].startswith("theme_"):
                    mapping["theme"]["id"] = f"theme_{mapping['theme']['id']}"
                    
                if "id" in mapping["source"] and source_type.lower() == "story" and not mapping["source"]["id"].startswith("story_"):
                    mapping["source"]["id"] = f"story_{mapping['source']['id']}"
                
                mappings.append(mapping)
                
            logger.debug(f"Found {len(mappings)} theme mappings")
            
            # Group mappings by type
            analysis = {
                "themes": mappings,
                "primary_themes": [],
                "secondary_themes": [],
                "mood_themes": [],
                "character_themes": [],
                "plot_themes": []
            }
            
            for mapping in mappings:
                mapping_type = mapping["relationship"].get("mapping_type", "primary")
                if mapping_type == "primary":
                    analysis["primary_themes"].append(mapping)
                elif mapping_type == "secondary":
                    analysis["secondary_themes"].append(mapping)
                elif mapping_type == "mood":
                    analysis["mood_themes"].append(mapping)
                elif mapping_type == "character":
                    analysis["character_themes"].append(mapping)
                elif mapping_type == "plot":
                    analysis["plot_themes"].append(mapping)
            
            # Log analysis summary
            for category, items in analysis.items():
                logger.debug(f"{category}: {len(items)} mappings")
                
            return analysis
            
        except Exception as e:
            logger.error(f"Error getting theme analysis for {source_type}:{source_id}: {str(e)}")
            raise
    
    async def get_mappings_by_type(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        mapping_type: str
    ) -> List[Dict[str, Any]]:
        """
        Get theme mappings of a specific type for a source.
        
        Args:
            session: Neo4j session
            source_type: Type of source (anime, book, movie, etc.)
            source_id: ID of the source
            mapping_type: Type of mapping (primary, secondary, mood, etc.)
            
        Returns:
            List of theme mappings
        """
        try:
            logger.debug(f"Fetching {mapping_type} theme mappings for {source_type}:{source_id}")
            
            query = """
            MATCH (s:$source_type {id: $source_id})-[r:HAS_THEME {mapping_type: $mapping_type}]->(t:Theme)
            RETURN s, t, r
            ORDER BY r.mapping_strength DESC
            """
            
            result = await session.run(
                query, 
                {
                    "source_type": source_type,
                    "source_id": source_id,
                    "mapping_type": mapping_type
                }
            )
            records = await result.fetch()
            
            # Convert records to mappings
            mappings = []
            for record in records:
                mapping = {
                    "source": dict(record["s"]),
                    "theme": dict(record["t"]),
                    "relationship": dict(record["r"])
                }
                mappings.append(mapping)
                
            logger.debug(f"Found {len(mappings)} {mapping_type} theme mappings")
            return mappings
            
        except Exception as e:
            logger.error(f"Error fetching {mapping_type} theme mappings for {source_type}:{source_id}: {str(e)}")
            raise
    
    async def find_similar_media(
        self,
        session: AsyncSession,
        *,
        source_type: str,
        source_id: str,
        target_types: Optional[List[str]] = None,
        limit: int = 10,
        min_similarity: float = 0.1
    ) -> List[Dict[str, Any]]:
        """
        Find media with similar themes.
        
        Args:
            session: Neo4j session
            source_type: Type of source media (anime, book, movie, etc.)
            source_id: ID of the source media
            target_types: Optional list of media types to include in results
            limit: Maximum number of results to return
            min_similarity: Minimum similarity score (0.0 to 1.0)
            
        Returns:
            List of similar media with similarity scores
        """
        try:
            logger.debug(f"Finding similar media for {source_type}:{source_id}")
            
            # Standardize the source ID format
            if not source_id.startswith("story_"):
                source_id = f"story_{source_id}"
            
            # Build WHERE clause for target types
            where_clause = "WHERE s2.id <> $source_id" # Avoid recommending the same story
            params = {
                "source_id": source_id,
                "min_similarity": min_similarity,
                "limit": limit
            }
            
            if target_types:
                types_clause = " AND s2.media_type IN $target_types"
                where_clause += types_clause
                params["target_types"] = target_types
            
            # If we want cross-media recommendations only
            elif source_type:
                where_clause += f" AND s2.media_type <> '{source_type}'"
            
            # Optimized query that leverages our indexes on relationship properties
            # Using MATCH pattern with direct path to improve traversal performance
            # Using parameterized properties to leverage our new indexes
            query = f"""
            // First find the themes for our source story (using the id index)
            MATCH (s1:Story {{id: $source_id}})-[r1:HAS_THEME]->(t:Theme)
            // Store the themes and their strengths
            WITH t, r1.mapping_strength AS s1_strength, r1.mapping_type AS s1_type
            // Find stories that share these themes (using the theme id index)
            MATCH (t)<-[r2:HAS_THEME]-(s2:Story)
            {where_clause}
            // Calculate similarity based on match quality and mapping strength
            WITH s2, 
                 sum(s1_strength * r2.mapping_strength) AS thematic_similarity,
                 count(t) AS shared_theme_count,
                 collect(t.name) AS shared_themes
            // Filter by minimum similarity
            WHERE thematic_similarity >= $min_similarity
            // Get popularity/score for ranking boost, using our new indexes
            WITH s2, thematic_similarity, shared_theme_count, shared_themes,
                 CASE WHEN s2.popularity IS NULL THEN 0 ELSE s2.popularity END AS popularity,
                 CASE WHEN s2.average_score IS NULL THEN 0 ELSE s2.average_score END AS score
            // Calculate a weighted score giving priority to thematic similarity
            WITH s2, thematic_similarity, shared_theme_count, shared_themes,
                 (thematic_similarity * 0.7) + (popularity * 0.1 / 10000) + (score * 0.2 / 100) AS weighted_score
            // Return results
            RETURN s2, 
                   thematic_similarity AS similarity,
                   shared_theme_count,
                   shared_themes,
                   weighted_score
            // Sort by our weighted score
            ORDER BY weighted_score DESC
            LIMIT $limit
            """
            
            result = await session.run(query, params)
            records = await result.fetch()
            
            similar_media = []
            for record in records:
                media = dict(record["s2"])
                # Round similarity to 3 decimal places for consistency
                media["similarity"] = round(record["similarity"], 3)
                media["shared_theme_count"] = record["shared_theme_count"]
                media["shared_themes"] = record["shared_themes"]
                similar_media.append(media)
            
            logger.debug(f"Found {len(similar_media)} similar media")
            return similar_media
            
        except Exception as e:
            logger.error(f"Error finding similar media for {source_type}:{source_id}: {str(e)}")
            raise
    
    async def get_theme_network(
        self,
        session: AsyncSession,
        *,
        theme_id: Optional[str] = None,
        min_shared: int = 2,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Get the network of related themes based on co-occurrence in media.
        
        Args:
            session: Neo4j session
            theme_id: Optional theme ID to center the network on
            min_shared: Minimum number of shared media to consider themes related
            limit: Maximum number of theme relationships to return
            
        Returns:
            List of theme relationships with shared media count
        """
        try:
            logger.debug(f"Getting theme network{' for theme ' + theme_id if theme_id else ''}")
            
            params = {
                "min_shared": min_shared,
                "limit": limit
            }
            
            if theme_id:
                # Get themes related to a specific theme
                query = """
                MATCH (t1:Theme {id: $theme_id})<-[:HAS_THEME]-(s:Story)-[:HAS_THEME]->(t2:Theme)
                WHERE t1 <> t2
                WITH t1, t2, count(s) AS shared_stories
                WHERE shared_stories >= $min_shared
                RETURN t1, t2, shared_stories
                ORDER BY shared_stories DESC
                LIMIT $limit
                """
                params["theme_id"] = theme_id
            else:
                # Get the most strongly related themes overall
                query = """
                MATCH (t1:Theme)<-[:HAS_THEME]-(s:Story)-[:HAS_THEME]->(t2:Theme)
                WHERE t1 <> t2 AND id(t1) < id(t2)  // Avoid duplicates
                WITH t1, t2, count(s) AS shared_stories
                WHERE shared_stories >= $min_shared
                RETURN t1, t2, shared_stories
                ORDER BY shared_stories DESC
                LIMIT $limit
                """
            
            result = await session.run(query, params)
            records = await result.fetch(1000)  # Fetch up to 1000 records
            
            theme_relationships = []
            for record in records:
                relationship = {
                    "theme1": dict(record["t1"]),
                    "theme2": dict(record["t2"]),
                    "shared_stories": record["shared_stories"]
                }
                theme_relationships.append(relationship)
                
            logger.debug(f"Found {len(theme_relationships)} theme relationships")
            return theme_relationships
            
        except Exception as e:
            logger.error(f"Error getting theme network: {str(e)}")
            raise
    
    async def create_theme_relationship(
        self,
        session: AsyncSession,
        *,
        source_theme_id: str,
        target_theme_id: str,
        relationship_type: str,
        properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a relationship between two themes.
        
        Args:
            session: Neo4j session
            source_theme_id: Source theme ID
            target_theme_id: Target theme ID
            relationship_type: Relationship type (PARENT_OF, COMPLEMENTS, CONTRASTS, RELATED_TO)
            properties: Optional properties for the relationship
            
        Returns:
            Created relationship as a dictionary
        """
        # Ensure IDs have theme_ prefix
        source_theme_id = IdService.standardize_id(source_theme_id, "theme")
        target_theme_id = IdService.standardize_id(target_theme_id, "theme")
        
        # Set default properties if not provided
        if properties is None:
            properties = {}
        
        # Add timestamp to properties
        if "created_at" not in properties:
            properties["created_at"] = datetime.now().isoformat()
            
        # Add strength property if not provided
        if "strength" not in properties and relationship_type in ["COMPLEMENTS", "CONTRASTS", "RELATED_TO"]:
            properties["strength"] = 0.5
            
        # Build properties part of query
        props_list = []
        params = {
            "source_id": source_theme_id,
            "target_id": target_theme_id
        }
        
        for key, value in properties.items():
            prop_name = f"prop_{key}"
            props_list.append(f"{key}: ${prop_name}")
            params[prop_name] = value
            
        props_str = "{" + ", ".join(props_list) + "}" if props_list else ""
        
        # Create the relationship
        query = f"""
        MATCH (source:Theme {{id: $source_id}})
        MATCH (target:Theme {{id: $target_id}})
        MERGE (source)-[r:{relationship_type} {props_str}]->(target)
        RETURN source, r, target
        """
        
        result = await session.run(query, params)
        record = await result.single()
        
        if not record:
            logger.error(f"Failed to create {relationship_type} relationship between {source_theme_id} and {target_theme_id}")
            raise ValueError(f"Failed to create theme relationship")
            
        return {
            "source": dict(record["source"]),
            "relationship": dict(record["r"]),
            "target": dict(record["target"])
        }

    async def get_themes_by_category(
        self,
        session: AsyncSession,
        *,
        category: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get themes by category.
        
        Args:
            session: Neo4j session
            category: Category to filter by (MOOD, NARRATIVE_STRUCTURE, etc.)
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of themes in the specified category
        """
        query = """
        MATCH (t:Theme {category: $category})
        RETURN t
        ORDER BY t.name
        SKIP $skip LIMIT $limit
        """
        
        result = await session.run(
            query,
            category=category,
            skip=skip,
            limit=limit
        )
        
        themes = []
        async for record in result:
            theme_data = dict(record["t"])
            themes.append(theme_data)
            
        return themes

# Create an instance for use in other modules
theme = CRUDTheme() 