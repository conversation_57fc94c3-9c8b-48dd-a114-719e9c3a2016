"""
Theme mapper interface definitions using Abstract Base Classes.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional


class ThemeMapper(ABC):
    """Base interface for all media-to-universal theme mappers."""
    
    @abstractmethod
    def map_to_universal_themes(self, media_info: Dict[str, Any]) -> List[str]:
        """
        Maps media-specific information to a list of universal themes.
        
        Args:
            media_info: Dictionary containing media metadata (genres, tags, etc.)
            
        Returns:
            List of universal theme identifiers
        """
        pass
    
    @abstractmethod
    def get_confidence_scores(self, media_info: Dict[str, Any]) -> Dict[str, float]:
        """
        Returns confidence scores for theme mappings.
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            Dictionary mapping theme identifiers to confidence scores (0.0-1.0)
        """
        pass
    
    @abstractmethod
    def get_mapping_types(self, media_info: Dict[str, Any]) -> Dict[str, str]:
        """
        Determines the mapping type for each theme (primary, secondary, mood, etc.).
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            Dictionary mapping theme identifiers to mapping types
        """
        pass
    
    def get_full_theme_mapping(self, media_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Gets complete theme mapping information including theme IDs, 
        confidence scores, and mapping types.
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            List of dictionaries with complete theme mapping information
        """
        themes = self.map_to_universal_themes(media_info)
        confidence_scores = self.get_confidence_scores(media_info)
        mapping_types = self.get_mapping_types(media_info)
        
        result = []
        for theme_id in themes:
            result.append({
                "theme_id": theme_id,
                "confidence": confidence_scores.get(theme_id, 0.5),
                "mapping_type": mapping_types.get(theme_id, "secondary")
            })
        
        return result
        
    def analyze_context(self, media_info: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        Optional method to analyze context for theme mappings.
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            Dictionary mapping theme identifiers to context descriptions,
            or None if context analysis is not implemented
        """
        return None 