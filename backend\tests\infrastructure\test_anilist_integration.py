"""
Test script to validate AniList integration with Neo4j.

This script tests the AniList service and its integration with Neo4j,
ensuring that anime data can be fetched from AniList and stored in Neo4j.

Usage:
    python -m tests.infrastructure.test_anilist_integration
"""
import asyncio
import logging
import sys
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Import Neo4j session and driver
from app.db.neo4j_session import get_db_session, driver
from app.services.anilist import anilist_service
from app.crud.neo4j.story import story

# Test anime IDs (popular anime that should exist on AniList)
TEST_ANIME_IDS = ["1", "5114", "21", "16498"]

async def test_anilist_fetch():
    """Test fetching anime data from AniList."""
    logger.info("Testing AniList fetch...")
    
    try:
        # Test with a known anime ID
        anime_id = TEST_ANIME_IDS[0]  # Cowboy Bebop
        logger.info(f"Fetching anime with ID: {anime_id}")
        
        # Fetch from AniList
        result = await anilist_service.get_anime_details(anime_id)
        
        # Validate result
        if not result or "data" not in result or "Media" not in result["data"]:
            logger.error(f"Failed to fetch anime data: {result}")
            return False
        
        media = result["data"]["Media"]
        logger.info(f"Successfully fetched anime: {media.get('title', {}).get('romaji')}")
        
        # Check essential fields
        essential_fields = ["id", "title", "description", "genres"]
        for field in essential_fields:
            if field not in media:
                logger.error(f"Missing essential field: {field}")
                return False
        
        logger.info("AniList fetch test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing AniList fetch: {str(e)}", exc_info=True)
        return False

async def neo4j_store_and_retrieve(session, story_data):
    """
    Helper function to store and retrieve a story in Neo4j.
    
    Args:
        session: Neo4j database session
        story_data: Story data to store
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Extract key fields for verification later
        original_id = story_data.get('external_id', '')
        original_title = story_data.get('title_romaji', '') or story_data.get('title', '')
        
        logger.info(f"Storing story in Neo4j: {original_title}")
        logger.info(f"External ID being stored: {original_id}")

        # Store data
        stored_story = await story.create_or_update(session, obj_in=story_data)
        if not stored_story:
            logger.error("Failed to store story in Neo4j")
            return False
            
        # No need to explicitly commit - the Neo4j driver handles this automatically
        # when using the AsyncSession context manager
        
        # Get the stored external_id (which could have been modified during storage)
        stored_external_id = stored_story.get('external_id', '')
        logger.info(f"Story stored with external_id: {stored_external_id}")
        
        # Try to fetch the stored story using the stored external_id
        fetched_story = await story.get_by_external_id(session, 
                                                     external_id=stored_external_id)
        
        # If not found, try without the "story_" prefix
        if not fetched_story and stored_external_id.startswith("story_"):
            alternate_id = stored_external_id[6:]
            logger.info(f"Trying to fetch with alternate ID: {alternate_id}")
            fetched_story = await story.get_by_external_id(session,
                                                         external_id=alternate_id)
        
        # If still not found, try with the "story_" prefix
        if not fetched_story and not stored_external_id.startswith("story_"):
            alternate_id = f"story_{stored_external_id}"
            logger.info(f"Trying to fetch with alternate ID: {alternate_id}")
            fetched_story = await story.get_by_external_id(session,
                                                         external_id=alternate_id)
                                                         
        if not fetched_story:
            logger.error(f"Could not retrieve story with any ID format: {stored_external_id}")
            return False
            
        # Verify essential data was retrieved correctly
        fetched_title = fetched_story.get('title_romaji', '') or fetched_story.get('title', '')
        fetched_id = fetched_story.get('external_id', '')
        
        if original_title != fetched_title:
            logger.warning(f"Title mismatch: Original: '{original_title}', Fetched: '{fetched_title}'")
            # Continue despite mismatch - may be acceptable
        
        # For IDs, normalize by removing prefix
        original_id_normalized = original_id.replace("story_", "")
        fetched_id_normalized = fetched_id.replace("story_", "")
        
        if original_id_normalized != fetched_id_normalized:
            logger.error(f"ID mismatch even after normalization: '{original_id_normalized}' vs '{fetched_id_normalized}'")
            return False
            
        logger.info(f"Successfully verified Neo4j storage and retrieval for {fetched_title}")
        return True
        
    except Exception as e:
        logger.error(f"Error in Neo4j store and retrieve: {str(e)}", exc_info=True)
        return False

async def test_anilist_to_neo4j():
    """Test storing AniList data in Neo4j."""
    logger.info("Testing AniList to Neo4j integration...")
    
    try:
        # Get a Neo4j session
        async for session in get_db_session():
            # Test with a known anime ID
            anime_id = TEST_ANIME_IDS[1]  # Fullmetal Alchemist: Brotherhood
            logger.info(f"Fetching anime with ID: {anime_id}")
            
            # Fetch from AniList
            result = await anilist_service.get_anime_details(anime_id)
            if not result or "data" not in result or "Media" not in result["data"]:
                logger.error(f"Failed to fetch anime data for ID {anime_id}")
                return False
                
            # Transform to story format
            story_data = anilist_service.transform_to_story(result)
            if not story_data:
                logger.error("Failed to transform AniList data to story format")
                return False
                
            # Test Neo4j storage and retrieval
            success = await neo4j_store_and_retrieve(session, story_data)
            return success
            
    except Exception as e:
        logger.error(f"Error testing AniList to Neo4j integration: {str(e)}", exc_info=True)
        return False

async def test_anilist_search():
    """Test searching anime on AniList."""
    logger.info("Testing AniList search...")
    
    try:
        # Search for a popular anime
        search_term = "Attack on Titan"
        logger.info(f"Searching for anime: {search_term}")
        
        # Search on AniList
        result = await anilist_service.search_anime(search_term)
        
        # Validate result
        if not result or "data" not in result or "Page" not in result["data"]:
            logger.error(f"Failed to search anime: {result}")
            return False
        
        media_items = result["data"]["Page"]["media"]
        if not media_items:
            logger.error("No search results found")
            return False
        
        logger.info(f"Found {len(media_items)} search results")
        
        # Check if the first result is relevant
        first_result = media_items[0]
        title = first_result.get("title", {}).get("romaji", "")
        logger.info(f"First result: {title}")
        
        # Simple relevance check
        if search_term.lower() not in title.lower():
            logger.warning(f"First result may not be relevant: {title}")
        
        logger.info("AniList search test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing AniList search: {str(e)}", exc_info=True)
        return False

async def test_anilist_recommendations():
    """Test getting recommendations from AniList."""
    logger.info("Testing AniList recommendations...")
    
    try:
        # Get recommendations for a popular anime
        anime_id = TEST_ANIME_IDS[3]  # Attack on Titan
        logger.info(f"Getting recommendations for anime ID: {anime_id}")
        
        # Get recommendations from AniList
        result = await anilist_service.get_recommendations(anime_id)
        
        # Validate result
        if not result or "recommendations" not in result:
            logger.error(f"Failed to get recommendations: {result}")
            return False
        
        recommendations = result["recommendations"]
        logger.info(f"Found {len(recommendations)} recommendations")
        
        if not recommendations:
            logger.error("No recommendations found")
            return False
        
        # Check the first recommendation
        first_rec = recommendations[0]
        logger.info(f"First recommendation: {first_rec.get('title')}")
        
        # Check essential fields - these are the fields we actually use
        essential_fields = ["id", "title", "synopsis", "genres", "cover_image"]
        missing_fields = [field for field in essential_fields if field not in first_rec]
        
        if missing_fields:
            logger.error(f"Missing essential fields in recommendation: {missing_fields}")
            return False
        
        logger.info("AniList recommendations test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing AniList recommendations: {str(e)}", exc_info=True)
        return False

async def test_recommendations_to_neo4j():
    """Test storing and retrieving recommendations in Neo4j."""
    logger.info("Testing recommendations to Neo4j integration...")
    
    try:
        # Get a Neo4j session
        async for session in get_db_session():
            # Test with a known anime ID
            anime_id = TEST_ANIME_IDS[2]  # One Piece
            logger.info(f"Fetching recommendations for anime ID: {anime_id}")
            
            # Get recommendations from AniList
            result = await anilist_service.get_recommendations(anime_id, limit=3)
            
            if not result or "recommendations" not in result or not result["recommendations"]:
                logger.error("Failed to fetch recommendations")
                return False
                
            # Take the first recommendation and try to store it
            first_rec = result["recommendations"][0]
            
            # Make sure it has expected fields for Neo4j storage
            required_fields = ["id", "title", "media_type"]
            
            # Add external_id if not present (using id)
            if "external_id" not in first_rec and "id" in first_rec:
                first_rec["external_id"] = first_rec["id"]
                
            # Always add source field for recommendations
            first_rec["source"] = "anilist"
                
            # If any required fields are missing, log and skip
            missing_fields = [field for field in required_fields if field not in first_rec]
            if missing_fields:
                logger.error(f"Recommendation missing required fields: {missing_fields}")
                # Try to add minimal required fields for testing
                if "media_type" not in first_rec:
                    first_rec["media_type"] = "ANIME"
                    
            # Store and test retrieval using the helper function
            success = await neo4j_store_and_retrieve(session, first_rec)
            return success
            
    except Exception as e:
        logger.error(f"Error testing recommendations to Neo4j: {str(e)}", exc_info=True)
        return False

async def run_tests():
    """Run all AniList integration tests."""
    logger.info("Starting AniList integration tests...")
    
    # Define all tests with descriptions
    tests = [
        (test_anilist_fetch, "AniList fetch test"),
        (test_anilist_search, "AniList search test"),
        (test_anilist_recommendations, "AniList recommendations test"),
        (test_anilist_to_neo4j, "AniList to Neo4j integration test"),
        (test_recommendations_to_neo4j, "Recommendations to Neo4j test")
    ]
    
    # Track overall success
    success = True
    
    # Run each test and continue even if some fail
    for test_func, description in tests:
        try:
            logger.info(f"Running {description}...")
            test_result = await test_func()
            if not test_result:
                logger.error(f"{description} failed")
                success = False
            else:
                logger.info(f"{description} passed")
        except Exception as e:
            logger.error(f"Exception in {description}: {str(e)}", exc_info=True)
            success = False
    
    if success:
        logger.info("All AniList integration tests passed!")
    else:
        logger.error("Some AniList integration tests failed.")
        
    return success

if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(run_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1) 