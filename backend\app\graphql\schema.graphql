"""
GraphQL schema definition.
"""

enum MappingStatus {
  PENDING
  MAPPED
  NEEDS_REVIEW
  VERIFIED
}

enum ThemeMappingType {
  PRIMARY
  SECONDARY
  MOOD
  CHARACTER
  PLOT
}

enum ThemeCategory {
  MOOD
  NARRATIVE_STRUCTURE
  CHARACTER_DYNAMIC
  SETTING_TYPE
  UNCATEGORIZED
}

enum RelationshipType {
  COMPLEMENTS
  CONTRASTS
  REQUIRES
  SPECIALIZES
  PARENT_OF
  CHILD_OF
  EVOLVES_INTO
  EVOLVED_FROM
  SUBVERTS
  COEXISTS_WITH
  TENSIONS_WITH
  CULTURALLY_RELATED
  VISUALLY_SIMILAR
  NARRATIVELY_LINKED
}

enum ThemeSubCategory {
  # Mood sub-categories
  COZY
  TENSE
  MELANCHOLIC
  UPLIFTING
  CONTEMPLATIVE
  CHAOTIC
  
  # Narrative structure sub-categories
  HERO_JOURNEY
  SLICE_OF_LIFE
  EPISODIC
  LINEAR
  NONLINEAR
  INSTRUCTIONAL
  MYSTERY
  
  # Character dynamic sub-categories
  RIVALRY
  MENTORSHIP
  FRIENDSHIP
  ROMANCE
  FAMILY
  INTERNAL_CONFLICT
  ENSEMBLE
  
  # Setting type sub-categories
  URBAN
  RURAL
  FANTASY
  HISTORICAL
  FUTURISTIC
  WORKPLACE
  SCHOOL
  DOMESTIC
  
  # General sub-categories
  OTHER
}

enum ThemeDimension {
  NARRATIVE
  EMOTIONAL
  VISUAL
  CULTURAL
  PHILOSOPHICAL
  TECHNICAL
  TEMPORAL
  PSYCHOLOGICAL
}

enum EvolutionStage {
  FOUNDATION
  GUIDED
  ADAPTIVE
  EMERGENT
}

type Theme {
  id: ID!
  name: String!
  description: String!
  parentThemeId: String
  category: ThemeCategory
  subCategory: ThemeSubCategory
  dimensions: [ThemeDimension!]
  confidence: Float!
  createdAt: String!
  updatedAt: String!
  status: MappingStatus!
  version: Int
  deprecated: Boolean
  replacedBy: String
  culturalContext: [String!]
  evolutionStage: EvolutionStage
  implicitTags: [String!]
}

type ThemeRelationship {
  id: ID!
  sourceId: String!
  sourceName: String!
  targetId: String!
  targetName: String!
  type: RelationshipType!
  strength: Float
  tension: Float
  description: String
  dimensions: [ThemeDimension!]
  culturalContext: [String!]
  version: Int
  evidence: String
  confidence: Float
  createdAt: String!
  updatedAt: String!
}

type ThemeMapping {
  id: ID!
  sourceType: String!
  sourceId: String!
  themeId: String!
  theme: Theme!
  mappingStrength: Float!
  mappingType: ThemeMappingType!
  context: String
  createdAt: String!
  updatedAt: String!
  llmConfidence: Float
  needsReview: Boolean!
}

type ThemeAnalysis {
  themes: [ThemeMapping!]!
  primaryThemes: [ThemeMapping!]!
  secondaryThemes: [ThemeMapping!]!
  moodThemes: [ThemeMapping!]!
  characterThemes: [ThemeMapping!]!
  plotThemes: [ThemeMapping!]!
  confidence: Float!
  lastAnalyzed: String!
}

type ThemeStats {
  totalThemes: Int!
  mappedCount: Int!
  pendingCount: Int!
  needsReviewCount: Int!
  cacheHitRate: Float!
}

input TagInput {
  name: String!
  category: String!
  description: String
  rank: Int
}

input ThemeInput {
  name: String!
  description: String!
  parentThemeId: String
  confidence: Float = 1.0
}

input ThemeMappingInput {
  sourceType: String!
  sourceId: String!
  themeId: String!
  mappingStrength: Float = 1.0
  mappingType: ThemeMappingType!
  context: String
  llmConfidence: Float
  needsReview: Boolean = true
}

input ThemeUpdateInput {
  name: String
  description: String
  parentThemeId: String
  confidence: Float
  status: MappingStatus
}

input ThemeRelationshipInput {
  sourceId: String!
  targetId: String!
  type: RelationshipType!
  strength: Float = 0.5
  tension: Float = 0.0
  description: String
  dimensions: [ThemeDimension!]
  culturalContext: [String!]
  evidence: String
  confidence: Float
}

# Media-specific types
type AnimeInfo {
  id: ID!
  sourceId: String!
  title: String!
  alternativeTitles: [String!]
  description: String
  genres: [String!]
  tags: [String!]
  themeAnalysis: ThemeAnalysis
}

# Query root
type Query {
  # Theme queries
  theme(id: ID!): Theme
  themes(limit: Int = 10, offset: Int = 0): [Theme!]!
  themeStats: ThemeStats!
  
  # Theme relationship queries
  themeRelationship(id: ID!): ThemeRelationship
  themeRelationships(
    sourceId: String,
    targetId: String,
    type: RelationshipType,
    limit: Int = 100,
    offset: Int = 0
  ): [ThemeRelationship!]!
  
  # Theme mapping queries
  themeMapping(id: ID!): ThemeMapping
  themeMappings(
    sourceType: String,
    sourceId: String,
    themeId: String,
    limit: Int = 10,
    offset: Int = 0
  ): [ThemeMapping!]!
  
  # Analyze a specific item
  analyzeMedia(sourceType: String!, sourceId: String!): ThemeAnalysis
  
  # Media-specific queries
  anime(id: ID!): AnimeInfo
  similarAnime(id: ID!, limit: Int = 5): [AnimeInfo!]!
  
  # Recommendation queries
  recommendCrossmedia(
    sourceType: String!,
    sourceId: String!,
    targetTypes: [String!],
    limit: Int = 5
  ): [ThemeMapping!]!
  
  # Story theme analysis query
  analyzeStoryThemes(
    sourceType: String!,
    sourceId: String!,
    genres: [String!]!,
    tags: [TagInput!]!,
    description: String
  ): ThemeAnalysisResult!
}

# Analysis result type
type ThemeAnalysisResult {
  themes: [ThemeResult!]!
  stats: ThemeAnalysisStats
}

type ThemeResult {
  id: ID!
  name: String!
  category: String
  description: String
  confidence: Float!
  pattern_type: String!
  source: String
  reason: String
}

type ThemeAnalysisStats {
  total_matches: Int!
  confidence_avg: Float!
  top_categories: [String!]!
}

# Mutation root
type Mutation {
  # Theme mutations
  createTheme(input: ThemeInput!): Theme!
  updateTheme(id: ID!, input: ThemeUpdateInput!): Theme!
  deleteTheme(id: ID!): Boolean!
  
  # Theme relationship mutations
  createThemeRelationship(input: ThemeRelationshipInput!): ThemeRelationship!
  updateThemeRelationship(id: ID!, input: ThemeRelationshipInput!): ThemeRelationship!
  deleteThemeRelationship(id: ID!): Boolean!
  
  # Theme mapping mutations
  createThemeMapping(input: ThemeMappingInput!): ThemeMapping!
  updateThemeMapping(id: ID!, input: ThemeMappingInput!): ThemeMapping!
  deleteThemeMapping(id: ID!): Boolean!
  
  # Review mutations
  approveThemeMapping(id: ID!): ThemeMapping!
  rejectThemeMapping(id: ID!): Boolean!
  requestReview(id: ID!): ThemeMapping!
  
  # Story theme analysis query
  analyzeStoryThemes(
    sourceType: String!,
    sourceId: String!,
    genres: [String!]!,
    tags: [TagInput!]!,
    description: String
  ): ThemeAnalysisResult!
} 