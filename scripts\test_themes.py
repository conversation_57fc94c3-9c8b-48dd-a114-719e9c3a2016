import requests
import json

def test_themes_query():
    url = "http://localhost:3000/graphql"
    query = """
    query {
        themes {
            id
            name
            category
        }
    }
    """
    
    # Prepare the request
    payload = {"query": query}
    headers = {"Content-Type": "application/json"}
    
    # Send the request
    response = requests.post(url, json=payload, headers=headers)
    
    # Print the response
    print(f"Status Code: {response.status_code}")
    print("Response:")
    print(json.dumps(response.json(), indent=2))
    
    return response.json()

if __name__ == "__main__":
    test_themes_query() 