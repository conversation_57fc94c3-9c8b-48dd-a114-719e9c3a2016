from typing import Optional, Dict, Any, Union
from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.story import Story
from app.schemas.story import StoryCreate, StoryUpdate, StoryId
from app.core.logging import get_logger, log_redis_operation
import uuid

logger = get_logger("crud_story")

class CRUDStory:
    # Maximum age of story data before considered stale (in hours)
    STALE_THRESHOLD_HOURS = 24

    async def get_by_external_id(
        self, db: AsyncSession, *, external_id: StoryId
    ) -> Optional[Story]:
        """Get a story by its external ID."""
        try:
            # Remove 'story_' prefix if present
            external_id_str = str(external_id)
            if external_id_str.startswith("story_"):
                external_id_str = external_id_str[6:]

            logger.debug(f"Fetching story with external_id: {external_id_str}")
            result = await db.execute(
                select(Story).where(Story.external_id == external_id_str)
            )
            story = result.scalar_one_or_none()
            if story:
                logger.debug(f"Found story: {story.title_english or story.title_romaji}")
            else:
                logger.debug(f"No story found with external_id: {external_id_str}")
            return story
        except Exception as e:
            logger.error(f"Error fetching story with external_id {external_id}: {str(e)}")
            raise

    async def create(
        self, db: AsyncSession, *, obj_in: Union[StoryCreate, Dict[str, Any]]
    ) -> Story:
        """Create a new story."""
        try:
            if isinstance(obj_in, dict):
                create_data = obj_in
            else:
                create_data = obj_in.model_dump(exclude_unset=True)
            
            # Ensure created_at and updated_at are set
            now = datetime.utcnow()
            create_data["created_at"] = now
            create_data["updated_at"] = now
            
            # Ensure ID is a string and has the correct prefix
            if "id" not in create_data or not isinstance(create_data["id"], str):
                create_data["id"] = f"story_{uuid.uuid4().hex}"
            elif not create_data["id"].startswith("story_"):
                create_data["id"] = f"story_{create_data['id']}"
            
            # Ensure external_id is a string
            if "external_id" in create_data:
                create_data["external_id"] = str(create_data["external_id"])
            
            db_obj = Story(**create_data)
            db.add(db_obj)
            await db.flush()
            await db.refresh(db_obj)
            
            logger.info(f"Created new story: {db_obj.title_english or db_obj.title_romaji}")
            return db_obj
            
        except Exception as e:
            logger.error(f"Error creating story: {str(e)}")
            raise

    async def update(
        self, db: AsyncSession, *, db_obj: Story, obj_in: Union[StoryUpdate, Dict[str, Any]]
    ) -> Story:
        """Update a story."""
        try:
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.model_dump(exclude_unset=True)
            
            # Always update the updated_at timestamp
            update_data["updated_at"] = datetime.utcnow()
            
            # Ensure ID is not changed and is a string
            if "id" in update_data:
                del update_data["id"]  # Don't allow ID updates
            
            # Ensure external_id is a string if present
            if "external_id" in update_data:
                update_data["external_id"] = str(update_data["external_id"])
            
            # Log significant changes
            title = update_data.get("title_english") or update_data.get("title_romaji")
            if title:
                logger.info(f"Updating story: {title}")
            
            for field, value in update_data.items():
                setattr(db_obj, field, value)
            
            await db.flush()
            await db.refresh(db_obj)
            return db_obj
            
        except Exception as e:
            logger.error(f"Error updating story: {str(e)}")
            raise

    async def create_or_update(
        self, db: AsyncSession, *, obj_in: Union[StoryCreate, Dict[str, Any]]
    ) -> Story:
        """Create or update a story."""
        try:
            if isinstance(obj_in, dict):
                create_data = obj_in
            else:
                create_data = obj_in.model_dump(exclude_unset=True)

            external_id = str(create_data.get("external_id"))
            if not external_id:
                raise ValueError("external_id is required")
                
            logger.debug(f"Creating or updating story with external_id: {external_id}")

            # First try to get the existing record
            db_obj = await self.get_by_external_id(db, external_id=external_id)
            
            if db_obj:
                logger.debug(f"Updating existing story: {db_obj.title_english or db_obj.title_romaji}")
                return await self.update(db, db_obj=db_obj, obj_in=create_data)
            
            try:
                # Try to create new record
                logger.debug(f"Attempting to create new story with external_id: {external_id}")
                return await self.create(db, obj_in=create_data)
            except Exception as create_error:
                if "duplicate key value violates unique constraint" in str(create_error):
                    logger.debug(f"Concurrent creation detected for external_id: {external_id}, retrying as update")
                    await db.rollback()
                    # Another transaction may have created the record, try to get it again
                    db_obj = await self.get_by_external_id(db, external_id=external_id)
                    if db_obj:
                        return await self.update(db, db_obj=db_obj, obj_in=create_data)
                raise

        except Exception as e:
            logger.error(f"Error in create_or_update: {str(e)}")
            await db.rollback()
            raise

    def is_stale(self, story: Story) -> bool:
        """
        Check if a story's data is stale and needs updating.
        A story is considered stale if:
        1. It has no updated_at timestamp
        2. Its updated_at timestamp is older than STALE_THRESHOLD_HOURS
        3. It has missing required data (e.g., no title)
        """
        if not story.updated_at:
            logger.debug(f"Story {story.external_id} is stale: no updated_at timestamp")
            return True
            
        stale_threshold = datetime.utcnow() - timedelta(hours=self.STALE_THRESHOLD_HOURS)
        if story.updated_at < stale_threshold:
            logger.debug(f"Story {story.external_id} is stale: last updated at {story.updated_at}")
            return True
            
        # Check for missing required data
        if not (story.title_english or story.title_romaji):
            logger.debug(f"Story {story.external_id} is stale: missing title")
            return True
            
        return False

story = CRUDStory() 