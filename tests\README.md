# Tests

This directory contains all test-related files for the <PERSON>himoto application, organized by test type and purpose.

## Directory Structure

```
tests/
├── README.md              # This file
├── data/                  # Test data files (JSON, etc.)
│   ├── README.md
│   ├── test_anime.json
│   └── test_request.json
└── integration/           # Integration tests
    ├── README.md
    ├── test_admin_api.py
    ├── test_contextual_themes.py
    ├── test_fixed_frontend.py
    ├── test_frontend_api.py
    ├── test_frontend_direct.py
    └── test_theme_mapping.py
```

## Test Types

### Integration Tests (`integration/`)
Tests that verify the interaction between different components:
- API endpoint testing
- Frontend-backend communication
- End-to-end workflows
- Cross-component functionality

### Test Data (`data/`)
Reusable test data files in JSON format:
- Sample anime data
- API request/response examples
- Mock data for various test scenarios

## Backend Tests

The backend has its own comprehensive test suite located in `backend/tests/` with:
- Unit tests
- Component tests
- Infrastructure tests
- Service tests

## Running Tests

### Integration Tests
```bash
# From project root
python -m pytest tests/integration/

# Run specific test
python tests/integration/test_admin_api.py
```

### Backend Tests
```bash
# From backend directory
cd backend
python -m pytest tests/

# Or use the provided scripts
./run_tests.ps1
```

## Prerequisites

- Backend server running on `http://localhost:8000`
- Database properly seeded
- Redis server running (for caching tests)
- All dependencies installed

## Notes

- Integration tests require a running application environment
- Use test databases to avoid affecting production data
- Some tests may require specific test data setup
