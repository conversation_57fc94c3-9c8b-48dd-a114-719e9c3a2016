"""initial schema

Revision ID: v1
Revises: 
Create Date: 2024-12-13 14:51:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'v1'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # Create stories table
    op.create_table(
        'stories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.String(100), nullable=False),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('original_title', sa.String(255), nullable=True),
        sa.Column('synopsis', sa.Text(), nullable=True),
        sa.Column('media_type', sa.String(50), nullable=False),
        sa.Column('source', sa.String(50), nullable=False),
        
        # Images
        sa.Column('cover_image_large', sa.String(500), nullable=True),
        sa.Column('cover_image_medium', sa.String(500), nullable=True),
        sa.Column('banner_image', sa.String(500), nullable=True),
        
        # Stats
        sa.Column('status', sa.String(50), nullable=True),
        sa.Column('popularity', sa.Integer(), nullable=True),
        sa.Column('average_score', sa.Float(), nullable=True),
        
        # Content Details
        sa.Column('episode_count', sa.Integer(), nullable=True),
        sa.Column('episode_duration', sa.Integer(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('season', sa.String(50), nullable=True),
        
        # Extended Metadata
        sa.Column('story_metadata', postgresql.JSONB(), nullable=False, server_default='{}'),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id')
    )
    
    # Create indexes
    op.create_index('ix_stories_external_id', 'stories', ['external_id'])
    op.create_index('ix_stories_title', 'stories', ['title'])

def downgrade() -> None:
    op.drop_index('ix_stories_title')
    op.drop_index('ix_stories_external_id')
    op.drop_table('stories') 