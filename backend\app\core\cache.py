from typing import Optional, Any, Type, Union, Dict, List, TypeVar, Generic, Callable, cast
import json
from datetime import datetime
from pydantic import BaseModel
import logging
from enum import Enum
import pickle
from functools import wraps
import inspect
import traceback

import redis.asyncio as redis
from redis.asyncio import Redis
import redis.exceptions as redis_exceptions

from app.core import redis as redis_connection
from app.core.logging import get_logger
from app.core.redis import RedisConnection

logger = get_logger("core_cache")

T = TypeVar('T')

# Define types for cached functions
CacheableFunction = Callable[..., T]
AsyncCacheableFunction = Callable[..., T]

class ModelJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for models."""
    def default(self, obj):
        if isinstance(obj, BaseModel):
            return obj.model_dump()
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class CacheManager:
    """Manager for caching data using Redis."""
    
    @classmethod
    async def _get_redis(cls):
        """Get Redis connection."""
        return await RedisConnection.get()
    
    @classmethod
    async def _redis_available(cls) -> bool:
        """Check if Redis is available and attempt reconnection if needed."""
        try:
            # First, try the standard ping
            if await RedisConnection.ping():
                return True
            
            # If ping fails, attempt to reconnect
            logger.warning("Redis connection lost, attempting to reconnect...")
            
            # Force a new connection by setting instance to None and getting again
            await RedisConnection.close()  # Close existing connection if any
            redis_instance = await RedisConnection.get()  # This will trigger a new connection attempt
            
            if redis_instance:
                logger.info("Successfully reconnected to Redis")
                return True
            else:
                logger.warning("Redis reconnection failed")
                return False
        except Exception as e:
            logger.error(f"Redis availability check failed: {e}")
            return False
    
    @classmethod
    async def get(cls, key: str) -> Optional[str]:
        """Get value from cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return None
                
            value = await RedisConnection.get_value(key)
            if value:
                logger.debug(f"Cache hit for key: {key}")
                return value
            
            logger.debug(f"Cache miss for key: {key}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting from cache: {e}")
            return None
    
    @classmethod
    async def get_json(cls, key: str) -> Optional[Dict]:
        """Get JSON value from cache."""
        try:
            value = await cls.get(key)
            if value:
                return json.loads(value)
            return None
        except json.JSONDecodeError:
            logger.error(f"Failed to decode JSON from cache for key: {key}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting JSON from cache: {e}")
            return None
    
    @classmethod
    async def get_object(cls, key: str) -> Any:
        """Get pickled object from cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return None
                
            redis = await cls._get_redis()
            if not redis:
                logger.warning("Redis connection not available")
                return None
                
            value = await redis.get(key)
            if value:
                logger.debug(f"Cache hit for object key: {key}")
                return pickle.loads(value)
            
            logger.debug(f"Cache miss for object key: {key}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting object from cache: {e}")
            return None
    
    @classmethod
    async def set(cls, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return False
                
            success = await RedisConnection.set(key, value, ex=ttl)
            if success:
                logger.debug(f"Cached value for key: {key}")
            return success
        except Exception as e:
            logger.error(f"Unexpected error setting cache: {e}")
            return False
    
    @classmethod
    async def set_json(cls, key: str, value: Dict, ttl: Optional[int] = None) -> bool:
        """Set JSON value in cache."""
        try:
            json_value = json.dumps(value)
            return await cls.set(key, json_value, ttl)
        except Exception as e:
            logger.error(f"Unexpected error setting JSON in cache: {e}")
            return False
    
    @classmethod
    async def set_object(cls, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set pickled object in cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return False
                
            redis = await cls._get_redis()
            if not redis:
                logger.warning("Redis connection not available")
                return False
                
            pickled_value = pickle.dumps(value)
            await redis.set(key, pickled_value, ex=ttl)
            logger.debug(f"Cached object for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Unexpected error setting object in cache: {e}")
            return False
    
    @classmethod
    async def delete(cls, key: str) -> bool:
        """Delete key from cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return False
                
            success = await RedisConnection.delete(key)
            if success:
                logger.debug(f"Deleted cache for key: {key}")
            return success
        except Exception as e:
            logger.error(f"Unexpected error deleting from cache: {e}")
            return False
    
    @classmethod
    async def exists(cls, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return False
                
            exists = await RedisConnection.exists(key)
            return exists
        except Exception as e:
            logger.error(f"Unexpected error checking cache existence: {e}")
            return False
    
    @classmethod
    async def delete_pattern(cls, pattern: str) -> int:
        """Delete keys matching pattern from cache."""
        try:
            if not await cls._redis_available():
                logger.warning("Redis connection not available")
                return 0
                
            keys = await RedisConnection.get_keys(pattern)
            if not keys:
                return 0
                
            count = 0
            for key in keys:
                if await cls.delete(key):
                    count += 1
            
            logger.debug(f"Deleted {count} keys matching pattern: {pattern}")
            return count
        except Exception as e:
            logger.error(f"Unexpected error deleting pattern from cache: {e}")
            return 0 