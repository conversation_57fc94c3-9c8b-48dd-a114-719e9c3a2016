#!/usr/bin/env python3
"""
Demo Database ID Verification Script

This script demonstrates how the database ID verification process works.
It shows how the verify_database_ids.py would check database records for ID standardization issues.
"""

import json
from collections import defaultdict
from typing import Dict, List, Any, Set

# Import our mock IdService
from mock_id_service import IdService

class MockDatabase:
    """Mock database for demonstration purposes."""
    
    def __init__(self):
        """Initialize the mock database with some entities."""
        self.nodes = {
            "Theme": [
                {"id": "adventure", "name": "Adventure"},
                {"id": "theme_mystery", "name": "Mystery"},
                {"id": "fantasy", "name": "Fantasy"},
                {"id": "7fc75255-6e7f-400f-a74c-2ec78668aaf3", "name": "Horror"},
                {"id": "theme_7fc75255-6e7f-400f-a74c-2ec78668aaf3", "name": "Science Fiction"}
            ],
            "Story": [
                {"id": "story_123", "name": "The Journey", "theme_id": "adventure"},
                {"id": "456", "name": "The Mystery", "theme_id": "theme_mystery"},
                {"id": "story_one_piece", "name": "One Piece", "theme_id": "fantasy"},
                {"id": "naruto", "name": "Naruto", "theme_id": "theme_7fc75255-6e7f-400f-a74c-2ec78668aaf3"}
            ],
            "Character": [
                {"id": "character_abc", "name": "Hero", "story_id": "story_123"},
                {"id": "def", "name": "Villain", "story_id": "456"},
                {"id": "character_luffy", "name": "Monkey D. Luffy", "story_id": "story_one_piece"},
                {"id": "sasuke", "name": "Sasuke", "story_id": "naruto"}
            ],
            "Location": [
                {"id": "location_xyz", "name": "Castle", "story_id": "story_123"},
                {"id": "pdq", "name": "Forest", "story_id": "456"},
                {"id": "grand_line", "name": "Grand Line", "story_id": "story_one_piece"},
                {"id": "location_hidden_leaf", "name": "Hidden Leaf Village", "story_id": "naruto"}
            ]
        }
        
        self.relationships = [
            {"source_id": "story_123", "target_id": "adventure", "type": "HAS_THEME"},
            {"source_id": "456", "target_id": "theme_mystery", "type": "HAS_THEME"},
            {"source_id": "story_one_piece", "target_id": "fantasy", "type": "HAS_THEME"},
            {"source_id": "naruto", "target_id": "theme_7fc75255-6e7f-400f-a74c-2ec78668aaf3", "type": "HAS_THEME"},
            {"source_id": "character_abc", "target_id": "story_123", "type": "BELONGS_TO"},
            {"source_id": "def", "target_id": "456", "type": "BELONGS_TO"},
            {"source_id": "character_luffy", "target_id": "story_one_piece", "type": "BELONGS_TO"},
            {"source_id": "sasuke", "target_id": "naruto", "type": "BELONGS_TO"},
            {"source_id": "location_xyz", "target_id": "story_123", "type": "IS_IN"},
            {"source_id": "pdq", "target_id": "456", "type": "IS_IN"},
            {"source_id": "grand_line", "target_id": "story_one_piece", "type": "IS_IN"},
            {"source_id": "location_hidden_leaf", "target_id": "naruto", "type": "IS_IN"}
        ]

class DatabaseVerificationDemo:
    """Demo for database ID verification."""
    
    # Entity label to entity type mapping
    LABEL_TO_TYPE = {
        "Theme": "theme",
        "Story": "story",
        "Character": "character",
        "Location": "location",
        "Item": "item",
        "Event": "event",
        "Quest": "quest",
        "Note": "note",
        "Tag": "tag",
        "Mapping": "mapping"
    }
    
    # Relationship type to entity type mapping
    REL_TYPE_TO_ENTITY_TYPES = {
        "HAS_THEME": ("story", "theme"),
        "BELONGS_TO": ("character", "story"),
        "IS_IN": ("location", "story"),
        "HAS_ITEM": ("character", "item"),
        "PARTICIPATES_IN": ("character", "event"),
        "TAKES_PLACE_IN": ("event", "location"),
        "TAGGED_WITH": (None, "tag"),
        "PARENT_OF": (None, None)  # This is a generic relationship, both ends can be any type
    }
    
    def __init__(self, db: MockDatabase, autofix: bool = False):
        """
        Initialize the database verification demo.
        
        Args:
            db: The mock database to verify
            autofix: Whether to automatically fix issues
        """
        self.db = db
        self.autofix = autofix
        self.node_issues = defaultdict(list)
        self.fixed_node_issues = defaultdict(list)
        self.relationship_issues = []
        self.fixed_relationship_issues = []
    
    def verify_database(self) -> Dict[str, Any]:
        """
        Verify all entities and relationships in the database.
        
        Returns:
            A report of issues found
        """
        # Verify nodes
        for label, nodes in self.db.nodes.items():
            entity_type = self.LABEL_TO_TYPE.get(label, "").lower()
            
            if not entity_type:
                continue
                
            for node in nodes:
                # Check node ID
                self._verify_node_id(node, entity_type, label)
                
                # Check related entity IDs
                for field, value in node.items():
                    if field.endswith("_id") and field != "id":
                        # Extract the entity type from the field name
                        related_entity_type = field[:-3]  # Remove "_id" suffix
                        self._verify_node_field_id(node, field, value, related_entity_type)
        
        # Verify relationships
        for rel in self.db.relationships:
            self._verify_relationship(rel)
        
        # Generate report
        return self._generate_report()
    
    def _verify_node_id(self, node: Dict[str, Any], entity_type: str, label: str) -> None:
        """
        Verify that a node's ID is standardized.
        
        Args:
            node: The node to verify
            entity_type: The entity type
            label: The node label
        """
        id_value = node.get("id", "")
        is_valid = IdService.validate_id(id_value, entity_type)
        
        if not is_valid:
            # Create an issue record
            std_id = IdService.standardize_id(id_value, entity_type)
            issue = {
                "entity_type": entity_type,
                "label": label,
                "id": id_value,
                "standard_id": std_id,
                "name": node.get("name", ""),
                "properties": node
            }
            self.node_issues[entity_type].append(issue)
            
            # Fix the issue if autofix is enabled
            if self.autofix:
                node["id"] = std_id
                issue["fixed"] = True
                self.fixed_node_issues[entity_type].append(issue)
    
    def _verify_node_field_id(self, node: Dict[str, Any], field: str, id_value: str, related_entity_type: str) -> None:
        """
        Verify that a node's field ID (reference to another entity) is standardized.
        
        Args:
            node: The node to verify
            field: The field name
            id_value: The ID value to verify
            related_entity_type: The entity type of the related entity
        """
        is_valid = IdService.validate_id(id_value, related_entity_type)
        
        if not is_valid:
            # Create an issue record
            std_id = IdService.standardize_id(id_value, related_entity_type)
            issue = {
                "entity_type": self.LABEL_TO_TYPE.get(node.get("label", ""), "").lower(),
                "id": node.get("id", ""),
                "name": node.get("name", ""),
                "field": field,
                "field_value": id_value,
                "standard_field_value": std_id,
                "related_entity_type": related_entity_type,
                "properties": node
            }
            self.node_issues[related_entity_type].append(issue)
            
            # Fix the issue if autofix is enabled
            if self.autofix:
                node[field] = std_id
                issue["fixed"] = True
                self.fixed_node_issues[related_entity_type].append(issue)
    
    def _verify_relationship(self, rel: Dict[str, Any]) -> None:
        """
        Verify a relationship's source and target IDs.
        
        Args:
            rel: The relationship to verify
        """
        rel_type = rel.get("type", "")
        source_id = rel.get("source_id", "")
        target_id = rel.get("target_id", "")
        
        # Determine entity types based on relationship type
        source_type, target_type = self.REL_TYPE_TO_ENTITY_TYPES.get(rel_type, (None, None))
        
        if source_type and target_type:
            # Verify source ID
            source_is_valid = IdService.validate_id(source_id, source_type)
            
            # Verify target ID
            target_is_valid = IdService.validate_id(target_id, target_type)
            
            if not source_is_valid or not target_is_valid:
                # Create an issue record
                std_source_id = IdService.standardize_id(source_id, source_type) if not source_is_valid else source_id
                std_target_id = IdService.standardize_id(target_id, target_type) if not target_is_valid else target_id
                
                issue = {
                    "rel_type": rel_type,
                    "source_type": source_type,
                    "target_type": target_type,
                    "source_id": source_id,
                    "target_id": target_id,
                    "standard_source_id": std_source_id,
                    "standard_target_id": std_target_id,
                    "properties": rel
                }
                self.relationship_issues.append(issue)
                
                # Fix the issue if autofix is enabled
                if self.autofix:
                    rel["source_id"] = std_source_id
                    rel["target_id"] = std_target_id
                    issue["fixed"] = True
                    self.fixed_relationship_issues.append(issue)
    
    def _generate_report(self) -> Dict[str, Any]:
        """
        Generate a report of issues found.
        
        Returns:
            A dictionary with the report data
        """
        # Count total entities for each type
        total_entities = {}
        for label, nodes in self.db.nodes.items():
            entity_type = self.LABEL_TO_TYPE.get(label, "").lower()
            if entity_type:
                total_entities[entity_type] = len(nodes)
        
        # Count total relationships
        total_relationships = len(self.db.relationships)
        
        # Count issues for each type
        node_issues_count = sum(len(issues) for issues in self.node_issues.values())
        fixed_node_issues_count = sum(len(issues) for issues in self.fixed_node_issues.values())
        
        report = {
            "summary": {
                "total_entities": sum(total_entities.values()),
                "total_relationships": total_relationships,
                "total_node_issues": node_issues_count,
                "total_relationship_issues": len(self.relationship_issues),
                "total_issues": node_issues_count + len(self.relationship_issues),
                "total_fixed_node_issues": fixed_node_issues_count,
                "total_fixed_relationship_issues": len(self.fixed_relationship_issues),
                "total_fixed_issues": fixed_node_issues_count + len(self.fixed_relationship_issues),
            },
            "by_entity_type": {
                entity_type: {
                    "total": total_entities.get(entity_type, 0),
                    "issues": len(self.node_issues.get(entity_type, [])),
                    "fixed": len(self.fixed_node_issues.get(entity_type, [])),
                }
                for entity_type in set(list(total_entities.keys()) + list(self.node_issues.keys()))
            },
            "node_issues": {
                entity_type: [
                    {
                        "entity_type": issue["entity_type"],
                        "id": issue["id"],
                        "standard_id": issue.get("standard_id", ""),
                        "name": issue.get("name", ""),
                        "field": issue.get("field", ""),
                        "field_value": issue.get("field_value", ""),
                        "standard_field_value": issue.get("standard_field_value", ""),
                        "fixed": issue.get("fixed", False)
                    }
                    for issue in issues
                ]
                for entity_type, issues in self.node_issues.items()
            },
            "relationship_issues": [
                {
                    "rel_type": issue["rel_type"],
                    "source_type": issue["source_type"],
                    "target_type": issue["target_type"],
                    "source_id": issue["source_id"],
                    "target_id": issue["target_id"],
                    "standard_source_id": issue["standard_source_id"],
                    "standard_target_id": issue["standard_target_id"],
                    "fixed": issue.get("fixed", False)
                }
                for issue in self.relationship_issues
            ]
        }
        
        return report

def run_demo():
    """Run the database verification demo."""
    print("\n=== Database ID Verification Demo ===\n")
    
    # Create a mock database
    db = MockDatabase()
    
    # Create the verifier
    verifier = DatabaseVerificationDemo(db)
    
    # Verify the database
    print("Verifying database entities and relationships...")
    report = verifier.verify_database()
    
    # Print the report summary
    print("\nVerification Report Summary:")
    print(f"  Total entities: {report['summary']['total_entities']}")
    print(f"  Total relationships: {report['summary']['total_relationships']}")
    print(f"  Entity ID issues: {report['summary']['total_node_issues']}")
    print(f"  Relationship ID issues: {report['summary']['total_relationship_issues']}")
    print(f"  Total issues: {report['summary']['total_issues']}")
    
    # Print issues by entity type
    print("\nIssues by Entity Type:")
    for entity_type, stats in report['by_entity_type'].items():
        if stats['issues'] > 0:
            print(f"  {entity_type.capitalize()}: {stats['issues']} issues out of {stats['total']} entities")
    
    # Print node issues
    print("\nEntity ID Issues:")
    for entity_type, issues in report['node_issues'].items():
        if issues:
            print(f"\n  {entity_type.capitalize()} Issues:")
            for issue in issues[:3]:  # Show at most 3 examples per entity type
                if "standard_id" in issue:
                    # This is a primary ID issue
                    print(f"    Entity with name '{issue['name']}' has non-standard ID:")
                    print(f"      Current: {issue['id']}")
                    print(f"      Should be: {issue['standard_id']}")
                else:
                    # This is a field ID issue
                    print(f"    Entity '{issue['id']}' has non-standard ID in field '{issue['field']}':")
                    print(f"      Current: {issue['field_value']}")
                    print(f"      Should be: {issue['standard_field_value']}")
                print()
    
    # Print relationship issues
    if report['relationship_issues']:
        print("\nRelationship ID Issues:")
        for issue in report['relationship_issues'][:3]:  # Show at most 3 examples
            print(f"  Relationship of type '{issue['rel_type']}' has non-standard IDs:")
            if issue['source_id'] != issue['standard_source_id']:
                print(f"    Source ID (type: {issue['source_type']}):")
                print(f"      Current: {issue['source_id']}")
                print(f"      Should be: {issue['standard_source_id']}")
            if issue['target_id'] != issue['standard_target_id']:
                print(f"    Target ID (type: {issue['target_type']}):")
                print(f"      Current: {issue['target_id']}")
                print(f"      Should be: {issue['standard_target_id']}")
            print()
    
    # Now let's fix the issues
    print("\nAutofixing issues...")
    fix_verifier = DatabaseVerificationDemo(db, autofix=True)
    fixed_report = fix_verifier.verify_database()
    
    print(f"  Total entities fixed: {fixed_report['summary']['total_fixed_node_issues']} out of {fixed_report['summary']['total_node_issues']}")
    print(f"  Total relationships fixed: {fixed_report['summary']['total_fixed_relationship_issues']} out of {fixed_report['summary']['total_relationship_issues']}")
    print(f"  Total issues fixed: {fixed_report['summary']['total_fixed_issues']} out of {fixed_report['summary']['total_issues']}")
    
    # Print fixed entities
    if fixed_report['summary']['total_fixed_node_issues'] > 0:
        print("\nFixed Entity IDs:")
        for entity_type, issues in fixed_report['node_issues'].items():
            fixed_issues = [i for i in issues if i.get('fixed', False)]
            if fixed_issues:
                print(f"\n  {entity_type.capitalize()} Fixes:")
                for issue in fixed_issues[:2]:  # Show at most 2 examples per entity type
                    if "standard_id" in issue:
                        # This is a primary ID issue
                        print(f"    Entity with name '{issue['name']}' had ID fixed:")
                        print(f"      From: {issue['id']}")
                        print(f"      To: {issue['standard_id']}")
                    else:
                        # This is a field ID issue
                        print(f"    Entity '{issue['id']}' had field '{issue['field']}' fixed:")
                        print(f"      From: {issue['field_value']}")
                        print(f"      To: {issue['standard_field_value']}")
                    print()
    
    # Print fixed relationships
    if fixed_report['summary']['total_fixed_relationship_issues'] > 0:
        print("\nFixed Relationship IDs:")
        fixed_rels = [i for i in fixed_report['relationship_issues'] if i.get('fixed', False)]
        for issue in fixed_rels[:2]:  # Show at most 2 examples
            print(f"  Relationship of type '{issue['rel_type']}' had IDs fixed:")
            if issue['source_id'] != issue['standard_source_id']:
                print(f"    Source ID (type: {issue['source_type']}):")
                print(f"      From: {issue['source_id']}")
                print(f"      To: {issue['standard_source_id']}")
            if issue['target_id'] != issue['standard_target_id']:
                print(f"    Target ID (type: {issue['target_type']}):")
                print(f"      From: {issue['target_id']}")
                print(f"      To: {issue['standard_target_id']}")
            print()
    
    print("\n=== End of Demo ===\n")

if __name__ == "__main__":
    run_demo() 