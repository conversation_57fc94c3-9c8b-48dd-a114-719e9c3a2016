"""update title fields

Revision ID: v2
Revises: v1
Create Date: 2024-12-13 14:53:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'v2'
down_revision: Union[str, None] = 'v1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # Add new title columns
    op.add_column('stories', sa.Column('title_english', sa.String(255), nullable=True))
    op.add_column('stories', sa.Column('title_romaji', sa.String(255), nullable=True))
    op.add_column('stories', sa.Column('title_native', sa.String(255), nullable=True))
    
    # Copy existing title data to title_english
    op.execute("UPDATE stories SET title_english = title")
    
    # Drop the old title column
    op.drop_column('stories', 'title')

def downgrade() -> None:
    # Add back the original title column
    op.add_column('stories', sa.Column('title', sa.String(255), nullable=False))
    
    # Copy data back from title_english
    op.execute("UPDATE stories SET title = COALESCE(title_english, title_romaji, title_native)")
    
    # Drop the new columns
    op.drop_column('stories', 'title_english')
    op.drop_column('stories', 'title_romaji')
    op.drop_column('stories', 'title_native') 