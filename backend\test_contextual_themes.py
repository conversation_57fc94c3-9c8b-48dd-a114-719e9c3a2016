"""
Test script for Contextual Theme Mapping

Tests the new ContextualThemeMapper with The Apothecary Diaries data
to verify Female Empowerment theme detection with context awareness.
"""

import asyncio
import json
import sys
import os
import aiohttp

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import only what we need without full app config
from app.services.contextual_theme_mapper import ContextualThemeMapper


async def test_contextual_theme_mapping():
    """Test contextual theme mapping with The Apothecary Diaries."""

    print("🧪 Testing Contextual Theme Mapping")
    print("=" * 50)

    # Initialize theme mapper
    theme_mapper = ContextualThemeMapper()

    # Create mock story metadata based on The Apothecary Diaries
    # This simulates the enhanced metadata we would get from AniList
    story_metadata = {
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Historical", "category": "Setting", "rank": 85},
            {"name": "Court Politics", "category": "Theme", "rank": 80},
            {"name": "Imperial Palace", "category": "Setting", "rank": 75},
            {"name": "Mystery Solving", "category": "Theme", "rank": 70},
            {"name": "Female Protagonist", "category": "Cast", "rank": 90},
            {"name": "Intelligent Female Lead", "category": "Cast", "rank": 85}
        ],
        "characters": [
            {
                "id": "character_123456",
                "name": "Maomao",
                "first_name": "Maomao",
                "last_name": None,
                "gender": "Female",
                "age": 17,
                "role": "MAIN",
                "description": "A young apothecary with extensive knowledge of medicine and poisons",
                "image_medium": "https://example.com/maomao.jpg",
                "image_large": "https://example.com/maomao_large.jpg"
            },
            {
                "id": "character_123457",
                "name": "Jinshi",
                "first_name": "Jinshi",
                "last_name": None,
                "gender": "Male",
                "age": 18,
                "role": "MAIN",
                "description": "A beautiful eunuch who works in the imperial palace",
                "image_medium": "https://example.com/jinshi.jpg",
                "image_large": "https://example.com/jinshi_large.jpg"
            },
            {
                "id": "character_123458",
                "name": "Gaoshun",
                "first_name": "Gaoshun",
                "last_name": None,
                "gender": "Male",
                "age": 37,
                "role": "SUPPORTING",
                "description": "Jinshi's loyal retainer",
                "image_medium": "https://example.com/gaoshun.jpg",
                "image_large": "https://example.com/gaoshun_large.jpg"
            }
        ],
        "staff": [
            {
                "id": "staff_001",
                "name": "Norihiro Naganuma",
                "first_name": "Norihiro",
                "last_name": "Naganuma",
                "gender": "Male",
                "age": None,
                "role": "Director",
                "description": "Director of The Apothecary Diaries",
                "image_medium": None,
                "image_large": None
            },
            {
                "id": "staff_002",
                "name": "Touko Shino",
                "first_name": "Touko",
                "last_name": "Shino",
                "gender": "Female",
                "age": None,
                "role": "Original Character Design",
                "description": "Character designer for the series",
                "image_medium": None,
                "image_large": None
            },
            {
                "id": "staff_003",
                "name": "Natsu Hyuuga",
                "first_name": "Natsu",
                "last_name": "Hyuuga",
                "gender": None,
                "age": None,
                "role": "Original Story",
                "description": "Original light novel author",
                "image_medium": None,
                "image_large": None
            }
        ]
    }

    try:
        
        print(f"📺 Anime: The Apothecary Diaries (Mock Data)")
        print(f"📊 Genres: {', '.join(story_metadata.get('genres', []))}")
        
        # Display character analysis
        characters = story_metadata.get("characters", [])
        print(f"\n👥 Characters ({len(characters)}):")
        
        main_chars = [c for c in characters if c.get('role') == 'MAIN']
        female_main = [c for c in main_chars if c.get('gender') == 'Female']
        
        print(f"   Main Characters: {len(main_chars)}")
        print(f"   Female Main Characters: {len(female_main)}")
        
        if female_main:
            print(f"   Female MC Names: {[c.get('name') for c in female_main]}")
        
        # Display staff analysis
        staff = story_metadata.get("staff", [])
        print(f"\n🎬 Staff ({len(staff)}):")
        
        directors = [s for s in staff if 'Director' in s.get('role', '')]
        female_staff = [s for s in staff if s.get('gender') == 'Female']
        
        print(f"   Directors: {len(directors)}")
        print(f"   Female Staff: {len(female_staff)}")
        
        if female_staff:
            female_staff_info = [f"{s.get('name')} ({s.get('role')})" for s in female_staff[:3]]
            print(f"   Female Staff: {female_staff_info}")
        
        # Display tags for context
        tags = story_metadata.get("tags", [])
        relevant_tags = [t for t in tags if any(keyword in t.get('name', '').lower() 
                                              for keyword in ['historical', 'court', 'politics', 'mystery', 'palace'])]
        
        if relevant_tags:
            print(f"\n🏷️ Relevant Tags:")
            for tag in relevant_tags[:5]:
                print(f"   - {tag.get('name')} (Category: {tag.get('category', 'Unknown')})")
        
        # Perform contextual theme analysis
        print(f"\n🎯 Contextual Theme Analysis:")
        print("-" * 30)
        
        theme_matches = theme_mapper.analyze_story_themes(story_metadata)
        
        if not theme_matches:
            print("❌ No theme matches found")
            return
        
        print(f"✅ Found {len(theme_matches)} theme matches:")
        print()
        
        for i, match in enumerate(theme_matches, 1):
            confidence_percent = match.confidence * 100
            print(f"{i}. {match.theme_name}")
            print(f"   Confidence: {confidence_percent:.1f}%")
            
            if match.base_indicators:
                print(f"   Base Indicators: {', '.join(match.base_indicators)}")
            
            if match.context_boosts:
                print(f"   Context Boosts:")
                for context, boost in match.context_boosts.items():
                    print(f"     • {context}: +{boost:.2f}")
            
            if match.reasoning:
                print(f"   Reasoning:")
                for reason in match.reasoning:
                    print(f"     • {reason}")
            
            print()
        
        # Test specific case: Female MC + Historical setting
        print("🔍 Specific Test Case Analysis:")
        print("-" * 30)
        
        has_female_mc = len(female_main) > 0
        has_historical = 'Drama' in story_metadata.get('genres', []) and any(
            'historical' in tag.get('name', '').lower() or 'court' in tag.get('name', '').lower()
            for tag in tags
        )
        
        print(f"Female MC: {'✅' if has_female_mc else '❌'}")
        print(f"Historical/Court Setting: {'✅' if has_historical else '❌'}")
        
        if has_female_mc and has_historical:
            print("🎯 Expected: High confidence for 'Female Agency in Restrictive Systems'")
            
            agency_theme = next((m for m in theme_matches if 'Agency' in m.theme_name), None)
            if agency_theme:
                print(f"✅ Found with {agency_theme.confidence * 100:.1f}% confidence")
            else:
                print("❌ Theme not detected - needs investigation")
        
        # Save detailed results
        results = {
            "anime_title": "The Apothecary Diaries (Mock Data)",
            "genres": story_metadata.get('genres', []),
            "character_count": len(characters),
            "female_main_characters": len(female_main),
            "staff_count": len(staff),
            "female_staff_count": len(female_staff),
            "theme_matches": [
                {
                    "theme_name": match.theme_name,
                    "confidence": match.confidence,
                    "confidence_percent": f"{match.confidence * 100:.1f}%",
                    "base_indicators": match.base_indicators,
                    "context_boosts": match.context_boosts,
                    "reasoning": match.reasoning
                }
                for match in theme_matches
            ]
        }
        
        with open("contextual_theme_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Detailed results saved to contextual_theme_test_results.json")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_contextual_theme_mapping())
