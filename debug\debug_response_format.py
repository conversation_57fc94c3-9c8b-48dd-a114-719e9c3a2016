#!/usr/bin/env python3
"""
Debug the exact response format from contextual themes API.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def debug_contextual_response():
    """Debug the exact response format."""
    print("🎯 Debugging Contextual Theme Response Format")
    print("=" * 60)
    
    anime_id = "21202"
    request_data = {
        "anime_id": anime_id,
        "include_reasoning": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Response received")
            print(f"📋 Raw response structure:")
            print(json.dumps(data, indent=2))
            
            print(f"\n🔍 Analyzing theme_matches:")
            for i, match in enumerate(data.get('theme_matches', []), 1):
                print(f"\n   Theme {i}:")
                for key, value in match.items():
                    print(f"      {key}: {value} (type: {type(value).__name__})")
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    debug_contextual_response()
