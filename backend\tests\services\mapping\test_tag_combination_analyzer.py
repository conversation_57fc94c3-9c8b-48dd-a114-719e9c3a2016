"""
Tests for the TagCombinationAnalyzer class.
"""
import pytest
from app.services.mapping.tag_combination_analyzer import Tag<PERSON><PERSON><PERSON>A<PERSON>yzer


@pytest.fixture
def analyzer():
    """Create a TagCombinationAnalyzer instance for testing."""
    return TagCombinationAnalyzer()


def test_normalize_tags(analyzer):
    """Test tag normalization."""
    # Test with string tags
    string_tags = ["Action", "Comedy"]
    normalized = analyzer._normalize_tags(string_tags)
    
    assert len(normalized) == 2
    assert normalized[0]["name"] == "Action"
    assert normalized[0]["rank"] == 50
    
    # Test with dictionary tags
    dict_tags = [
        {"name": "School", "category": "Setting", "rank": 80},
        {"name": "Romance", "category": "Genre", "rank": 70}
    ]
    normalized = analyzer._normalize_tags(dict_tags)
    
    assert len(normalized) == 2
    assert normalized[0]["name"] == "School"
    assert normalized[0]["category"] == "Setting"
    assert normalized[0]["rank"] == 80


def test_match_patterns(analyzer):
    """Test pattern matching."""
    # Test with matching pattern
    elements = ["Thriller", "Psychological", "Mystery"]
    matches = analyzer._match_patterns(elements)
    
    assert len(matches) > 0
    assert any(match["theme_id"] == "psychological_thriller" for match in matches)
    
    # Test with non-matching pattern
    elements = ["Action", "Comedy"]
    matches = analyzer._match_patterns(elements)
    
    assert len(matches) == 0


def test_analyze_combinations(analyzer):
    """Test the full analyze_combinations method."""
    genres = ["Romance", "Comedy"]
    tags = [
        {"name": "School", "category": "Setting", "rank": 80},
        {"name": "Slice of Life", "category": "Genre", "rank": 70}
    ]
    
    results = analyzer.analyze_combinations(genres, tags)
    
    # Should match the "School Romantic Comedy" pattern
    assert len(results) > 0
    assert any(result["theme_id"] == "romantic_comedy_school_life" for result in results)
    
    # Check confidence scores
    confidence_scores = analyzer.get_confidence_scores(results)
    assert "romantic_comedy_school_life" in confidence_scores
    assert confidence_scores["romantic_comedy_school_life"] > 0.7
    
    # Check mapping types
    mapping_types = analyzer.get_mapping_types(results)
    assert "romantic_comedy_school_life" in mapping_types
    assert mapping_types["romantic_comedy_school_life"] == "genre_combination"


def test_individual_tag_analysis(analyzer):
    """Test individual tag analysis."""
    tags = [
        {"name": "Psychological", "category": "Theme", "rank": 90},
    ]
    
    tag_weights = analyzer._calculate_tag_weights(tags)
    assert "Psychological" in tag_weights
    assert tag_weights["Psychological"] > 1.0
    
    results = analyzer._analyze_individual_tags(tags, tag_weights)
    assert len(results) == 1
    assert results[0]["theme_id"] == "psychological"
    assert results[0]["confidence"] > 0.5


def test_combine_results(analyzer):
    """Test result combination."""
    pattern_themes = [
        {
            "theme_id": "psychological_thriller",
            "name": "Psychological Thriller",
            "confidence": 0.85,
            "mapping_type": "mood"
        }
    ]
    
    individual_themes = [
        {
            "theme_id": "psychological",
            "name": "Psychological",
            "confidence": 0.7,
            "mapping_type": "theme"
        },
        {
            "theme_id": "thriller",
            "name": "Thriller",
            "confidence": 0.65,
            "mapping_type": "genre"
        }
    ]
    
    combined = analyzer._combine_results(pattern_themes, individual_themes)
    
    # Should include all three themes (no duplicates)
    assert len(combined) == 3
    
    # Pattern theme should be included
    assert any(theme["theme_id"] == "psychological_thriller" for theme in combined)
    
    # Individual themes should be included
    assert any(theme["theme_id"] == "psychological" for theme in combined)
    assert any(theme["theme_id"] == "thriller" for theme in combined) 