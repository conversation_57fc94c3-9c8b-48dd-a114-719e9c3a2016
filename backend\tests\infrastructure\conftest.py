# backend/tests/infrastructure/conftest.py

"""
Infrastructure test fixtures and configuration.
These fixtures are designed for infrastructure tests that test core system connections.
"""
import pytest
import asyncio
import os
from typing import Dict, Any, AsyncGenerator
from unittest.mock import AsyncMock, MagicMock, patch

from app.db.neo4j_session import driver, init_db
from app.core.config import settings
from app.services.anilist import anilist_service
from app.db.redis_client import get_redis, init_redis

# Mark all tests in this directory as infrastructure tests
pytest.mark.infrastructure = pytest.mark.infrastructure

@pytest.fixture(scope="session", autouse=True)
async def setup_neo4j():
    """Initialize Neo4j database and verify connectivity."""
    # Initialize the database
    await init_db()
    yield
    # No cleanup needed - the connection will be closed with the application

@pytest.fixture
async def neo4j_session():
    """Create a Neo4j session for testing."""
    async with driver.session() as session:
        yield session

@pytest.fixture
async def clean_neo4j_database(neo4j_session):
    """Clean the Neo4j database before and after tests."""
    # Clean before test
    await neo4j_session.run("MATCH (n) WHERE NOT n:_Schema DETACH DELETE n")
    yield
    # Clean after test
    await neo4j_session.run("MATCH (n) WHERE NOT n:_Schema DETACH DELETE n")

@pytest.fixture
def anilist():
    """Provide the AniList service for testing."""
    return anilist_service

@pytest.fixture
async def graphql_context(neo4j_session):
    """Create a GraphQL context for resolver testing."""
    return {
        "session": neo4j_session,
        "anilist_service": anilist_service,
    }

@pytest.fixture(scope="session", autouse=True)
async def setup_redis():
    """Initialize Redis connection and verify connectivity."""
    await init_redis()
    yield
    # No cleanup needed - the connection will be closed with the application

@pytest.fixture
async def redis_client():
    """Create a Redis client for testing."""
    client = await get_redis()
    yield client
    # No explicit cleanup needed as we don't drop the Redis database

@pytest.fixture
async def clean_redis_database(redis_client):
    """Clean the Redis database before and after tests."""
    # Clean before test - only for test keys
    await redis_client.delete("test:*")
    yield
    # Clean after test - only for test keys
    await redis_client.delete("test:*")

@pytest.fixture
def test_anilist_id():
    """Return a consistent AniList ID for testing."""
    return "21"  # One Piece

@pytest.fixture
async def load_one_piece_data(neo4j_session):
    """Load One Piece data into Neo4j for testing."""
    # Check if One Piece data already exists
    result = await neo4j_session.run(
        "MATCH (s:Story {id: $id}) RETURN s",
        id="story_21"
    )
    
    story = None
    async for record in result:
        story = record["s"]
    
    if story:
        return story
    
    # Create One Piece data
    result = await neo4j_session.run("""
        CREATE (s:Story {
            id: "story_21",
            title: "One Piece",
            description: "Test description for One Piece",
            image_url: "http://example.com/onepiece.jpg",
            themes: ["Action", "Adventure", "Pirates", "Shounen"],
            source: "anilist",
            source_id: 21,
            last_updated: datetime("2023-01-01T00:00:00")
        })
        RETURN s
    """)
    
    async for record in result:
        story = record["s"]
    
    return story