"""
Test cases for the story retrieval component of the stories API.
This file focuses on testing story retrieval functionality in isolation.
"""
import pytest
import json
from unittest.mock import MagicMock, AsyncMock, patch
from typing import Dict, Any
from datetime import datetime
from fastapi import HTTPException

# Import the story retrieval function - adjust import path as needed
from app.api.v1.endpoints.stories import get_story
from app.services.dependencies import get_anilist_service
from app.schemas.story import Story

@pytest.mark.asyncio
class TestStoryRetrievalComponent:
    """Test suite for the story retrieval component."""
    
    @pytest.fixture
    def mock_story_cache(self):
        """Return a mock cache with controlled hit/miss behavior."""
        mock = AsyncMock()
        mock.get.return_value = None  # Default to cache miss
        mock.set.return_value = None
        return mock
    
    @pytest.fixture
    def mock_story_crud(self):
        """Return a mock story CRUD with controlled results."""
        mock = AsyncMock()
        # Default story data
        story_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "title_romaji": "Test Story",
            "title_native": "テストストーリ",
            "synopsis": "Test synopsis",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large.jpg",
            "cover_image_medium": "https://example.com/medium.jpg",
            "banner_image": "https://example.com/banner.jpg",
            "status": "FINISHED",
            "average_score": 85,
            "popularity": 1000,
            "source": "ORIGINAL",
            "last_updated": "2023-01-01T00:00:00",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "story_metadata": {
                "genres": ["Action", "Adventure"],
                "tags": ["Shounen", "Fighting"],
                "studios": ["Test Studio"],
                "relations": []
            }
        }
        
        # AniList story data (for stale/not found scenarios)
        anilist_story_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story from AniList",
            "title_romaji": "Test Story from AniList",
            "title_native": "テストストーリー",
            "synopsis": "Test synopsis from AniList",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large_anilist.jpg",
            "cover_image_medium": "https://example.com/medium_anilist.jpg",
            "banner_image": "https://example.com/banner_anilist.jpg",
            "status": "FINISHED",
            "average_score": 88,
            "popularity": 2000,
            "source": "MANGA",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "story_metadata": {
                "genres": ["Action", "Adventure", "Fantasy"],
                "tags": [],
                "studios": [],
                "relations": []
            }
        }
        
        mock.get.return_value = story_data
        mock.is_stale.return_value = False  # Default to not stale
        mock.get_by_external_id.return_value = story_data
        mock.create_or_update.return_value = anilist_story_data  # Return AniList data when creating/updating
        return mock
    
    @pytest.fixture
    def mock_anilist_service(self):
        """Return a mock AniList service."""
        mock = AsyncMock()
        # Mock data for get_anime_details
        mock.get_anime_details.return_value = {
            "id": 123,
            "title": {
                "english": "Test Story from AniList",
                "romaji": "Test Story from AniList",
                "native": "テストストーリー"
            },
            "type": "ANIME",
            "format": "TV",
            "description": "Test synopsis from AniList",
            "coverImage": {
                "large": "https://example.com/large_anilist.jpg",
                "medium": "https://example.com/medium_anilist.jpg"
            },
            "bannerImage": "https://example.com/banner_anilist.jpg",
            "status": "FINISHED",
            "averageScore": 88,
            "popularity": 2000,
            "source": "MANGA",
            "genres": ["Action", "Adventure", "Fantasy"]
        }
        
        # Mock data for transform_to_story
        anilist_transformed_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story from AniList",
            "title_romaji": "Test Story from AniList",
            "title_native": "テストストーリー",
            "synopsis": "Test synopsis from AniList",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large_anilist.jpg",
            "cover_image_medium": "https://example.com/medium_anilist.jpg",
            "banner_image": "https://example.com/banner_anilist.jpg",
            "status": "FINISHED",
            "average_score": 88,
            "popularity": 2000,
            "source": "MANGA",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "story_metadata": {
                "genres": ["Action", "Adventure", "Fantasy"],
                "tags": [],
                "studios": [],
                "relations": []
            }
        }
        
        # Use side_effect to ensure the mock returns the correct data
        async def mock_transform(*args, **kwargs):
            return anilist_transformed_data
            
        mock.transform_to_story.side_effect = mock_transform
        return mock
    
    @pytest.fixture
    def mock_db_session(self):
        """Return a mock database session."""
        return AsyncMock()
    
    async def test_story_retrieval_from_db(self, monkeypatch, mock_story_cache, mock_story_crud, mock_anilist_service, mock_db_session):
        """Test retrieving a story that exists in the database."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_cache.reset_mock()

        # Create a specific story data for this test
        db_story_data = {
            "id": "story_123",
            "external_id": "123",
            "title_english": "Test Story",
            "title_romaji": "Test Story",
            "title_native": "テストストーリ",
            "synopsis": "Test synopsis",
            "media_type": "ANIME",
            "cover_image_large": "https://example.com/large.jpg",
            "cover_image_medium": "https://example.com/medium.jpg",
            "banner_image": "https://example.com/banner.jpg",
            "status": "FINISHED",
            "average_score": 85,
            "popularity": 1000,
            "source": "ORIGINAL",
            "last_updated": "2023-01-01T00:00:00",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00",
            "story_metadata": {
                "genres": ["Action", "Adventure"],
                "tags": ["Shounen", "Fighting"],
                "studios": ["Test Studio"],
                "relations": []
            }
        }

        # Create a complete mock for story_crud
        class MockStoryCrud:
            async def get(self, db, id=None):
                return db_story_data
                
            def is_stale(self, story_data):
                return False
                
            async def create_or_update(self, db, obj_in=None):
                return db_story_data
        
        # Create a mock for the cache
        class MockStoryCache:
            async def get(self, key):
                return None
                
            async def set(self, key, value):
                return True

        # Patch dependencies with our custom mocks
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_cache", MockStoryCache())
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", MockStoryCrud())
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: mock_anilist_service)

        # Call the function
        result = await get_story(
            story_id="story_123",
            db=mock_db_session,
            anilist_service=mock_anilist_service
        )

        # Verify the result
        assert result.title_english == "Test Story"
        
        # Verify the AniList service was NOT called
        mock_anilist_service.get_anime_details.assert_not_called()
        mock_anilist_service.transform_to_story.assert_not_called()
    
    async def test_story_retrieval_from_cache(self, monkeypatch, mock_story_cache, mock_story_crud, mock_anilist_service, mock_db_session):
        """Test retrieving a story from cache."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_crud.reset_mock()
        mock_story_cache.reset_mock()
        
        # Set up cache hit
        cached_story = Story(
            id="story_123",
            external_id="123",
            title_english="Cached Story",
            title_romaji="Cached Story",
            title_native="キャッシュされたストーリー",
            synopsis="Cached synopsis",
            media_type="ANIME",
            cover_image_large="https://example.com/cached_large.jpg",
            cover_image_medium="https://example.com/cached.jpg",
            banner_image="https://example.com/cached_banner.jpg",
            status="FINISHED",
            average_score=90,
            popularity=1500,
            source="ORIGINAL",
            created_at="2023-01-01T00:00:00",
            updated_at="2023-01-01T00:00:00",
            story_metadata={
                "genres": ["Action"],
                "tags": [],
                "studios": [],
                "relations": []
            }
        )
        # Convert to JSON string before returning from mock (to match real cache behavior)
        mock_story_cache.get.return_value = cached_story.model_dump_json()
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: mock_anilist_service)
        
        # Call the function
        result = await get_story(
            story_id="story_123",
            db=mock_db_session,
            anilist_service=mock_anilist_service
        )
        
        # Verify cache check
        mock_story_cache.get.assert_called_once()
        
        # Verify database not accessed
        mock_story_crud.get.assert_not_called()
        
        # Verify result is from cache
        assert result.id == "story_123"
        assert result.title_english == "Cached Story"
        
        # Cache should not be set again
        mock_story_cache.set.assert_not_called()
        
        # AniList service should not be called
        mock_anilist_service.get_anime_details.assert_not_called()
    
    async def test_story_retrieval_stale_data(self, monkeypatch, mock_story_cache, mock_story_crud, mock_anilist_service, mock_db_session):
        """Test retrieving a story with stale data that needs refreshing."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_crud.reset_mock()
        mock_story_cache.reset_mock()
        
        # Set up stale data scenario
        mock_story_crud.is_stale.return_value = True
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: mock_anilist_service)
        
        # Call the function
        result = await get_story(
            story_id="story_123",
            db=mock_db_session,
            anilist_service=mock_anilist_service
        )
        
        # Verify cache check
        mock_story_cache.get.assert_called_once()
        
        # Verify database retrieval
        mock_story_crud.get.assert_called_once_with(mock_db_session, id="story_123")
        
        # Verify stale check
        mock_story_crud.is_stale.assert_called_once()
        
        # Verify AniList API call for refresh
        mock_anilist_service.get_anime_details.assert_called_once_with("123")
        
        # Verify story creation/update from AniList data
        mock_story_crud.create_or_update.assert_called_once()
        
        # Verify cache update
        mock_story_cache.set.assert_called_once()
        
        # Verify result is from AniList
        assert result.id == "story_123"
        assert result.title_english == "Test Story from AniList"
    
    async def test_story_retrieval_not_found(self, monkeypatch, mock_story_cache, mock_story_crud, mock_anilist_service, mock_db_session):
        """Test story retrieval when not found in database, fetch from AniList."""
        # Reset mocks to ensure clean state
        mock_anilist_service.reset_mock()
        mock_story_crud.reset_mock()
        mock_story_cache.reset_mock()
        
        # Set up not found scenario
        mock_story_crud.get.return_value = None
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: mock_anilist_service)
        
        # Call the function, should raise exception
        with pytest.raises(HTTPException) as exc_info:
            await get_story(
                story_id="story_123",
                db=mock_db_session,
                anilist_service=mock_anilist_service
            )
        
        # Verify cache check
        mock_story_cache.get.assert_called_once()
        
        # Verify database retrieval
        mock_story_crud.get.assert_called_once_with(mock_db_session, id="story_123")
        
        # Verify AniList API call for missing story
        mock_anilist_service.get_anime_details.assert_called_once_with("123")
        
        # Verify story creation from AniList data
        mock_story_crud.create_or_update.assert_called_once()
        
        # Verify cache update
        mock_story_cache.set.assert_called_once()
        
        # Verify result is from AniList
        assert result.id == "story_123"
        assert result.title_english == "Test Story from AniList"
    
    async def test_story_retrieval_anilist_error(self, monkeypatch, mock_story_cache, mock_story_crud, mock_anilist_service, mock_db_session):
        """Test error handling when AniList API fails."""
        # Set up AniList error scenario
        mock_story_crud.get.return_value = None
        mock_anilist_service.get_anime_details.side_effect = Exception("AniList API error")
        
        # Patch dependencies
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_cache", mock_story_cache)
        monkeypatch.setattr("app.api.v1.endpoints.stories.story_crud", mock_story_crud)
        monkeypatch.setattr("app.api.v1.endpoints.stories.get_anilist_service", lambda: mock_anilist_service)
        
        # Call the function and expect exception
        with pytest.raises(Exception) as exc_info:
            await get_story(
                story_id="story_123",
                db=mock_db_session,
                anilist_service=mock_anilist_service
            )
        
        # Verify error message
        assert "AniList API error" in str(exc_info.value) 