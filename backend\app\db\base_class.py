from datetime import datetime
from typing import Any
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import DateTime
from app.core.logging import get_logger

logger = get_logger("db_models")

class Base(DeclarativeBase):
    """
    Base class for all database models.
    Provides common functionality and fields for all models.
    """
    
    @declared_attr.directive
    def __tablename__(cls) -> str:
        """Generate __tablename__ automatically from class name."""
        return cls.__name__.lower()

    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )
    
    def __init__(self, **kwargs: Any) -> None:
        """Initialize a model instance with proper timestamp handling."""
        for key, value in kwargs.items():
            setattr(self, key, value)
            
        if not self.created_at:
            self.created_at = datetime.utcnow()
        if not self.updated_at:
            self.updated_at = datetime.utcnow()
            
    def update(self, **kwargs: Any) -> None:
        """Update model fields with automatic timestamp handling."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()
        
    def to_dict(self) -> dict:
        """Convert model to dictionary with proper datetime handling."""
        result = {}
        for key in self.__mapper__.columns.keys():
            value = getattr(self, key)
            if isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result