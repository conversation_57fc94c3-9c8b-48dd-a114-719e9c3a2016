# Dependencies
node_modules/
.pnpm-debug.log*

# Build outputs
dist/
.vite/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Coverage and testing
coverage/
.nyc_output/
.vitest/

# Cache
.eslintcache
.stylelintcache
.cache/

# Temporary files
*.tmp
*.temp
