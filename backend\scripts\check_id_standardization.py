#!/usr/bin/env python
"""
ID Standardization Checker

This script scans the codebase for potential ID standardization issues by looking
for patterns that might indicate non-standardized IDs being used.

Expected formats:
- Theme IDs: theme_<uuid>
- Story IDs: story_<id>
- Mapping IDs: mapping_<uuid>

It will report file paths and line numbers where potential issues are found.
"""

import os
import re
import sys
from typing import List, Dict, Tuple, Set, Pattern

# ID patterns to check for
THEME_ID_PATTERN = re.compile(r'["\'](theme_[a-zA-Z0-9\-_]+)["\']')
STORY_ID_PATTERN = re.compile(r'["\'](story_[a-zA-Z0-9\-_]+)["\']')
MAPPING_ID_PATTERN = re.compile(r'["\'](mapping_[a-zA-Z0-9\-_]+)["\']')
RELATIONSHIP_ID_PATTERN = re.compile(r'["\'](rel_[a-zA-Z0-9\-_]+_[A-Z_]+_[a-zA-Z0-9\-_]+)["\']')

# Patterns for potential issues - IDs without proper prefix
RAW_THEME_ID_PATTERN = re.compile(r'theme_id\s*=\s*["\'](?!theme_)([a-zA-Z0-9\-_]+)["\']')
RAW_STORY_ID_PATTERN = re.compile(r'story_id\s*=\s*["\'](?!story_)([a-zA-Z0-9\-_]+)["\']')
RAW_MAPPING_ID_PATTERN = re.compile(r'mapping_id\s*=\s*["\'](?!mapping_)([a-zA-Z0-9\-_]+)["\']')

# Pattern for function calls with ID parameters
FUNC_WITH_ID_PATTERN = re.compile(r'(\w+)\([^)]*id\s*=\s*["\']([^"\']+)["\'][^)]*\)')
THEME_METHODS = {'get_theme', 'create_theme', 'update_theme', 'delete_theme', 'get_theme_network',
                 'get_theme_with_fallback', 'set_theme', 'get_mappings_by_theme'}
STORY_METHODS = {'get_story', 'create_story', 'update_story', 'delete_story', 
                'get_story_with_fallback', 'set_story'}
MAPPING_METHODS = {'create_relationship', 'update_relationship', 'remove_relationship', 
                  'get_relationship'}

# Directories and file types to scan
DIRS_TO_SCAN = ['app', 'tests']
FILE_EXTENSIONS = ['.py', '.graphql']

# Directories to exclude
EXCLUDE_DIRS = ['__pycache__', 'venv', 'env', '.git', '.pytest_cache']


def find_files_to_scan(base_dir: str) -> List[str]:
    """Find all relevant files to scan."""
    files_to_scan = []
    
    for root_dir in [os.path.join(base_dir, d) for d in DIRS_TO_SCAN]:
        if not os.path.exists(root_dir):
            print(f"Warning: Directory {root_dir} not found, skipping.")
            continue
            
        for root, dirs, files in os.walk(root_dir):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
            
            for file in files:
                if any(file.endswith(ext) for ext in FILE_EXTENSIONS):
                    files_to_scan.append(os.path.join(root, file))
    
    return files_to_scan


def check_file_for_id_issues(file_path: str) -> List[Tuple[int, str, str]]:
    """
    Check a file for potential ID standardization issues.
    Returns a list of tuples (line_number, line, issue_description)
    """
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            # Skip comments
            if line.strip().startswith('#') or line.strip().startswith('//'):
                continue
                
            # Check for potential theme ID issues
            if 'theme' in line.lower() and 'id' in line.lower():
                theme_ids = THEME_ID_PATTERN.findall(line)
                if not theme_ids and 'theme_' not in line and '"id"' in line:
                    issues.append((i+1, line.strip(), "Potential non-standard theme ID"))
            
            # Check for potential story ID issues
            if 'story' in line.lower() and 'id' in line.lower():
                story_ids = STORY_ID_PATTERN.findall(line)
                if not story_ids and 'story_' not in line and '"id"' in line:
                    issues.append((i+1, line.strip(), "Potential non-standard story ID"))
            
            # Check for potential mapping ID issues
            if 'mapping' in line.lower() and 'id' in line.lower():
                mapping_ids = MAPPING_ID_PATTERN.findall(line)
                if not mapping_ids and 'mapping_' not in line and '"id"' in line:
                    issues.append((i+1, line.strip(), "Potential non-standard mapping ID"))
                    
            # Check for potential relationship ID issues
            if ('relationship' in line.lower() or 'relation' in line.lower()) and 'id' in line.lower():
                rel_ids = RELATIONSHIP_ID_PATTERN.findall(line)
                if not rel_ids and 'rel_' not in line and '"id"' in line:
                    issues.append((i+1, line.strip(), "Potential non-standard relationship ID"))
                    
    except Exception as e:
        print(f"Error checking file {file_path}: {str(e)}")
        
    return issues


def main():
    """Main entry point for the script."""
    # Determine base directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)  # Go up one level to backend/
    
    print(f"Scanning for ID standardization issues in {base_dir}...")
    
    # Find files to scan
    files = find_files_to_scan(base_dir)
    print(f"Found {len(files)} files to scan.")
    
    # Track total issues
    total_issues = 0
    files_with_issues = set()
    
    # Scan each file
    for file_path in files:
        issues = check_file_for_id_issues(file_path)
        if issues:
            rel_path = os.path.relpath(file_path, base_dir)
            files_with_issues.add(rel_path)
            print(f"\nIssues in {rel_path}:")
            for line_num, issue_type, line_content in issues:
                print(f"  Line {line_num}: {issue_type}")
                print(f"    {line_content}")
            total_issues += len(issues)
    
    # Summary
    print("\n" + "="*80)
    print(f"Scan complete. Found {total_issues} potential ID standardization issues in {len(files_with_issues)} files.")
    if files_with_issues:
        print("Files with issues:")
        for file_path in sorted(files_with_issues):
            print(f"  {file_path}")
    
    return 0 if total_issues == 0 else 1


if __name__ == "__main__":
    sys.exit(main()) 