"""
CRUD operations for Story nodes in Neo4j.
This module provides story-specific database operations using Cypher queries.
"""
from typing import Optional, Dict, Any, Union, List
from datetime import datetime, timedelta
from uuid import uuid4

from neo4j import AsyncSession
import neo4j
import asyncio
import json

from app.core.logging import get_logger
from app.crud.neo4j.base import CRUDBase
from app.schemas.story import StoryCreate, StoryUpdate, StoryId

logger = get_logger("crud_neo4j_story")

class CRUDStory(CRUDBase[StoryCreate, StoryUpdate]):
    """
    CRUD operations for Story nodes in Neo4j.
    Extends the base CRUD class with story-specific operations.
    """
    
    # Maximum age of story data before considered stale (in hours)
    STALE_THRESHOLD_HOURS = 24
    
    def __init__(self):
        """Initialize with the 'Story' label."""
        super().__init__("Story")
    
    async def get_by_external_id(
        self, session: AsyncSession, *, external_id: Union[StoryId, str]
    ) -> Optional[Dict[str, Any]]:
        """
        Get a story by its external ID.
        
        Args:
            session: Neo4j session
            external_id: External ID of the story (e.g., from AniList, MAL)
            
        Returns:
            Story node properties as dictionary, or None if not found
        """
        try:
            external_id_str = str(external_id)
            self.logger.debug(f"Fetching story with external_id: {external_id_str}")
            
            query = """
            MATCH (s:Story {external_id: $external_id})
            RETURN s
            """
            
            result = await session.run(query, {"external_id": external_id_str})
            record = await result.single()
            
            if record:
                story = dict(record["s"])
                self.logger.debug(f"Found story: {story.get('title_english') or story.get('title_romaji')}")
                return story
            
            self.logger.debug(f"No story found with external_id: {external_id_str}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching story with external_id {external_id}: {str(e)}")
            raise
    
    async def create(
        self, session: AsyncSession, *, obj_in: Union[StoryCreate, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create a new story.
        
        Args:
            session: Neo4j session
            obj_in: Story creation data (Pydantic model or dict)
            
        Returns:
            Created story node properties as dictionary
        """
        try:
            # Convert input to dict if it's a Pydantic model
            if hasattr(obj_in, "model_dump"):
                create_data = obj_in.model_dump(exclude_unset=True)
            else:
                create_data = obj_in.copy()  # Make a copy to avoid modifying the original
            
            # Ensure created_at and updated_at are set
            now = datetime.utcnow().isoformat()
            create_data["created_at"] = now
            create_data["updated_at"] = now
            
            # Ensure ID is a string and has the correct prefix
            if "id" not in create_data or not isinstance(create_data["id"], str):
                create_data["id"] = f"story_{uuid4().hex}"
            elif not create_data["id"].startswith("story_"):
                create_data["id"] = f"story_{create_data['id']}"
            
            # Ensure external_id is a string
            if "external_id" in create_data:
                create_data["external_id"] = str(create_data["external_id"])
                
            # Build SET clause for node properties
            props = []
            for key, value in create_data.items():
                props.append(f"s.{key} = ${key}")
            
            props_str = ", ".join(props)
            
            query = f"""
            CREATE (s:Story)
            SET {props_str}
            RETURN s
            """
            
            result = await session.run(query, create_data)
            record = await result.single()
            
            if record:
                story = dict(record["s"])
                self.logger.info(f"Created new story: {story.get('title_english') or story.get('title_romaji')}")
                return story
            
            raise Exception("Failed to create story")
            
        except Exception as e:
            self.logger.error(f"Error creating story: {str(e)}")
            raise
    
    async def update(
        self, 
        session: AsyncSession, 
        *, 
        db_obj: Dict[str, Any], 
        obj_in: Union[StoryUpdate, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Update an existing story.
        
        Args:
            session: Neo4j session
            db_obj: Existing story data (dictionary)
            obj_in: Story update data (Pydantic model or dict)
            
        Returns:
            Updated story node properties as dictionary
        """
        try:
            # Convert input to dict if it's a Pydantic model
            if hasattr(obj_in, "model_dump"):
                update_data = obj_in.model_dump(exclude_unset=True)
            else:
                update_data = obj_in.copy()  # Make a copy to avoid modifying the original
            
            # Always update the updated_at timestamp
            update_data["updated_at"] = datetime.utcnow().isoformat()
            
            # Ensure ID is not changed
            if "id" in update_data:
                del update_data["id"]
            
            # Ensure external_id is a string if present
            if "external_id" in update_data:
                update_data["external_id"] = str(update_data["external_id"])
            
            # Log significant changes
            title = update_data.get("title_english") or update_data.get("title_romaji")
            story_id = db_obj.get("id")
            
            if title:
                self.logger.info(f"Updating story: {title}")
            else:
                self.logger.info(f"Updating story with ID: {story_id}")
                
            # Nothing to update
            if not update_data:
                self.logger.debug(f"No properties to update for story {story_id}")
                return db_obj
                
            # Build SET clause for updating properties
            props = []
            for key in update_data.keys():
                props.append(f"s.{key} = ${key}")
            
            props_str = ", ".join(props)
            params = {"id": story_id, **update_data}
            
            query = f"""
            MATCH (s:Story {{id: $id}})
            SET {props_str}
            RETURN s
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if record:
                story = dict(record["s"])
                self.logger.info(f"Updated story: {story_id}")
                return story
            
            self.logger.warning(f"No story found to update with ID: {story_id}")
            return db_obj
            
        except Exception as e:
            self.logger.error(f"Error updating story: {str(e)}")
            raise
    
    async def create_or_update(
        self, session: AsyncSession, *, obj_in: Union[StoryCreate, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create or update a story using Neo4j's MERGE operation.
        
        Args:
            session: Neo4j session
            obj_in: Story data (Pydantic model or dict)
            
        Returns:
            Created or updated story node properties as dictionary
        """
        # Maximum number of retries for transaction termination errors
        MAX_RETRIES = 3
        retry_count = 0
        last_error = None
        
        while retry_count < MAX_RETRIES:
            try:
                # Convert input to dict if it's a Pydantic model
                if hasattr(obj_in, "model_dump"):
                    create_data = obj_in.model_dump(exclude_unset=True)
                else:
                    create_data = obj_in.copy()  # Make a copy to avoid modifying the original
                
                # External ID is required for merging
                external_id = str(create_data.get("external_id"))
                if not external_id:
                    raise ValueError("external_id is required for create_or_update operation")
                    
                self.logger.debug(f"Creating or updating story with external_id: {external_id}")
                
                # Ensure timestamps are set
                now = datetime.utcnow().isoformat()
                if "created_at" not in create_data:
                    create_data["created_at"] = now
                create_data["updated_at"] = now
                
                # Ensure ID has the correct prefix if provided
                if "id" in create_data and isinstance(create_data["id"], str):
                    if not create_data["id"].startswith("story_"):
                        create_data["id"] = f"story_{create_data['id']}"
                else:
                    create_data["id"] = f"story_{external_id}"  # Use external_id for story ID to ensure uniqueness
                
                # Process nested structures to make them Neo4j compatible
                for key, value in create_data.items():
                    # Convert datetime objects to ISO format strings
                    if isinstance(value, datetime):
                        create_data[key] = value.isoformat()
                    # Convert complex nested structures to JSON strings
                    elif key == "story_metadata" and isinstance(value, dict):
                        # Process each field in story_metadata
                        for metadata_key, metadata_value in value.items():
                            if isinstance(metadata_value, (dict, list)):
                                value[metadata_key] = json.dumps(metadata_value)
                        create_data[key] = json.dumps(value)
                    # Convert any other complex structures
                    elif isinstance(value, (dict, list)) and key != "external_id":
                        create_data[key] = json.dumps(value)
                
                # Build SET clause for node properties
                props = []
                for key, value in create_data.items():
                    if key not in ["id", "external_id"]:  # id and external_id are used in the MERGE
                        props.append(f"s.{key} = ${key}")
                
                props_str = ", ".join(props)
                
                # Using MERGE on both id and external_id to avoid constraint issues
                query = f"""
                MERGE (s:Story {{id: $id}})
                ON CREATE SET s.id = $id, s.external_id = $external_id, {props_str}
                ON MATCH SET s.external_id = $external_id, {props_str}
                RETURN s
                """
                
                result = await session.run(query, create_data)
                record = await result.single()
                
                if record:
                    story = dict(record["s"])
                    self.logger.info(f"Created or updated story with id: {create_data['id']}, external_id: {external_id}")
                    return story
                
                raise Exception(f"Failed to create or update story with external_id: {external_id}")
                
            except neo4j.exceptions.ClientError as e:
                # Handle Neo4j transaction termination errors specially
                if "terminated" in str(e) or "Neo.ClientError.Transaction.Terminated" in str(e):
                    retry_count += 1
                    last_error = e
                    wait_time = retry_count * 0.25  # Exponential backoff: 0.25s, 0.5s, 0.75s
                    
                    self.logger.warning(
                        f"Neo4j transaction terminated for story {create_data.get('id', 'unknown')}. "
                        f"Retry {retry_count}/{MAX_RETRIES} in {wait_time}s"
                    )
                    
                    # Wait before retrying to allow the system to stabilize
                    await asyncio.sleep(wait_time)
                    
                    # If this was the last session in this session, try to create a new session
                    if retry_count == MAX_RETRIES - 1:
                        try:
                            # Create a new session for the final retry
                            session = neo4j.AsyncSession(
                                driver=session._driver,
                                default_access_mode="WRITE"
                            )
                            self.logger.info("Created fresh Neo4j session for final retry")
                        except Exception as new_session_error:
                            self.logger.error(f"Failed to create new session: {str(new_session_error)}")
                else:
                    # For other Neo4j errors, raise immediately
                    self.logger.error(f"Neo4j error in create_or_update: {str(e)}")
                    raise
            except Exception as e:
                self.logger.error(f"Error in create_or_update: {str(e)}")
                raise
        
        # If we've exhausted all retries
        if last_error:
            self.logger.error(f"All retries failed for story {create_data.get('id', 'unknown')}: {str(last_error)}")
            raise last_error
    
    def is_stale(self, story: Dict[str, Any]) -> bool:
        """
        Check if a story's data is stale and needs updating.
        A story is considered stale if:
        1. It has no updated_at timestamp
        2. Its updated_at timestamp is older than STALE_THRESHOLD_HOURS
        3. It has missing required data (e.g., no title)
        
        Args:
            story: Story data dictionary
            
        Returns:
            True if the story data is stale, False otherwise
        """
        # Check if updated_at exists
        if "updated_at" not in story or not story["updated_at"]:
            self.logger.debug(f"Story {story.get('external_id')} is stale: no updated_at timestamp")
            return True
        
        # Parse the timestamp and check if it's too old
        try:
            updated_at = datetime.fromisoformat(story["updated_at"].replace("Z", "+00:00"))
            stale_threshold = datetime.utcnow() - timedelta(hours=self.STALE_THRESHOLD_HOURS)
            
            if updated_at < stale_threshold:
                self.logger.debug(f"Story {story.get('external_id')} is stale: last updated at {updated_at}")
                return True
        except (ValueError, TypeError):
            # If we can't parse the timestamp, consider it stale
            self.logger.debug(f"Story {story.get('external_id')} is stale: invalid updated_at format")
            return True
        
        # Check for missing required data
        if not (story.get("title_english") or story.get("title_romaji")):
            self.logger.debug(f"Story {story.get('external_id')} is stale: missing title")
            return True
        
        return False

    async def get_stories_with_same_themes(
        self, 
        session: AsyncSession, 
        *, 
        story_id: str, 
        limit: int = 10, 
        different_media_type: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Find stories that share themes with the given story.
        Demonstrates the graph traversal capabilities of Neo4j for recommendations.
        
        Args:
            session: Neo4j session
            story_id: ID of the story
            limit: Maximum number of stories to return
            different_media_type: Whether to only include stories with different media types
            
        Returns:
            List of similar stories with similarity scores
        """
        try:
            self.logger.debug(f"Finding stories with same themes as story {story_id}")
            
            # Media type filter
            media_type_filter = ""
            if different_media_type:
                media_type_filter = "AND s1.media_type <> s2.media_type"
                
            # Using graph traversal to find stories with shared themes
            query = f"""
            MATCH (s1:Story {{id: $story_id}})-[r1:HAS_THEME]->(t:Theme)<-[r2:HAS_THEME]-(s2:Story)
            WHERE s1 <> s2 {media_type_filter}
            WITH s2, SUM(r1.strength * r2.strength) AS similarity
            RETURN s2, similarity
            ORDER BY similarity DESC
            LIMIT $limit
            """
            
            result = await session.run(query, {"story_id": story_id, "limit": limit})
            records = await result.fetch()
            
            # Format the results
            similar_stories = []
            for record in records:
                story = dict(record["s2"])
                story["similarity_score"] = record["similarity"]
                similar_stories.append(story)
                
            self.logger.debug(f"Found {len(similar_stories)} similar stories for {story_id}")
            return similar_stories
            
        except Exception as e:
            self.logger.error(f"Error finding similar stories for {story_id}: {str(e)}")
            raise

# Create a singleton instance
story = CRUDStory() 