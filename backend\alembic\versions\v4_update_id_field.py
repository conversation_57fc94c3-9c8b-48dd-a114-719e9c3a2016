"""Update Story ID field to string

Revision ID: v4
Revises: v3
Create Date: 2024-12-18 20:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'v4'
down_revision = 'v3'
branch_labels = None
depends_on = None

def upgrade():
    # Create a temporary table with the new schema
    op.create_table(
        'stories_new',
        sa.Column('id', sa.String(100), primary_key=True),
        sa.Column('external_id', sa.String(100), unique=True, index=True, nullable=False),
        sa.Column('title_english', sa.String(255), nullable=True),
        sa.Column('title_romaji', sa.String(255), nullable=False),
        sa.Column('title_native', sa.String(255), nullable=True),
        sa.Column('synopsis', sa.String(), nullable=True),
        sa.Column('media_type', sa.String(50), nullable=False),
        sa.Column('source', sa.String(50), nullable=False),
        sa.Column('cover_image_large', sa.String(500), nullable=True),
        sa.Column('cover_image_medium', sa.String(500), nullable=True),
        sa.Column('banner_image', sa.String(500), nullable=True),
        sa.Column('status', sa.String(50), nullable=True),
        sa.Column('popularity', sa.Integer(), nullable=True),
        sa.Column('average_score', sa.Float(), nullable=True),
        sa.Column('episode_count', sa.Integer(), nullable=True),
        sa.Column('episode_duration', sa.Integer(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('season', sa.String(50), nullable=True),
        sa.Column('story_metadata', postgresql.JSONB(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False)
    )

    # Copy data from old table to new table, converting ID to string with prefix
    op.execute("""
        INSERT INTO stories_new 
        SELECT 
            CASE 
                WHEN id::text LIKE 'story_%' THEN id::text
                ELSE 'story_' || id::text
            END as id,
            CASE 
                WHEN external_id IS NULL THEN id::text
                ELSE external_id
            END as external_id,
            title_english,
            COALESCE(title_romaji, title_english, title_native, 'Untitled') as title_romaji,
            title_native,
            synopsis,
            media_type,
            COALESCE(source, 'anilist') as source,
            cover_image_large,
            cover_image_medium,
            banner_image,
            status,
            popularity,
            average_score,
            episode_count,
            episode_duration,
            start_date,
            end_date,
            season,
            COALESCE(story_metadata, '{"genres":[],"tags":[],"studios":[],"relations":[],"recommendations":[]}') as story_metadata,
            COALESCE(created_at, NOW()) as created_at,
            COALESCE(updated_at, NOW()) as updated_at
        FROM stories
    """)

    # Drop old table and rename new table
    op.drop_table('stories')
    op.rename_table('stories_new', 'stories')

    # Recreate indexes
    op.create_index('ix_stories_external_id', 'stories', ['external_id'], unique=True)
    op.create_index('ix_stories_media_type', 'stories', ['media_type'])
    op.create_index('ix_stories_status', 'stories', ['status'])
    op.create_index('ix_stories_average_score', 'stories', ['average_score'])
    op.create_index('ix_stories_popularity', 'stories', ['popularity'])

def downgrade():
    # Create a temporary table with the old schema
    op.create_table(
        'stories_old',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('external_id', sa.String(100), unique=True, index=True),
        sa.Column('title_english', sa.String(255), nullable=True),
        sa.Column('title_romaji', sa.String(255), nullable=False),
        sa.Column('title_native', sa.String(255), nullable=True),
        sa.Column('synopsis', sa.String(), nullable=True),
        sa.Column('media_type', sa.String(50), nullable=False),
        sa.Column('source', sa.String(50), nullable=False),
        sa.Column('cover_image_large', sa.String(500), nullable=True),
        sa.Column('cover_image_medium', sa.String(500), nullable=True),
        sa.Column('banner_image', sa.String(500), nullable=True),
        sa.Column('status', sa.String(50), nullable=True),
        sa.Column('popularity', sa.Integer(), nullable=True),
        sa.Column('average_score', sa.Float(), nullable=True),
        sa.Column('episode_count', sa.Integer(), nullable=True),
        sa.Column('episode_duration', sa.Integer(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('season', sa.String(50), nullable=True),
        sa.Column('story_metadata', postgresql.JSONB(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False)
    )

    # Copy data from new table to old table, converting string ID back to integer
    op.execute("""
        INSERT INTO stories_old 
        SELECT 
            CASE 
                WHEN id LIKE 'story_%' THEN SUBSTRING(id FROM 7)::integer
                ELSE id::integer
            END as id,
            external_id,
            title_english,
            COALESCE(title_romaji, title_english, title_native, 'Untitled') as title_romaji,
            title_native,
            synopsis,
            media_type,
            source,
            cover_image_large,
            cover_image_medium,
            banner_image,
            status,
            popularity,
            average_score,
            episode_count,
            episode_duration,
            start_date,
            end_date,
            season,
            story_metadata,
            created_at,
            updated_at
        FROM stories
        WHERE id ~ '^story_[0-9]+$' OR id ~ '^[0-9]+$'
    """)

    # Drop new table and rename old table
    op.drop_table('stories')
    op.rename_table('stories_old', 'stories')

    # Recreate indexes
    op.create_index('ix_stories_external_id', 'stories', ['external_id'], unique=True)
    op.create_index('ix_stories_media_type', 'stories', ['media_type'])
    op.create_index('ix_stories_status', 'stories', ['status'])
    op.create_index('ix_stories_average_score', 'stories', ['average_score'])
    op.create_index('ix_stories_popularity', 'stories', ['popularity']) 