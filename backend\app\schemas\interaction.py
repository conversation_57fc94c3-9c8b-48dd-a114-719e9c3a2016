from datetime import datetime
from typing import Optional, Dict, Literal
from pydantic import BaseModel, Field, validator
from enum import Enum

class InteractionType(str, Enum):
    """Enum for interaction types."""
    SEARCH = "search"
    CLICK = "click"
    VIEW = "view"
    RECOMMENDATION = "recommendation"

class SearchFilters(BaseModel):
    mediaType: Optional[str] = None
    minScore: Optional[int] = Field(None, ge=0, le=100)
    sort: Optional[Literal["popularity", "score", "trending"]] = "popularity"

class SearchRequest(BaseModel):
    """Schema for search requests."""
    query: str = Field(..., min_length=1)
    filters: Optional[SearchFilters] = None
    page: int = Field(1, ge=1)
    per_page: int = Field(10, ge=1, le=100)

    @validator('query')
    def query_not_empty(cls, v):
        """Validate that query is not empty."""
        if not v.strip():
            raise ValueError("Search query cannot be empty")
        return v

class InteractionBase(BaseModel):
    session_id: str
    interaction_type: str = Field(..., description="Type of interaction (search, click, view)")
    content_id: Optional[int] = None
    content_type: str = Field(..., description="Type of content interacted with")
    metadata: Dict = Field(default_factory=dict)

class InteractionCreate(InteractionBase):
    pass

class Interaction(InteractionBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class SearchQueryBase(BaseModel):
    session_id: str
    query: str
    filters: Optional[Dict] = Field(default_factory=dict)

class SearchQueryCreate(SearchQueryBase):
    results_count: int

class SearchQuery(SearchQueryBase):
    id: int
    results_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True