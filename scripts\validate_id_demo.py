#!/usr/bin/env python3
"""
Demo ID Validation Script

This script demonstrates how the ID validation process works.
It shows how the validate_id_usage.py would scan files for ID standardization issues.
"""

import os
import re
import json
from collections import defaultdict
from typing import Dict, List, Tuple, Set, Any

# Import our mock IdService
from mock_id_service import IdService

class IdValidationDemo:
    """Demo for ID validation in codebase files."""
    
    # Entity types we want to detect in code
    ENTITY_TYPES = list(IdService.ENTITY_PREFIXES.keys())
    
    # Patterns for detecting potential IDs in code
    ID_PATTERNS = [
        # Direct ID assignment: id = "value"
        r'(?:^|\s+)(?:id|ID|Id)\s*=\s*[\'"]([^\'"]+)[\'"]',
        
        # ID in function calls: get_theme("adventure")
        r'get_(\w+)\s*\(\s*[\'"]([^\'"]+)[\'"]',
        
        # ID in dictionary: {"id": "value"}
        r'[\'"](?:id|ID|Id)[\'"](?:\s)*:(?:\s)*[\'"]([^\'"]+)[\'"]',
        
        # ID in variables: theme_id = "value"
        r'(\w+)_id\s*=\s*[\'"]([^\'"]+)[\'"]',
        
        # ID in queries: WHERE id = "value"
        r'WHERE\s+(?:[\w\.]+\.)?(?:id|ID|Id)\s*=\s*(?:\$id|[\'"]([^\'"]+)[\'"])',
        
        # ID in parameters: id: "value"
        r'(?:id|ID|Id)(?:\s)*:(?:\s)*[\'"]([^\'"]+)[\'"]',
    ]
    
    def __init__(self):
        """Initialize the demo."""
        self.issues = defaultdict(list)
        self.fixed_issues = defaultdict(list)
        self.valid_ids = defaultdict(list)
        self.entity_counts = defaultdict(int)
        
    def scan_code_snippet(self, code: str, file_path: str, autofix: bool = False) -> List[Dict[str, Any]]:
        """
        Scan a code snippet for ID standardization issues.
        
        Args:
            code: The code snippet to scan
            file_path: Path to the file (for reporting)
            autofix: Whether to automatically fix issues
            
        Returns:
            A list of issues found
        """
        issues = []
        
        # Scan each line of the code
        for line_no, line in enumerate(code.split('\n'), 1):
            for pattern in self.ID_PATTERNS:
                # Find all matches of the pattern in the line
                for match in re.finditer(pattern, line):
                    # Extract the potential ID and entity type
                    if len(match.groups()) == 1:
                        # If we have one group, it's an ID without a clear entity type
                        potential_id = match.group(1)
                        if not potential_id:  # Skip if no match in the group
                            continue
                        entity_type = self._guess_entity_type(potential_id)
                    else:
                        # If we have two groups, first is entity type, second is ID
                        entity_type = match.group(1)
                        potential_id = match.group(2)
                        if not potential_id:  # Skip if no match in the group
                            continue
                    
                    # Skip if we couldn't determine the entity type
                    if not entity_type:
                        # Try to infer from the file path
                        entity_type = self._infer_entity_type_from_path(file_path)
                        if not entity_type:
                            continue
                    
                    # Normalize entity type to singular form
                    entity_type = self._normalize_entity_type(entity_type)
                    
                    # Check if the ID is valid
                    is_valid = IdService.validate_id(potential_id, entity_type)
                    
                    if is_valid:
                        # Record valid ID
                        self.valid_ids[entity_type].append(potential_id)
                        self.entity_counts[entity_type] += 1
                    else:
                        # Standardize the ID
                        std_id = IdService.standardize_id(potential_id, entity_type)
                        
                        # Record the issue
                        issue = {
                            'file': file_path,
                            'line': line_no,
                            'line_content': line,
                            'entity_type': entity_type,
                            'invalid_id': potential_id,
                            'standard_id': std_id,
                            'match_start': match.start(),
                            'match_end': match.end(),
                        }
                        issues.append(issue)
                        self.issues[entity_type].append(issue)
                        self.entity_counts[entity_type] += 1
                        
                        # Autofix if requested
                        if autofix:
                            # Create a fixed line by replacing the invalid ID with the standard ID
                            fixed_line = line.replace(f'"{potential_id}"', f'"{std_id}"')
                            issue['fixed_line'] = fixed_line
                            self.fixed_issues[entity_type].append(issue)
        
        return issues
    
    def _guess_entity_type(self, id_value: str) -> str:
        """
        Guess the entity type from an ID value.
        
        Args:
            id_value: The ID value to check
            
        Returns:
            The guessed entity type, or empty string if unknown
        """
        # Check if the ID has a prefix
        parts = id_value.split('_', 1)
        if len(parts) > 1:
            prefix = parts[0]
            # Check if the prefix matches a known entity type
            for entity_type, entity_prefix in IdService.ENTITY_PREFIXES.items():
                if prefix == entity_prefix:
                    return entity_type
        
        # If no prefix match, return empty string
        return ""
    
    def _infer_entity_type_from_path(self, file_path: str) -> str:
        """
        Infer the entity type from the file path.
        
        Args:
            file_path: The file path to check
            
        Returns:
            The inferred entity type, or empty string if unknown
        """
        # Extract the file name without extension
        file_name = os.path.basename(file_path)
        file_name = os.path.splitext(file_name)[0]
        
        # Check if the file name contains an entity type
        for entity_type in self.ENTITY_TYPES:
            if entity_type in file_name:
                return entity_type
        
        # Check if the directory contains an entity type
        dir_name = os.path.dirname(file_path)
        for entity_type in self.ENTITY_TYPES:
            if entity_type in dir_name:
                return entity_type
        
        # If no match, return empty string
        return ""
    
    def _normalize_entity_type(self, entity_type: str) -> str:
        """
        Normalize an entity type to singular form.
        
        Args:
            entity_type: The entity type to normalize
            
        Returns:
            The normalized entity type
        """
        # Remove trailing 's' for plurals
        if entity_type.endswith('s') and entity_type[:-1] in self.ENTITY_TYPES:
            return entity_type[:-1]
        
        # Check for exact match
        if entity_type in self.ENTITY_TYPES:
            return entity_type
        
        # Default return the original
        return entity_type
    
    def generate_report(self) -> Dict[str, Any]:
        """
        Generate a report of issues found.
        
        Returns:
            A dictionary with the report data
        """
        total_issues = sum(len(issues) for issues in self.issues.values())
        total_valid = sum(len(ids) for ids in self.valid_ids.values())
        total_fixed = sum(len(issues) for issues in self.fixed_issues.values())
        
        report = {
            'summary': {
                'total_entities': sum(self.entity_counts.values()),
                'total_issues': total_issues,
                'total_valid': total_valid,
                'total_fixed': total_fixed,
                'compliance_rate': (total_valid / sum(self.entity_counts.values())) * 100 if sum(self.entity_counts.values()) > 0 else 100,
            },
            'by_entity_type': {
                entity_type: {
                    'total': self.entity_counts[entity_type],
                    'issues': len(self.issues[entity_type]),
                    'valid': len(self.valid_ids[entity_type]),
                    'fixed': len(self.fixed_issues[entity_type]),
                    'compliance_rate': (len(self.valid_ids[entity_type]) / self.entity_counts[entity_type]) * 100 if self.entity_counts[entity_type] > 0 else 100,
                }
                for entity_type in set(self.entity_counts.keys())
            },
            'issues': {
                entity_type: [
                    {
                        'file': issue['file'],
                        'line': issue['line'],
                        'line_content': issue['line_content'],
                        'invalid_id': issue['invalid_id'],
                        'standard_id': issue['standard_id'],
                        'fixed_line': issue.get('fixed_line', None),
                    }
                    for issue in issues
                ]
                for entity_type, issues in self.issues.items()
            }
        }
        
        return report

def run_demo():
    """Run the ID validation demo."""
    print("\n=== ID Validation Demo ===\n")
    
    # Create some example code snippets with ID issues
    code_samples = [
        {
            'file': 'resolvers/theme_resolver.py',
            'code': '''
def get_theme_by_id(id):
    """Get a theme by ID."""
    # This ID should be prefixed with theme_
    theme = db.find_one("themes", {"id": "adventure"})
    return theme

def get_themes_by_ids(ids):
    """Get themes by IDs."""
    # These IDs should be prefixed with theme_
    themes = db.find_many("themes", {"id": {"$in": ["journey", "mystery", "theme_fantasy"]}})
    return themes
'''
        },
        {
            'file': 'services/story_service.py',
            'code': '''
def create_story(title, theme_id):
    """Create a new story."""
    # theme_id doesn't have the right prefix
    story_id = generate_id()
    story = {
        "id": story_id,
        "title": title,
        "theme_id": "adventure",
        "created_at": datetime.now()
    }
    db.insert("stories", story)
    return story

def get_story(id):
    """Get a story by ID."""
    # id doesn't have the right prefix
    return db.find_one("stories", {"id": id})

def update_story(story_id, title=None, theme_id=None):
    """Update a story."""
    # story_id and theme_id should have prefixes
    updates = {}
    if title:
        updates["title"] = title
    if theme_id:
        updates["theme_id"] = theme_id
    db.update("stories", {"id": story_id}, {"$set": updates})
    return get_story(story_id)
'''
        },
        {
            'file': 'models/character.py',
            'code': '''
class Character:
    """Character model."""
    
    def __init__(self, id=None, name=None, story_id=None):
        """Initialize a character."""
        # id and story_id should have prefixes
        self.id = id or generate_id()
        self.name = name
        self.story_id = story_id
    
    def to_dict(self):
        """Convert to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "story_id": self.story_id
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create from dictionary."""
        return cls(
            id=data.get("id"),
            name=data.get("name"),
            story_id=data.get("story_id")
        )
'''
        },
        {
            'file': 'database/queries.py',
            'code': '''
def get_theme(theme_id):
    """Get a theme by ID."""
    # This is correct, but we'll include it to show valid IDs too
    return db.run_query("""
        MATCH (t:Theme)
        WHERE t.id = $id
        RETURN t
    """, {"id": theme_id})

def get_story_with_theme(story_id):
    """Get a story with its theme."""
    # story_id doesn't have prefix, theme_id is used in query without variable
    return db.run_query("""
        MATCH (s:Story)-[:HAS_THEME]->(t:Theme)
        WHERE s.id = $id
        RETURN s, t.id as theme_id, t.name as theme_name
    """, {"id": story_id})

def get_character_with_story(character_id):
    """Get a character with its story."""
    # This is correct, but we'll include it to show valid IDs too
    return db.run_query("""
        MATCH (c:Character)-[:BELONGS_TO]->(s:Story)
        WHERE c.id = $id
        RETURN c, s
    """, {"id": character_id})
'''
        }
    ]
    
    # Create the validator
    validator = IdValidationDemo()
    
    # Scan each code sample
    print("Scanning code samples for ID issues...\n")
    for sample in code_samples:
        print(f"Scanning {sample['file']}...")
        issues = validator.scan_code_snippet(sample['code'], sample['file'])
        if issues:
            print(f"  Found {len(issues)} issues.\n")
        else:
            print("  No issues found.\n")
    
    # Generate and print the report
    report = validator.generate_report()
    
    print("\nValidation Report Summary:")
    print(f"  Total entities checked: {report['summary']['total_entities']}")
    print(f"  Valid IDs: {report['summary']['total_valid']}")
    print(f"  Invalid IDs: {report['summary']['total_issues']}")
    print(f"  Compliance rate: {report['summary']['compliance_rate']:.2f}%\n")
    
    print("Issues by Entity Type:")
    for entity_type, stats in report['by_entity_type'].items():
        if stats['issues'] > 0:
            print(f"  {entity_type.capitalize()}: {stats['issues']} issues out of {stats['total']} ({stats['compliance_rate']:.2f}% compliant)")
    
    print("\nExample Issues:")
    for entity_type, issues in report['issues'].items():
        if issues:
            print(f"\n  {entity_type.capitalize()} Issues:")
            for issue in issues[:3]:  # Show at most 3 examples per entity type
                print(f"    File: {issue['file']}, Line: {issue['line']}")
                print(f"    Content: {issue['line_content'].strip()}")
                print(f"    Invalid ID: {issue['invalid_id']}")
                print(f"    Standard ID: {issue['standard_id']}")
                print()
    
    # Now let's fix the issues
    print("\nAutofixing issues...")
    validator = IdValidationDemo()  # Start fresh
    
    for sample in code_samples:
        validator.scan_code_snippet(sample['code'], sample['file'], autofix=True)
    
    fixed_report = validator.generate_report()
    
    print(f"  Total issues fixed: {fixed_report['summary']['total_fixed']} out of {fixed_report['summary']['total_issues']}\n")
    
    print("Example Fixes:")
    for entity_type, issues in fixed_report['issues'].items():
        if issues:
            print(f"\n  {entity_type.capitalize()} Fixes:")
            for issue in issues[:2]:  # Show at most 2 examples per entity type
                print(f"    File: {issue['file']}, Line: {issue['line']}")
                print(f"    Before: {issue['line_content'].strip()}")
                print(f"    After:  {issue['fixed_line'].strip()}")
                print()
    
    print("\n=== End of Demo ===\n")

if __name__ == "__main__":
    run_demo() 