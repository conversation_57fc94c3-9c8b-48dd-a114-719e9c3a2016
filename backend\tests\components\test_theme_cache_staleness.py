"""
Test the stale cache detection in the ThemeRedisService.
"""
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from app.core.redis import RedisConnection
from app.services.theme_redis import ThemeRedisService
from app.crud.neo4j.theme import CRUDTheme
from app.core.cache import CacheManager

# Test data
THEME_DATA = {
    "id": "theme_12345",
    "name": "Test Theme",
    "description": "A test theme",
    "status": "ACTIVE",
}

UPDATED_THEME_DATA = {
    "id": "theme_12345",
    "name": "Updated Test Theme",
    "description": "An updated test theme",
    "status": "ACTIVE",
    "updated_at": (datetime.utcnow().isoformat()),
}

@pytest.fixture
def redis_mock():
    """Mock Redis client."""
    redis_instance = AsyncMock()
    redis_instance.pipeline.return_value = redis_instance
    redis_instance.execute.return_value = [True, True]
    return redis_instance

@pytest.fixture
def theme_redis_service():
    """Create a ThemeRedisService instance."""
    service = ThemeRedisService()
    # Set a smaller stale time for testing
    service.max_stale_time = 3600  # 1 hour
    return service

@pytest.mark.asyncio
class TestThemeCacheStaleness:
    """Test suite for theme cache staleness detection."""
    
    async def test_check_if_stale_no_timestamp(self, theme_redis_service, monkeypatch):
        """Test theme with no cache timestamp is considered stale."""
        # Create theme data without cache_timestamp
        theme_data = THEME_DATA.copy()
        
        # Call check_if_stale
        is_stale = await theme_redis_service.check_if_stale(None, "theme_12345", theme_data)
        
        # Verify result
        assert is_stale is True
    
    async def test_check_if_stale_recent_cache(self, theme_redis_service, monkeypatch):
        """Test recently cached theme is not considered stale."""
        # Create theme data with recent cache_timestamp
        theme_data = THEME_DATA.copy()
        theme_data["cache_timestamp"] = datetime.utcnow().isoformat()
        
        # Call check_if_stale
        is_stale = await theme_redis_service.check_if_stale(None, "theme_12345", theme_data)
        
        # Verify result
        assert is_stale is False
    
    async def test_check_if_stale_old_cache_updated_theme(self, theme_redis_service, monkeypatch):
        """Test old cached theme with Neo4j updates is considered stale."""
        # Create theme data with old cache_timestamp
        theme_data = THEME_DATA.copy()
        theme_data["cache_timestamp"] = (datetime.utcnow() - timedelta(hours=2)).isoformat()
        
        # Mock Neo4j get method
        neo4j_get_mock = AsyncMock(return_value=UPDATED_THEME_DATA)
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Call check_if_stale
        is_stale = await theme_redis_service.check_if_stale(None, "theme_12345", theme_data)
        
        # Verify result
        assert is_stale is True
    
    async def test_check_if_stale_old_cache_same_theme(self, theme_redis_service, monkeypatch):
        """Test old cached theme with no Neo4j updates is not considered stale."""
        # Create theme data with old cache_timestamp
        theme_data = THEME_DATA.copy()
        old_time = datetime.utcnow() - timedelta(hours=2)
        theme_data["cache_timestamp"] = old_time.isoformat()
        
        # Create Neo4j theme data with older updated_at
        db_theme = THEME_DATA.copy()
        db_theme["updated_at"] = (old_time - timedelta(hours=1)).isoformat()
        
        # Mock Neo4j get method
        neo4j_get_mock = AsyncMock(return_value=db_theme)
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Call check_if_stale
        is_stale = await theme_redis_service.check_if_stale(None, "theme_12345", theme_data)
        
        # Verify result
        assert is_stale is False
    
    async def test_check_if_stale_theme_not_in_neo4j(self, theme_redis_service, monkeypatch):
        """Test cached theme not in Neo4j is considered stale."""
        # Create theme data with old cache_timestamp
        theme_data = THEME_DATA.copy()
        theme_data["cache_timestamp"] = (datetime.utcnow() - timedelta(hours=2)).isoformat()
        
        # Mock Neo4j get method to return None
        neo4j_get_mock = AsyncMock(return_value=None)
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Call check_if_stale
        is_stale = await theme_redis_service.check_if_stale(None, "theme_12345", theme_data)
        
        # Verify result
        assert is_stale is True
    
    async def test_get_theme_with_fallback_fresh_cache(self, theme_redis_service, monkeypatch):
        """Test get_theme_with_fallback returns fresh cached theme."""
        # Create theme data with recent cache_timestamp
        theme_data = THEME_DATA.copy()
        theme_data["cache_timestamp"] = datetime.utcnow().isoformat()
        
        # Mock cache get
        get_json_mock = AsyncMock(return_value=theme_data)
        monkeypatch.setattr(CacheManager, "get_json", get_json_mock)
        
        # Mock check_if_stale
        check_if_stale_mock = AsyncMock(return_value=False)
        monkeypatch.setattr(theme_redis_service, "check_if_stale", check_if_stale_mock)
        
        # Ensure Neo4j is not called
        neo4j_get_mock = AsyncMock()
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Call get_theme_with_fallback
        result = await theme_redis_service.get_theme_with_fallback(None, "theme_12345")
        
        # Verify results
        assert result == theme_data
        neo4j_get_mock.assert_not_called()
    
    async def test_get_theme_with_fallback_stale_cache(self, theme_redis_service, monkeypatch):
        """Test get_theme_with_fallback updates stale cache."""
        # Create theme data with old cache_timestamp
        theme_data = THEME_DATA.copy()
        theme_data["cache_timestamp"] = (datetime.utcnow() - timedelta(hours=2)).isoformat()
        
        # Mock cache get
        get_json_mock = AsyncMock(return_value=theme_data)
        monkeypatch.setattr(CacheManager, "get_json", get_json_mock)
        
        # Mock check_if_stale
        check_if_stale_mock = AsyncMock(return_value=True)
        monkeypatch.setattr(theme_redis_service, "check_if_stale", check_if_stale_mock)
        
        # Mock Neo4j get
        neo4j_get_mock = AsyncMock(return_value=UPDATED_THEME_DATA)
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Mock cache set
        set_json_mock = AsyncMock(return_value=True)
        monkeypatch.setattr(CacheManager, "set_json", set_json_mock)
        
        # Call get_theme_with_fallback
        result = await theme_redis_service.get_theme_with_fallback(None, "theme_12345")
        
        # Verify results
        assert result == UPDATED_THEME_DATA
        neo4j_get_mock.assert_called_once()
        set_json_mock.assert_called_once()
    
    async def test_refresh_stale_themes(self, theme_redis_service, monkeypatch, redis_mock):
        """Test refreshing multiple stale themes."""
        # Create keys to scan
        theme_keys = [
            "theme:theme_12345".encode(),
            "theme:theme_67890".encode()
        ]
        
        # Mock Redis scan_iter
        # Create a proper async iterable class
        class AsyncIterator:
            def __init__(self, items):
                self.items = items
                self.index = 0
                
            def __aiter__(self):
                return self
                
            async def __anext__(self):
                if self.index < len(self.items):
                    item = self.items[self.index]
                    self.index += 1
                    return item
                raise StopAsyncIteration
        
        # Set the scanner directly
        redis_mock.scan_iter = lambda match: AsyncIterator(theme_keys)
        
        # Mock Redis connection
        monkeypatch.setattr(RedisConnection, "get", AsyncMock(return_value=redis_mock))
        
        # Theme 1 is stale, Theme 2 is not
        async def get_theme_mock(theme_id):
            if theme_id == "theme_12345":
                data = THEME_DATA.copy()
                data["cache_timestamp"] = (datetime.utcnow() - timedelta(hours=2)).isoformat()
                return data
            else:
                data = THEME_DATA.copy()
                data["id"] = "theme_67890"
                data["name"] = "Another Theme"
                data["cache_timestamp"] = datetime.utcnow().isoformat()
                return data
        
        monkeypatch.setattr(theme_redis_service, "get_theme", AsyncMock(side_effect=get_theme_mock))
        
        # Theme 1 is stale, Theme 2 is not
        async def check_if_stale_mock(session, theme_id, cached_theme):
            return theme_id == "theme_12345"
        
        monkeypatch.setattr(theme_redis_service, "check_if_stale", AsyncMock(side_effect=check_if_stale_mock))
        
        # Mock Neo4j get for the stale theme
        neo4j_get_mock = AsyncMock(return_value=UPDATED_THEME_DATA)
        monkeypatch.setattr(theme_redis_service.theme_neo4j, "get", neo4j_get_mock)
        
        # Mock cache set
        monkeypatch.setattr(theme_redis_service, "set_theme", AsyncMock())
        
        # Call refresh_stale_themes
        stats = await theme_redis_service.refresh_stale_themes(None)
        
        # Verify results
        assert stats["checked"] == 2
        assert stats["refreshed"] == 1
        assert stats["errors"] == 0 