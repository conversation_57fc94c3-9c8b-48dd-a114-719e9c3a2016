from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid
from app.core.logging import get_logger
from app.db.neo4j_session import get_session
from .theme_redis import theme_redis
from app.schemas.theme import Theme, ThemeStatus as MappingStatus, ThemeMapping, ThemeMappingType
from app.crud.neo4j.theme import CRUDTheme

logger = get_logger(__name__)

# Create an instance of the CRUDTheme class
theme_crud = CRUDTheme()

class ThemeAnalysisService:
    """Service for analyzing and extracting themes from content."""

    def __init__(self):
        # Comprehensive set of base themes organized by category
        self.base_themes = {
            "MOOD": [
                {"name": "Cozy", "description": "Warm, comfortable, safe feelings", "category": "MOOD"},
                {"name": "Tense", "description": "Anxiety-inducing, on-edge atmosphere", "category": "MOOD"},
                {"name": "Melancholic", "description": "Wistful, reflective, tinged with sadness", "category": "MOOD"},
                {"name": "Uplifting", "description": "Inspiring, positive emotional response", "category": "MOOD"},
                {"name": "Horrific", "description": "Fear-inducing, disturbing", "category": "MOOD"},
                {"name": "Comedic", "description": "Humorous, light-hearted", "category": "MOOD"},
                {"name": "Romantic", "description": "Love-focused, heartwarming", "category": "MOOD"}
            ],
            "NARRATIVE_STRUCTURE": [
                {"name": "Journey", "description": "Character travels physically and/or metaphorically", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Coming of Age", "description": "Evolution from youth to maturity", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Mystery", "description": "Incremental revelation of hidden information", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Episodic", "description": "Self-contained episodes with loose continuity", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Tournament", "description": "Structured competition with escalating challenges", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Ensemble", "description": "Multiple character arcs of similar importance", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Tragedy", "description": "Inevitable downfall despite efforts", "category": "NARRATIVE_STRUCTURE"}
            ],
            "CHARACTER_DYNAMIC": [
                {"name": "Found Family", "description": "Non-blood relations forming familial bonds", "category": "CHARACTER_DYNAMIC"},
                {"name": "Rivalry", "description": "Competitive relationship driving mutual growth", "category": "CHARACTER_DYNAMIC"},
                {"name": "Mentor-Student", "description": "Knowledge/wisdom transfer relationship", "category": "CHARACTER_DYNAMIC"},
                {"name": "Romance", "description": "Romantic relationship development", "category": "CHARACTER_DYNAMIC"},
                {"name": "Ensemble Cast", "description": "Group dynamics with multiple key relationships", "category": "CHARACTER_DYNAMIC"},
                {"name": "Lone Wolf", "description": "Character operating independently despite social contexts", "category": "CHARACTER_DYNAMIC"},
                {"name": "Hero's Journey Companions", "description": "Supporting characters aiding protagonist's growth", "category": "CHARACTER_DYNAMIC"}
            ],
            "SETTING_TYPE": [
                {"name": "School Life", "description": "Educational institution as primary setting", "category": "SETTING_TYPE"},
                {"name": "Fantasy World", "description": "Imaginary setting with its own rules", "category": "SETTING_TYPE"},
                {"name": "Historical", "description": "Based in real historical period", "category": "SETTING_TYPE"},
                {"name": "Urban Modern", "description": "Contemporary city setting", "category": "SETTING_TYPE"},
                {"name": "Post-Apocalyptic", "description": "After societal collapse", "category": "SETTING_TYPE"},
                {"name": "Workplace", "description": "Professional environment focus", "category": "SETTING_TYPE"},
                {"name": "Rural", "description": "Countryside or small town setting", "category": "SETTING_TYPE"}
            ],
            # Adding identified sub-genres
            "SLICE_OF_LIFE_SUBGENRES": [
                {"name": "Instructional SoL", "description": "Learning-focused slice of life with detailed activity portrayal", "category": "NARRATIVE_STRUCTURE"},
                {"name": "Healing", "description": "Cozy slice of life with minimal conflict in natural settings", "category": "MOOD"},
                {"name": "Workplace SoL", "description": "Professional growth in occupational settings", "category": "SETTING_TYPE"},
                {"name": "Food-focused SoL", "description": "Culinary preparation and appreciation", "category": "SETTING_TYPE"},
                {"name": "Mystery SoL", "description": "Everyday mysteries with analytical characters", "category": "NARRATIVE_STRUCTURE"}
            ]
        }
        
        # Flattened list for initialization
        self.all_base_themes = []
        for category, themes in self.base_themes.items():
            self.all_base_themes.extend(themes)

    async def ensure_base_themes(self):
        """Ensure that all base themes exist in the database."""
        logger.info("Ensuring base themes exist in database")
        
        # Use the flattened list of all themes
        for theme_data in self.all_base_themes:
            theme_name = theme_data["name"]
            theme = await theme_crud.get_by_name(session, theme_name)
            
            if not theme:
                logger.debug(f"Creating base theme: {theme_name}")
                new_theme = {
                    "name": theme_name,
                    "description": theme_data.get("description", f"Description for {theme_name}"),
                    "category": theme_data.get("category", "UNCATEGORIZED"),
                    "confidence": 1.0,
                    "status": "VERIFIED"
                }
                
                await theme_crud.create(session, obj_in=new_theme)
            else:
                logger.debug(f"Base theme already exists: {theme_name}")
        
        # After creating all themes, establish relationships
        await self._establish_theme_relationships()
        
        logger.info("Base themes check completed")

    async def _establish_theme_relationships(self):
        """Establish relationships between themes based on core theme document."""
        logger.info("Establishing theme relationships")
        
        # Define key relationships from core theme document
        theme_relationships = [
            # Complementary Interactions
            {"source": "Cozy", "target": "Rural", "type": "COMPLEMENTS", "strength": 0.8},
            {"source": "Cozy", "target": "Episodic", "type": "COMPLEMENTS", "strength": 0.7},
            {"source": "Tense", "target": "Mystery", "type": "COMPLEMENTS", "strength": 0.8},
            {"source": "Tense", "target": "Urban Modern", "type": "COMPLEMENTS", "strength": 0.6},
            
            # Contrasting Interactions (creates tension)
            {"source": "Comedic", "target": "Tragedy", "type": "CONTRASTS", "tension": 0.7},
            {"source": "Cozy", "target": "Horrific", "type": "CONTRASTS", "tension": 0.9},
            
            # Sub-genre Relationships
            {"source": "Instructional SoL", "target": "Episodic", "type": "REQUIRES", "strength": 0.9},
            {"source": "Healing", "target": "Cozy", "type": "REQUIRES", "strength": 0.9},
            {"source": "Healing", "target": "Rural", "type": "COMPLEMENTS", "strength": 0.8},
            {"source": "Workplace SoL", "target": "Workplace", "type": "REQUIRES", "strength": 0.9},
            {"source": "Food-focused SoL", "target": "Episodic", "type": "COMPLEMENTS", "strength": 0.7},
            {"source": "Mystery SoL", "target": "Mystery", "type": "SPECIALIZES", "strength": 0.8},
            
            # Character Dynamic Relationships
            {"source": "Found Family", "target": "Ensemble Cast", "type": "COMPLEMENTS", "strength": 0.7},
            {"source": "Mentor-Student", "target": "Coming of Age", "type": "COMPLEMENTS", "strength": 0.8},
            {"source": "Rivalry", "target": "Tournament", "type": "COMPLEMENTS", "strength": 0.8}
        ]
        
        async with get_session() as session:
            # Create each relationship
            for rel in theme_relationships:
                source_theme = await theme_crud.get_by_name(session, rel["source"])
                target_theme = await theme_crud.get_by_name(session, rel["target"])
                
                if not source_theme or not target_theme:
                    logger.warning(f"Cannot create relationship - missing theme: {rel}")
                    continue
                
                try:
                    await theme_crud.create_relationship(
                        session, 
                        source_type="Theme",
                        source_id=source_theme["id"],
                        theme_id=target_theme["id"],
                        mapping_type="VERIFIED",
                        mapping_strength=rel.get("strength", 0.8),
                        source="system",
                        notes=rel.get("description", None)
                    )
                    logger.debug(f"Created relationship: {rel['source']} -> {rel['type']} -> {rel['target']}")
                except Exception as e:
                    logger.error(f"Error creating relationship: {str(e)}")
        
        logger.info("Theme relationships established")

    def _analyze_genres(self, genres: List[str]) -> List[Dict[str, Any]]:
        """Map genres to themes with confidence scores."""
        theme_mappings = []
        
        # Example genre-to-theme mappings
        genre_theme_map = {
            "Action": [("Epic", 0.8, "primary"), ("Quest", 0.7, "plot")],
            "Romance": [("Self Discovery", 0.7, "primary")],
            "Comedy": [("Lighthearted", 0.9, "mood")],
            "Drama": [("Dark", 0.7, "mood")],
            "Fantasy": [("Epic", 0.8, "mood"), ("Quest", 0.7, "plot")],
            "Slice of Life": [("Coming of Age", 0.8, "primary")],
            "Sports": [("Training Arc", 0.9, "plot"), ("Rivalry", 0.8, "character")],
            "Mystery": [("Mysterious", 0.9, "mood")],
            "Supernatural": [("Mysterious", 0.7, "mood")],
            "Horror": [("Dark", 0.9, "mood")]
        }

        for genre in genres:
            if genre in genre_theme_map:
                for theme_name, confidence, theme_type in genre_theme_map[genre]:
                    theme_mappings.append({
                        "theme_name": theme_name,
                        "confidence": confidence,
                        "type": theme_type,
                        "context": f"Derived from {genre} genre"
                    })

        return theme_mappings

    def _analyze_tags(self, tags: List[Any]) -> List[Dict[str, Any]]:
        """Analyze tags for theme extraction."""
        theme_mappings = []
        
        for tag in tags:
            # Handle both string and dictionary tag formats
            if isinstance(tag, str):
                tag_name = tag.lower()
                category = ""
                confidence = 0.5
                description = None
                is_spoiler = False
            else:
                tag_name = tag.get("name", "").lower()
                category = tag.get("category", "").lower()
                rank = tag.get("rank", 0) or 0
                confidence = min(rank / 100, 0.95) if rank else 0.5
                description = tag.get("description")
                is_spoiler = tag.get("isGeneralSpoiler", False) or tag.get("isMediaSpoiler", False)

            # Skip spoiler tags if they would reveal too much
            if is_spoiler:
                continue

            # Cast and Characters
            if category == "cast" or "character" in category:
                if any(term in tag_name for term in ["ensemble cast", "large cast"]):
                    theme_mappings.append({
                        "theme_name": "Ensemble Cast",
                        "confidence": confidence,
                        "type": "character",
                        "context": f"Based on cast tag: {tag_name}"
                    })
                if "female protagonist" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Strong Female Lead",
                        "confidence": confidence,
                        "type": "character",
                        "context": f"Based on protagonist tag: {tag_name}"
                    })
                if "male protagonist" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Male Lead Focus",
                        "confidence": confidence,
                        "type": "character",
                        "context": f"Based on protagonist tag: {tag_name}"
                    })
                if any(term in tag_name for term in ["anti-hero", "morally grey", "anti-villain"]):
                    theme_mappings.append({
                        "theme_name": "Morally Grey Characters",
                        "confidence": confidence,
                        "type": "character",
                        "context": f"Based on character morality tag: {tag_name}"
                    })

            # Setting and World
            if category == "setting":
                if any(term in tag_name for term in ["school", "academy", "university"]):
                    theme_mappings.append({
                        "theme_name": "School Life",
                        "confidence": confidence,
                        "type": "setting",
                        "context": f"Based on setting tag: {tag_name}"
                    })
                if any(term in tag_name for term in ["fantasy world", "isekai", "alternate world"]):
                    theme_mappings.append({
                        "theme_name": "Fantasy World",
                        "confidence": confidence,
                        "type": "setting",
                        "context": f"Based on setting tag: {tag_name}"
                    })
                if "historical" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Historical",
                        "confidence": confidence,
                        "type": "setting",
                        "context": f"Based on setting tag: {tag_name}"
                    })

            # Plot Elements
            if category == "plot" or "story" in category:
                if "tournament" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Tournament Arc",
                        "confidence": confidence,
                        "type": "plot",
                        "context": f"Based on plot tag: {tag_name}"
                    })
                if any(term in tag_name for term in ["training", "power progression", "leveling"]):
                    theme_mappings.append({
                        "theme_name": "Power Progression",
                        "confidence": confidence,
                        "type": "plot",
                        "context": f"Based on plot tag: {tag_name}"
                    })
                if "revenge" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Revenge",
                        "confidence": confidence,
                        "type": "plot",
                        "context": f"Based on plot tag: {tag_name}"
                    })
                if "mystery" in tag_name:
                    theme_mappings.append({
                        "theme_name": "Mystery",
                        "confidence": confidence,
                        "type": "plot",
                        "context": f"Based on plot tag: {tag_name}"
                    })

            # Narrative Themes
            if any(term in tag_name for term in ["found family", "chosen family"]):
                theme_mappings.append({
                    "theme_name": "Found Family",
                    "confidence": confidence,
                    "type": "primary",
                    "context": f"Based on narrative tag: {tag_name}"
                })
            if "coming of age" in tag_name:
                theme_mappings.append({
                    "theme_name": "Coming of Age",
                    "confidence": confidence,
                    "type": "primary",
                    "context": f"Based on narrative tag: {tag_name}"
                })
            if any(term in tag_name for term in ["friendship", "nakama", "team"]):
                theme_mappings.append({
                    "theme_name": "Power of Friendship",
                    "confidence": confidence,
                    "type": "primary",
                    "context": f"Based on narrative tag: {tag_name}"
                })

            # Mood and Tone
            if any(term in tag_name for term in ["dark", "tragedy", "psychological"]):
                theme_mappings.append({
                    "theme_name": "Dark Themes",
                    "confidence": confidence,
                    "type": "mood",
                    "context": f"Based on mood tag: {tag_name}"
                })
            if any(term in tag_name for term in ["lighthearted", "iyashikei", "cute", "comedy"]):
                theme_mappings.append({
                    "theme_name": "Lighthearted",
                    "confidence": confidence,
                    "type": "mood",
                    "context": f"Based on mood tag: {tag_name}"
                })
            if "emotional" in tag_name:
                theme_mappings.append({
                    "theme_name": "Emotional Impact",
                    "confidence": confidence,
                    "type": "mood",
                    "context": f"Based on mood tag: {tag_name}"
                })

            # Technical Elements
            if category == "technical":
                if "cgi" in tag_name:
                    theme_mappings.append({
                        "theme_name": "CGI Animation",
                        "confidence": confidence,
                        "type": "technical",
                        "context": f"Based on technical tag: {tag_name}"
                    })
                if "beautiful animation" in tag_name or "high quality animation" in tag_name:
                    theme_mappings.append({
                        "theme_name": "High Production Value",
                        "confidence": confidence,
                        "type": "technical",
                        "context": f"Based on technical tag: {tag_name}"
                    })

            # Demographics and Target Audience
            if category == "demographic":
                for demo in ["shounen", "shoujo", "seinen", "josei"]:
                    if demo in tag_name:
                        theme_mappings.append({
                            "theme_name": f"{demo.title()} Demographic",
                            "confidence": confidence,
                            "type": "demographic",
                            "context": f"Based on demographic tag: {tag_name}"
                        })

        return theme_mappings

    async def analyze_story(
        self,
        source_type: str,
        source_id: str,
        genres: List[str],
        tags: List[Dict[str, Any]],
        description: Optional[str] = None
    ) -> Dict:
        """
        Analyze a story to identify themes based on genres and tags.
        
        Args:
            source_type: Type of source (anime, manga, etc.)
            source_id: Unique identifier for the source
            genres: List of genre strings
            tags: List of tag dictionaries
            description: Optional synopsis or description for enhanced keyword analysis
            
        Returns:
            Dictionary with analysis results including themes
        """
        normalized_source_id = source_id.replace('anime_', '') if source_type == 'anime' else source_id
        cache_key = f"theme_analysis:{source_type}:{normalized_source_id}"
        
        # Try getting from Redis cache first
        cached = await theme_redis.get_analysis(source_type, normalized_source_id)
        if cached:
            logger.info(f"Using cached theme analysis for {source_type}:{normalized_source_id}")
            return cached
        
        # Initialize the analyzer
        from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer
        analyzer = TagCombinationAnalyzer()
        
        # Use the enhanced analyzer if description is provided
        if description:
            theme_results = analyzer.analyze_with_description(genres, tags, description)
        else:
            theme_results = analyzer.analyze_combinations(genres, tags)
        
        # Create a formatted result with source info
        analysis = {
            "themes": theme_results,
            "primary_themes": [m for m in theme_results if m["mapping_type"] == "PRIMARY"],
            "secondary_themes": [m for m in theme_results if m["mapping_type"] == "SECONDARY"],
            "mood_themes": [m for m in theme_results if m["mapping_type"] == "MOOD"],
            "character_themes": [m for m in theme_results if m["mapping_type"] == "CHARACTER"],
            "plot_themes": [m for m in theme_results if m["mapping_type"] == "PLOT"],
            "confidence": sum(m["confidence"] for m in theme_results) / len(theme_results) if theme_results else 0.0,
            "last_analyzed": datetime.utcnow().isoformat(),
            "cache_hit": True,
            "metadata": {
                "genres": genres,
                "tags": tags,
                "analyzed_at": datetime.utcnow().isoformat()
            }
        }

        # Transform theme results to match frontend expectations
        for theme in analysis["themes"]:
            # Ensure "id" field exists (frontend looks for this)
            if "theme_id" in theme and "id" not in theme:
                theme["id"] = theme["theme_id"]
            
            # Normalize mapping_type to expected values
            if "mapping_type" in theme:
                # Convert to uppercase for comparison
                mapping_type = theme["mapping_type"].upper()
                
                # Map "tertiary" to appropriate categories based on theme content or confidence
                if mapping_type == "TERTIARY":
                    # Try to categorize based on theme category if available
                    category = theme.get("category", "").upper()
                    if category in ["MOOD", "EMOTIONAL"]:
                        theme["mapping_type"] = "MOOD"
                    elif category in ["CHARACTER", "CHARACTER_DYNAMIC"]:
                        theme["mapping_type"] = "CHARACTER"
                    elif category in ["PLOT", "NARRATIVE", "NARRATIVE_STRUCTURE"]:
                        theme["mapping_type"] = "PLOT"
                    else:
                        # Categorize based on confidence
                        confidence = theme.get("confidence", 0)
                        if confidence > 0.7:
                            theme["mapping_type"] = "PRIMARY"
                        else:
                            theme["mapping_type"] = "SECONDARY"
                elif mapping_type not in ["PRIMARY", "SECONDARY", "MOOD", "CHARACTER", "PLOT"]:
                    # Default categorization for other values
                    confidence = theme.get("confidence", 0)
                    if confidence > 0.7:
                        theme["mapping_type"] = "PRIMARY"
                    else:
                        theme["mapping_type"] = "SECONDARY"
            else:
                # Default to SECONDARY if no mapping_type exists
                theme["mapping_type"] = "SECONDARY"
            
            # Ensure pattern_type is set (frontend looks for this)
            if "pattern_type" not in theme:
                if "mapping_type" in theme:
                    theme["pattern_type"] = theme["mapping_type"]
                else:
                    theme["pattern_type"] = "Analysis"
            
            # Ensure source field exists (frontend looks for this)
            if "source" not in theme:
                sources = []
                if "derived_from" in theme:
                    sources.append(theme["derived_from"])
                if "tag_source" in theme:
                    sources.append(theme["tag_source"])
                if sources:
                    theme["source"] = ", ".join(sources)
                else:
                    theme["source"] = "Combined Analysis"
            
            # Ensure reason field exists (frontend looks for this)
            if "reason" not in theme:
                if "description" in theme:
                    theme["reason"] = theme["description"]
                else:
                    theme["reason"] = f"Determined by analysis engine ({theme.get('category', 'unknown category')})"

        # Recategorize themes after updating mapping_types
        analysis["primary_themes"] = [m for m in analysis["themes"] if m["mapping_type"] == "PRIMARY"]
        analysis["secondary_themes"] = [m for m in analysis["themes"] if m["mapping_type"] == "SECONDARY"]
        analysis["mood_themes"] = [m for m in analysis["themes"] if m["mapping_type"] == "MOOD"]
        analysis["character_themes"] = [m for m in analysis["themes"] if m["mapping_type"] == "CHARACTER"]
        analysis["plot_themes"] = [m for m in analysis["themes"] if m["mapping_type"] == "PLOT"]

        # Store in Redis
        logger.debug(f"Storing analysis results in Redis for {source_type}:{normalized_source_id}")
        await theme_redis.set_analysis(source_type, normalized_source_id, analysis)

        return analysis
        
    async def partial_update_analysis(
        self,
        source_type: str,
        source_id: str,
        updated_themes: List[Dict]
    ) -> Dict:
        """
        Partially update a cached analysis with updated theme data.
        This is more efficient than a full reanalysis when only theme mappings change.
        
        Args:
            source_type: Type of source (story, theme, etc)
            source_id: ID of the source
            updated_themes: List of updated theme mappings
            
        Returns:
            Updated analysis
        """
        # Standardize IDs
        if source_type == "story" and not source_id.startswith("story_"):
            source_id = f"story_{source_id}"
        elif source_type == "theme" and not source_id.startswith("theme_"):
            source_id = f"theme_{source_id}"
        
        # Get existing analysis
        analysis = await theme_redis.get_analysis(source_type, source_id)
        if not analysis:
            logger.warning(f"No cached analysis found for {source_type}:{source_id} during partial update")
            # Create empty analysis structure if none exists
            analysis = {
                "themes": [],
                "primary_themes": [],
                "secondary_themes": [],
                "mood_themes": [],
                "character_themes": [],
                "plot_themes": [],
                "confidence": 0.0,
                "last_analyzed": datetime.utcnow().isoformat(),
                "cache_hit": False,
                "metadata": {
                    "genres": [],
                    "tags": [],
                    "analyzed_at": datetime.utcnow().isoformat()
                }
            }
        
        # Update or add themes
        updated_theme_ids = {t["id"] for t in updated_themes if "id" in t}
        
        # Remove themes that are being updated
        analysis["themes"] = [t for t in analysis.get("themes", []) if t.get("id") not in updated_theme_ids]
        
        # Add updated themes
        analysis["themes"].extend(updated_themes)
        
        # Recalculate grouped themes
        analysis["primary_themes"] = [t for t in analysis["themes"] if t.get("mapping_type") == "PRIMARY"]
        analysis["secondary_themes"] = [t for t in analysis["themes"] if t.get("mapping_type") == "SECONDARY"]
        analysis["mood_themes"] = [t for t in analysis["themes"] if t.get("mapping_type") == "MOOD"]
        analysis["character_themes"] = [t for t in analysis["themes"] if t.get("mapping_type") == "CHARACTER"]
        analysis["plot_themes"] = [t for t in analysis["themes"] if t.get("mapping_type") == "PLOT"]
        
        # Update confidence
        analysis["confidence"] = (
            sum(t.get("confidence", 0.0) for t in analysis["themes"]) / len(analysis["themes"])
            if analysis["themes"] else 0.0
        )
        
        # Update timestamp
        analysis["last_analyzed"] = datetime.utcnow().isoformat()
        
        # Store updated analysis
        await theme_redis.set_analysis(source_type, source_id, analysis)
        
        return analysis

theme_analysis_service = ThemeAnalysisService() 