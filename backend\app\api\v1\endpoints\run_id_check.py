"""
Scan for ID standardization issues in the endpoints directory.
"""
import os
import re
import sys
from typing import List, Dict, Any

from app.services.id_service import IdService

def scan_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Scan a file for potential ID standardization issues.
    
    Returns:
        List of detected issues
    """
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return []
    
    # Patterns to look for
    patterns = [
        # Direct ID assignments without IdService
        (r'id\s*=\s*["\']([^"\']*)["\']', "direct ID assignment"),
        (r'normalized_id\s*=\s*(.+)', "ID normalization"),
        (r'([a-zA-Z]+_id)\s*=\s*["\']([^"\']*)["\']', "direct ID variable assignment"),
        (r'\.startswith\(["\']([a-zA-Z]+_)["\']', "ID prefix check"),
        (r'f"([a-zA-Z]+)_\{(.+)\}"', "f-string ID construction"),
    ]
    
    # Check for each pattern
    for pattern, issue_type in patterns:
        for match in re.finditer(pattern, content):
            line_num = content[:match.start()].count('\n') + 1
            context = content[max(0, match.start() - 40):match.end() + 40]
            
            # Skip if IdService is used in the same context
            if "IdService" in context:
                continue
                
            issues.append({
                "file": file_path,
                "line": line_num,
                "pattern": pattern,
                "match": match.group(0),
                "type": issue_type,
                "context": context.strip()
            })
    
    return issues

def scan_directory(directory: str) -> List[Dict[str, Any]]:
    """
    Scan a directory for ID standardization issues.
    
    Returns:
        List of detected issues
    """
    all_issues = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                file_path = os.path.join(root, file)
                issues = scan_file(file_path)
                all_issues.extend(issues)
    
    return all_issues

def main():
    """Main entry point."""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    issues = scan_directory(current_dir)
    
    if issues:
        print(f"Found {len(issues)} potential ID standardization issues:")
        for i, issue in enumerate(issues):
            print(f"\nIssue #{i+1}:")
            print(f"  File: {issue['file']}")
            print(f"  Line: {issue['line']}")
            print(f"  Type: {issue['type']}")
            print(f"  Match: {issue['match']}")
            print(f"  Context: {issue['context']}")
            print(f"  Suggestion: Use IdService.standardize_id() for consistent ID handling")
    else:
        print("No ID standardization issues found!")

if __name__ == "__main__":
    main() 