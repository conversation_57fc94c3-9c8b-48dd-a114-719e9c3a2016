# Neo4j Testing Strategies

This document outlines the different approaches we use for testing Neo4j database operations throughout the Tahimoto backend codebase.

## Table of Contents

1. [Overview](#overview)
2. [Testing Layers](#testing-layers)
3. [Mocking Neo4j Sessions](#mocking-neo4j-sessions)
4. [Mocking CRUD Operations](#mocking-crud-operations)
5. [Mocking Complex Functions](#mocking-complex-functions)
6. [Testing with Real Neo4j Instances](#testing-with-real-neo4j-instances)
7. [Best Practices](#best-practices)

## Overview

Neo4j is a critical component of our backend infrastructure, handling all graph-based data operations including story storage, theme mapping, and recommendations. Testing these operations requires different approaches depending on the test context:

- For unit and component tests, we need fast, isolated tests with no external dependencies
- For integration tests, we need to verify that our code interacts correctly with Neo4j
- For performance tests, we need to ensure our queries are optimized

This document outlines our strategies for each testing scenario.

## Testing Layers

We use a layered approach to Neo4j testing:

1. **Unit Tests**: Completely mock Neo4j operations
2. **Component Tests**: Mock Neo4j but test component logic
3. **Integration Tests**: 
   - Option A: Use real Neo4j containers (true integration)
   - Option B: Use advanced mocking (faster, more isolated)
4. **Performance Tests**: Always use real Neo4j instances

## Mocking Neo4j Sessions

### The `MockSession` Class

For most tests, we create a `MockSession` class that simulates the behavior of a Neo4j `AsyncSession`:

```python
class MockSession:
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
    
    async def run(self, *args, **kwargs):
        return await mock_run(*args, **kwargs)
```

The `mock_run` function is defined to return appropriate test data:

```python
async def mock_run(*args, **kwargs):
    return None  # Or specific mock data
```

### Patching the Session Factory

In our code, we typically get Neo4j sessions using the `get_db_session` function. We patch this in tests:

```python
with patch('app.api.v1.endpoints.stories.get_db_session', return_value=MockSession()):
    # Test code here
```

## Mocking CRUD Operations

### Mocking `get` Method

For the `CRUDStory.get` method, we create a mock that returns a story with test data:

```python
async def mock_get_story(*args, **kwargs):
    return {
        "id": "story_123456",
        "external_id": "123456",
        "title_english": "Test Story",
        "title_romaji": "Test Story",
        "title_native": "テストストーリー",
        "synopsis": "A story created for testing",
        "media_type": "ANIME",
        "source": "ORIGINAL",
        "cover_image_large": "https://example.com/large.jpg",
        "cover_image_medium": "https://example.com/medium.jpg",
        "banner_image": "https://example.com/banner.jpg",
        "status": "FINISHED",
        "popularity": 1000,
        "average_score": 75.5,
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }
```

### Patching CRUD Methods

We patch the CRUD methods using the `patch` function:

```python
with patch('app.crud.neo4j.story.CRUDStory.get', side_effect=mock_get_story):
    # Test code here
```

## Mocking Complex Functions

For complex functions like `get_neo4j_recommendations`, we create more sophisticated mocks that simulate the behavior of the actual function, including filtering logic:

```python
async def mock_neo4j_recommendations(db, story_id, genres=None, min_score=0, limit=10, status=None):
    # Create base recommendations
    recommendations = [
        {
            "id": "story_rec_1",
            "external_id": "rec_1",
            "title_english": "Recommendation 1",
            "average_score": 70.0,
            "status": "FINISHED",
            "story_metadata": {"genres": ["Action", "Drama"]}
        },
        {
            "id": "story_rec_2",
            "external_id": "rec_2",
            "title_english": "Recommendation 2",
            "average_score": 80.0,
            "status": "AIRING",
            "story_metadata": {"genres": ["Action", "Comedy"]}
        },
        {
            "id": "story_rec_3",
            "external_id": "rec_3",
            "title_english": "Recommendation 3",
            "average_score": 90.0,
            "status": "FINISHED",
            "story_metadata": {"genres": ["Drama", "Romance"]}
        }
    ]
    
    # Apply genre filter if specified
    if genres:
        filtered_recommendations = []
        for rec in recommendations:
            if any(genre in rec["story_metadata"]["genres"] for genre in genres):
                filtered_recommendations.append(rec)
        recommendations = filtered_recommendations
    
    # Apply min_score filter if specified
    if min_score > 0:
        recommendations = [rec for rec in recommendations if rec["average_score"] >= min_score]
    
    # Apply status filter if specified
    if status:
        recommendations = [rec for rec in recommendations if rec["status"] == status]
    
    # Apply limit
    recommendations = recommendations[:limit]
    
    # Return results in the expected format
    return {"recommendations": recommendations, "count": len(recommendations)}
```

We then patch this function where it's used:

```python
with patch('app.api.v1.endpoints.stories.get_neo4j_recommendations', side_effect=mock_neo4j_recommendations):
    # Test code here
```

## Testing with Real Neo4j Instances

For true integration tests, we use actual Neo4j instances running in Docker containers:

1. We set up a Neo4j container in the `conftest.py` file:

```python
@pytest.fixture(scope="session")
def neo4j_container(docker_client, request):
    """Start a Neo4j container for testing."""
    container = docker_client.containers.run(
        "neo4j:5.9.0",
        environment={
            "NEO4J_AUTH": "neo4j/testpassword",
            "NEO4J_ACCEPT_LICENSE_AGREEMENT": "yes"
        },
        ports={"7687/tcp": None},
        detach=True
    )
    
    # Wait for container to be ready
    for _ in range(30):
        try:
            logs = container.logs().decode("utf-8")
            if "Started." in logs:
                break
            time.sleep(1)
        except:
            time.sleep(1)
    
    # Update configuration to use this container
    host = "localhost"
    port = container.ports["7687/tcp"][0]["HostPort"]
    
    # Set up test environment with container details
    os.environ["NEO4J_URI"] = f"neo4j://{host}:{port}"
    os.environ["NEO4J_USERNAME"] = "neo4j"
    os.environ["NEO4J_PASSWORD"] = "testpassword"
    
    yield container
    
    # Clean up
    container.stop()
    container.remove()
```

2. We create a session fixture that connects to this container:

```python
@pytest.fixture(scope="function")
async def neo4j_test_session(neo4j_container=None):
    """Create a Neo4j session for testing."""
    # Set up Neo4j driver for tests
    uri = os.environ.get("NEO4J_URI", "neo4j://localhost:7687")
    username = os.environ.get("NEO4J_USERNAME", "neo4j")
    password = os.environ.get("NEO4J_PASSWORD", "password")
    
    driver = AsyncGraphDatabase.driver(uri, auth=(username, password))
    
    # Create test session
    async with driver.session() as session:
        yield session
    
    # Close driver when done
    await driver.close()
```

3. We use this session in tests that need real Neo4j operations:

```python
async def test_real_neo4j_operations(neo4j_test_session):
    # Create test data
    query = "CREATE (s:Story {id: $id, title: $title}) RETURN s"
    params = {"id": "test_story", "title": "Test Story"}
    result = await neo4j_test_session.run(query, params)
    
    # Test real operations
    # ...
    
    # Clean up
    cleanup_query = "MATCH (s:Story {id: $id}) DETACH DELETE s"
    await neo4j_test_session.run(cleanup_query, {"id": "test_story"})
```

## Best Practices

1. **Choose the Right Approach**: Select the appropriate testing strategy based on the test's purpose:
   - For unit tests, use simple mocks
   - For component tests, use more sophisticated mocks
   - For integration tests, consider real Neo4j instances or advanced mocks
   - For performance tests, always use real Neo4j instances

2. **Clean Up Test Data**: Always clean up test data, especially when using real Neo4j instances:
   ```python
   try:
       # Test code
   finally:
       # Clean up code
   ```

3. **Mock at the Right Level**: Mock at the appropriate level of abstraction:
   - For API tests, mock at the service or CRUD level
   - For service tests, mock at the session level
   - For CRUD tests, mock at the query level

4. **Simulate Realistic Behavior**: When mocking, try to simulate the actual behavior of Neo4j:
   - Include filtering logic
   - Return realistic data structures
   - Simulate error conditions

5. **Use Fixture Factories**: Create fixtures that can generate test data:
   ```python
   @pytest.fixture
   def create_test_story():
       def _create(id="test_story", title="Test Story"):
           return {"id": id, "title": title}
       return _create
   ```

By following these strategies, we can effectively test Neo4j operations throughout our codebase, ensuring both correctness and performance while maintaining test isolation and speed. 