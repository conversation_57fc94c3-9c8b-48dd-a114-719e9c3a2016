"""
GraphQL resolvers for Ariadne.
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from ariadne import QueryType, MutationType, ObjectType, ScalarType
from app.services.mapping import get_theme_mapper
from app.crud.neo4j import theme, story
from uuid import uuid4
import neo4j
from pydantic import ValidationError
from app.core.logging import get_logger
from app.services.theme_analysis import ThemeAnalysisService
from app.services.id_service import IdService

logger = logging.getLogger(__name__)

# Initialize resolver objects
query_resolvers = QueryType()
mutation_resolvers = MutationType()

# Helper functions
def format_theme(theme_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format theme data for GraphQL response."""
    # Ensure category is properly set - if it's missing or empty, use UNCATEGORIZED
    category = theme_data.get("category")
    if not category:
        category = "UNCATEGORIZED"
    
    return {
        "id": theme_data["id"],
        "name": theme_data["name"],
        "description": theme_data.get("description", ""),
        "parentThemeId": theme_data.get("parent_theme_id"),
        "category": category,  # Use the properly handled category
        "subCategory": theme_data.get("sub_category"),
        "dimensions": theme_data.get("dimensions", []),
        "confidence": theme_data.get("confidence", 1.0),
        "createdAt": theme_data.get("created_at", datetime.now().isoformat()),
        "updatedAt": theme_data.get("updated_at", datetime.now().isoformat()),
        "status": theme_data.get("status", "MAPPED"),
        "version": theme_data.get("version"),
        "deprecated": theme_data.get("deprecated", False),
        "replacedBy": theme_data.get("replaced_by"),
        "culturalContext": theme_data.get("cultural_context", []),
        "evolutionStage": theme_data.get("evolution_stage"),
        "implicitTags": theme_data.get("implicit_tags", [])
    }

def format_theme_mapping(mapping_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format theme mapping data for GraphQL response."""
    source = mapping_data["source"]
    theme_data = mapping_data["theme"]
    relationship = mapping_data["relationship"]
    
    return {
        "id": f"{source['id']}_{theme_data['id']}",  # Generate a unique ID for the mapping
        "sourceType": source.get("label", "Unknown"),
        "sourceId": source["id"],
        "themeId": theme_data["id"],
        "theme": format_theme(theme_data),
        "mappingStrength": relationship.get("mapping_strength", 1.0),
        "mappingType": relationship.get("mapping_type", "PRIMARY").upper(),
        "context": relationship.get("notes"),
        "createdAt": relationship.get("created_at", datetime.now().isoformat()),
        "updatedAt": relationship.get("updated_at", datetime.now().isoformat()),
        "llmConfidence": relationship.get("llm_confidence"),
        "needsReview": relationship.get("needs_review", False)
    }

# Query resolvers
@query_resolvers.field("theme")
async def resolve_theme(_, info, id: str):
    """Resolver for theme query."""
    logger.debug(f"Resolving theme with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    theme_data = await theme.get(session, id)
    if not theme_data:
        return None
    
    return format_theme(theme_data)

@query_resolvers.field("themes")
async def resolve_themes(_, info, name=None, category=None, status=None, limit=10, offset=0, 
                        prioritize_connected=True):
    """Resolve themes."""
    logger.debug(f"Resolving themes with limit: {limit}, offset: {offset}")
    try:
        # Get the Neo4j session from the request
        session = info.context["request"].state.db_session
        
        # Prepare query parameters
        params = {"skip": offset, "limit": limit}
        conditions = []
        
        # Add filters if provided
        if name:
            params["name"] = f"(?i).*{name}.*"  # Case-insensitive substring match
            conditions.append("n.name =~ $name")
            
        if category:
            params["category"] = category
            conditions.append("n.category = $category")
            
        if status:
            params["status"] = status
            conditions.append("n.status = $status")
            
        # Build filter string
        filter_string = ""
        if conditions:
            filter_string = "WHERE " + " AND ".join(conditions)
        
        # Execute the query with explicit transaction management
        tx = await session.begin_transaction()
        try:
            # Execute the query with modified ordering to prioritize themes with relationships if requested
            if prioritize_connected:
                query = f"""
                MATCH (n:Theme)
                {filter_string}
                OPTIONAL MATCH (n)-[r]-() 
                WITH n, COUNT(r) as relationshipCount
                RETURN n, relationshipCount
                ORDER BY relationshipCount DESC, n.created_at DESC
                SKIP $skip
                LIMIT $limit
                """
            else:
                query = f"""
                MATCH (n:Theme)
                {filter_string}
                RETURN n
                ORDER BY n.created_at DESC
                SKIP $skip
                LIMIT $limit
                """
            
            logger.debug(f"Executing theme query: {query}")
            logger.debug(f"With parameters: {params}")
            
            result = await tx.run(query, params)
            records = await result.fetch(limit)
            
            logger.debug(f"Query returned {len(records)} records")
            
            # Transform records to expected format
            themes = []
            for i, record in enumerate(records):
                theme_data = dict(record["n"])
                logger.debug(f"Processing theme {i+1}: {theme_data}")
                
                # Ensure ID format with theme_ prefix
                if "id" in theme_data:
                    # Strip any existing theme_ prefix to avoid double prefixing
                    theme_id = theme_data["id"]
                    if theme_id.startswith("theme_"):
                        theme_id = theme_id[6:]  # Remove the "theme_" prefix
                    
                    # Add the theme_ prefix
                    theme_data["id"] = f"theme_{theme_id}"
                    logger.debug(f"Normalized ID: {theme_data['id']}")
                else:
                    logger.warning(f"Theme missing ID: {theme_data}")
                    continue  # Skip themes without ID
                    
                # Check if name is present
                if "name" not in theme_data:
                    logger.warning(f"Theme missing name: {theme_data}")
                    continue  # Skip themes without name
                    
                # Add theme to result list
                formatted_theme = format_theme(theme_data)
                logger.debug(f"Formatted theme: {formatted_theme}")
                themes.append(formatted_theme)
            
            # Commit the transaction
            await tx.commit()
            
            logger.debug(f"Returning {len(themes)} themes")
            return themes
        except Exception as e:
            # Rollback the transaction in case of error
            await tx.rollback()
            logger.error(f"Error resolving themes: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    except Exception as e:
        logger.error(f"Error resolving themes: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

@query_resolvers.field("themeStats")
async def resolve_theme_stats(_, info):
    """Resolver for themeStats query."""
    logger.debug("Resolving theme stats")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operations to get counts
    all_themes = await theme.get_multi(session, limit=1000)  # Get all themes (up to 1000)
    mapped_themes = [t for t in all_themes if t.get("status") == "MAPPED"]
    pending_themes = [t for t in all_themes if t.get("status") == "PENDING"]
    needs_review_themes = [t for t in all_themes if t.get("status") == "NEEDS_REVIEW"]
    
    # TODO: Implement actual cache hit rate calculation
    cache_hit_rate = 0.75  # Placeholder
    
    return {
        "totalThemes": len(all_themes),
        "mappedCount": len(mapped_themes),
        "pendingCount": len(pending_themes),
        "needsReviewCount": len(needs_review_themes),
        "cacheHitRate": cache_hit_rate
    }

@query_resolvers.field("themeMapping")
async def resolve_theme_mapping(_, info, id: str):
    """Resolver for themeMapping query."""
    logger.debug(f"Resolving theme mapping with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return None
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation
    mapping_data = await theme.get_relationship(
        session, 
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id
    )
    
    if not mapping_data:
        return None
    
    return format_theme_mapping(mapping_data)

@query_resolvers.field("themeMappings")
async def resolve_theme_mappings(_, info, sourceType=None, sourceId=None, themeId=None, limit=10, offset=0):
    """Resolver for themeMappings query."""
    logger.debug(f"Resolving theme mappings with filters: sourceType={sourceType}, sourceId={sourceId}, themeId={themeId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operations based on provided filters
    if sourceType and sourceId:
        # Get mappings for a specific source
        analysis = await theme.get_analysis(session, source_type=sourceType, source_id=sourceId)
        mappings = analysis["themes"]
    else:
        # This is a simplified implementation - in a real system, you'd need a more sophisticated query
        # that can handle various filter combinations
        logger.warning("Fetching all theme mappings is not yet implemented")
        mappings = []
    
    # Apply themeId filter if provided
    if themeId and mappings:
        mappings = [m for m in mappings if m["theme"]["id"] == themeId]
    
    # Apply pagination
    paginated_mappings = mappings[offset:offset+limit]
    
    return [format_theme_mapping(m) for m in paginated_mappings]

@query_resolvers.field("themeRelationships")
async def resolve_theme_relationships(_, info, sourceId=None, targetId=None, type=None, limit=100, offset=0):
    """Resolve theme relationships."""
    try:
        # Get the existing session
        session = info.context["request"].state.db_session
        logger.debug(f"Resolving theme relationships with filters: sourceId={sourceId}, targetId={targetId}, type={type}")
        
        # Prepare query parameters
        params = {"skip": offset, "limit": limit}
        conditions = []
        
        # Add filters if provided
        if sourceId:
            # Standardize ID format
            sourceId = IdService.standardize_id(sourceId, "theme")
            
            # Check if it's a UUID or semantic ID
            if IdService.is_uuid(sourceId):
                # This is a UUID - direct lookup
                db_source_id = IdService.to_database_id(sourceId, "theme")
                params["sourceId"] = db_source_id
                conditions.append("source.id = $sourceId")
            else:
                # This is a semantic ID - lookup by name
                name_lookup = IdService.find_by_id_or_name(sourceId, "theme")
                params[name_lookup["param_name"]] = name_lookup["param_value"]
                conditions.append(f"source.{name_lookup['condition'].split('.')[1]}")
            
        if targetId:
            # Standardize ID format
            targetId = IdService.standardize_id(targetId, "theme")
            
            # Check if it's a UUID or semantic ID
            if IdService.is_uuid(targetId):
                # This is a UUID - direct lookup
                db_target_id = IdService.to_database_id(targetId, "theme")
                params["targetId"] = db_target_id
                conditions.append("target.id = $targetId")
            else:
                # This is a semantic ID - lookup by name
                name_lookup = IdService.find_by_id_or_name(targetId, "theme")
                # Use a different parameter name to avoid conflicts
                target_param_name = f"target{name_lookup['param_name'].capitalize()}"
                params[target_param_name] = name_lookup["param_value"]
                target_condition = name_lookup["condition"].replace(
                    f"${name_lookup['param_name']}", f"${target_param_name}"
                ).replace("n.", "target.")
                conditions.append(target_condition)
            
        if type:
            params["relType"] = type
            conditions.append("type(r) = $relType")
            
        # Build filter string
        filter_string = ""
        if conditions:
            filter_string = "WHERE " + " AND ".join(conditions)
        
        # Build the query
        query = f"""
        MATCH (source:Theme)-[r]->(target:Theme)
        {filter_string}
        RETURN source, target, type(r) as relType, properties(r) as relProps
        ORDER BY source.name, target.name
        SKIP $skip LIMIT $limit
        """
        
        logger.debug(f"Executing theme relationship query:\n{query}")
        logger.debug(f"With parameters: {params}")
        
        # Import the driver directly from the module
        from app.db.neo4j_session import driver
        
        # Create a new session for this query to avoid transaction conflicts
        async with driver.session(database="neo4j", fetch_size=1000) as new_session:
            # Execute the query in a new session
            result = await new_session.run(query, params)
            records = await result.fetch(limit)
            
            logger.debug(f"Query returned {len(records)} records")
            
            # Transform records to expected format
            relationships = []
            for record in records:
                source_data = dict(record["source"])
                target_data = dict(record["target"])
                rel_type = record["relType"]
                rel_props = record["relProps"]
                
                # Ensure IDs have theme_ prefix
                if "id" in source_data:
                    source_id = source_data["id"]
                    if not source_id.startswith("theme_"):
                        source_data["id"] = f"theme_{source_id}"
                        
                if "id" in target_data:
                    target_id = target_data["id"]
                    if not target_id.startswith("theme_"):
                        target_data["id"] = f"theme_{target_id}"
                
                # Create relationship object
                relationship = {
                    "id": rel_props.get("id", f"{source_data['id']}-{rel_type}-{target_data['id']}"),
                    "sourceId": source_data["id"],
                    "sourceName": source_data["name"],
                    "targetId": target_data["id"],
                    "targetName": target_data["name"],
                    "type": rel_type,
                    "strength": rel_props.get("strength", 1.0),
                    "tension": rel_props.get("tension", 0.0),
                    "description": rel_props.get("description", ""),
                    "dimensions": rel_props.get("dimensions", []),
                    "culturalContext": rel_props.get("cultural_context", []),
                    "version": rel_props.get("version"),
                    "evidence": rel_props.get("evidence", ""),
                    "confidence": rel_props.get("confidence"),
                    "createdAt": rel_props.get("created_at", datetime.now().isoformat()),
                    "updatedAt": rel_props.get("updated_at", datetime.now().isoformat())
                }
                
                relationships.append(relationship)
            
            logger.debug(f"Found {len(relationships)} theme relationships")
            return relationships
    except Exception as e:
        logger.error(f"Error resolving theme relationships: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

@query_resolvers.field("analyzeMedia")
async def resolve_analyze_media(_, info, sourceType: str, sourceId: str):
    """
    Resolver for analyzeMedia query - uses ThemeMapper interface and Neo4j CRUD operations.
    """
    logger.debug(f"Analyzing media: {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # First, check if we already have analysis in the database
    analysis = await theme.get_analysis(session, source_type=sourceType, source_id=sourceId)
    
    # If we have existing analysis with themes, return it
    if analysis and analysis["themes"]:
        logger.debug(f"Found existing theme analysis for {sourceType}/{sourceId}")
        
        # Format the response
        return {
            "themes": [format_theme_mapping(m) for m in analysis["themes"]],
            "primaryThemes": [format_theme_mapping(m) for m in analysis["primary_themes"]],
            "secondaryThemes": [format_theme_mapping(m) for m in analysis["secondary_themes"]],
            "moodThemes": [format_theme_mapping(m) for m in analysis["mood_themes"]],
            "characterThemes": [format_theme_mapping(m) for m in analysis["character_themes"]],
            "plotThemes": [format_theme_mapping(m) for m in analysis["plot_themes"]],
            "confidence": 0.85,  # TODO: Calculate actual confidence
            "lastAnalyzed": datetime.now().isoformat()  # TODO: Get actual timestamp
        }
    
    # If no existing analysis, we need to fetch the media data and analyze it
    # For now, we'll use a simplified approach with mock data
    # In a real implementation, you'd fetch actual media data from your database or external API
    
    # Get the story data
    story_data = None
    if sourceType.lower() == "anime":
        story_data = await story.get_by_external_id(session, external_id=sourceId, source="anilist")
    
    if not story_data:
        # Mock data for demonstration
        mock_media_info = {
            "title": f"Sample {sourceType}",
            "genres": ["Action", "Fantasy", "Adventure"],
            "tags": ["Time Travel", "Magic"]
        }
    else:
        # Use actual story data
        mock_media_info = {
            "title": story_data.get("title", f"Unknown {sourceType}"),
            "genres": story_data.get("genres", []),
            "tags": story_data.get("tags", [])
        }
    
    # Use the ThemeMapper interface
    mapper = get_theme_mapper(media_type=sourceType)
    theme_mappings = mapper.get_full_theme_mapping(mock_media_info)
    
    # Store the mappings in Neo4j
    stored_mappings = []
    for mapping in theme_mappings:
        # Check if the theme exists, create it if not
        theme_id = mapping["theme_id"]
        theme_data = await theme.get_by_name(session, name=theme_id.replace("_", " ").title())
        
        if not theme_data:
            # Create the theme
            theme_data = await theme.create(session, obj_in={
                "name": theme_id.replace("_", " ").title(),
                "description": f"Description for {theme_id}",
                "status": "MAPPED"
            })
        
        # Create the theme mapping relationship
        mapping_data = await theme.create_relationship(
            session,
            source_type=sourceType,
            source_id=sourceId,
            theme_id=theme_data["id"],
            mapping_type=mapping["mapping_type"],
            mapping_strength=mapping["confidence"],
            source="manual"  # This would be "agent" if using LLM
        )
        
        stored_mappings.append(mapping_data)
    
    # Get the complete analysis after creating all mappings
    updated_analysis = await theme.get_analysis(session, source_type=sourceType, source_id=sourceId)
    
    # Format the response
    return {
        "themes": [format_theme_mapping(m) for m in updated_analysis["themes"]],
        "primaryThemes": [format_theme_mapping(m) for m in updated_analysis["primary_themes"]],
        "secondaryThemes": [format_theme_mapping(m) for m in updated_analysis["secondary_themes"]],
        "moodThemes": [format_theme_mapping(m) for m in updated_analysis["mood_themes"]],
        "characterThemes": [format_theme_mapping(m) for m in updated_analysis["character_themes"]],
        "plotThemes": [format_theme_mapping(m) for m in updated_analysis["plot_themes"]],
        "confidence": 0.85,  # TODO: Calculate actual confidence
        "lastAnalyzed": datetime.now().isoformat()
    }

@query_resolvers.field("anime")
async def resolve_anime(_, info, id: str):
    """Resolver for anime query."""
    logger.debug(f"Resolving anime with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    anime_data = await story.get(session, id)
    
    if not anime_data:
        # Try to get by external ID
        anime_data = await story.get_by_external_id(session, external_id=id, source="anilist")
        
    if not anime_data:
        return None
    
    return {
        "id": anime_data["id"],
        "sourceId": anime_data.get("external_id", f"anilist_{id}"),
        "title": anime_data.get("title", "Unknown Anime"),
        "alternativeTitles": anime_data.get("alternative_titles", []),
        "description": anime_data.get("description", ""),
        "genres": anime_data.get("genres", []),
        "tags": anime_data.get("tags", []),
        "themeAnalysis": None  # Will be resolved by analyzeMedia
    }

@query_resolvers.field("similarAnime")
async def resolve_similar_anime(_, info, id: str, limit: int = 5):
    """Resolver for similarAnime query."""
    logger.debug(f"Resolving similar anime for ID: {id} with limit: {limit}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # First get the anime to ensure it exists
    anime_data = await story.get(session, id)
    
    if not anime_data:
        # Try to get by external ID
        anime_data = await story.get_by_external_id(session, external_id=id, source="anilist")
        
    if not anime_data:
        return []
    
    # Use Neo4j CRUD operation to find similar anime
    similar_anime = await theme.find_similar_media(
        session,
        source_type="Anime",
        source_id=anime_data["id"],
        target_types=["Anime"],
        limit=limit
    )
    
    # Format the response
    result = []
    for anime in similar_anime:
        result.append({
            "id": anime["id"],
            "sourceId": anime.get("external_id", f"anilist_{anime['id']}"),
            "title": anime.get("title", "Unknown Anime"),
            "alternativeTitles": anime.get("alternative_titles", []),
            "description": anime.get("description", ""),
            "genres": anime.get("genres", []),
            "tags": anime.get("tags", []),
            "themeAnalysis": None  # Will be resolved by analyzeMedia if needed
        })
    
    return result

@query_resolvers.field("recommendCrossmedia")
async def resolve_recommend_crossmedia(_, info, sourceType: str, sourceId: str, targetTypes=None, limit: int = 5):
    """Resolver for recommendCrossmedia query."""
    logger.debug(f"Resolving cross-media recommendations for {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # First get the source media to ensure it exists
    source_data = await story.get(session, sourceId)
    
    if not source_data:
        # Try to get by external ID
        source_data = await story.get_by_external_id(session, external_id=sourceId, source="anilist")
        
    if not source_data:
        return []
    
    # Use Neo4j CRUD operation to find similar media across different types
    similar_media = await theme.find_similar_media(
        session,
        source_type=sourceType,
        source_id=source_data["id"],
        target_types=targetTypes,
        limit=limit
    )
    
    # For each similar media, get its theme mappings
    result = []
    for media in similar_media:
        # Get the media type from Neo4j node label
        media_type = media.get("label", "Unknown")
        
        # Get theme mappings for this media
        media_analysis = await theme.get_analysis(session, source_type=media_type, source_id=media["id"])
        
        # Add the strongest theme mappings to the result
        if media_analysis and media_analysis["themes"]:
            # Sort by mapping strength
            sorted_mappings = sorted(
                media_analysis["themes"], 
                key=lambda m: m["relationship"].get("mapping_strength", 0),
                reverse=True
            )
            
            # Take the top mappings up to the limit
            top_mappings = sorted_mappings[:limit]
            
            # Format and add to result
            for mapping in top_mappings:
                result.append(format_theme_mapping(mapping))
    
    return result

@query_resolvers.field("analyzeStoryThemes")
async def resolve_analyze_story_themes(_, info, sourceType: str, sourceId: str, genres: List[str], tags: List[Dict[str, Any]], description: Optional[str] = None):
    """
    Resolver for analyzeStoryThemes query - uses ThemeAnalysisService to analyze story themes.
    """
    logger.debug(f"Analyzing themes for story: {sourceType}/{sourceId}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Create analysis service
    theme_analysis_service = ThemeAnalysisService()
    
    # Call the analyze_story method
    analysis_result = await theme_analysis_service.analyze_story(
        source_type=sourceType,
        source_id=sourceId,
        genres=genres,
        tags=tags,
        description=description
    )
    
    # Transform the result to match the GraphQL schema
    themes = analysis_result["themes"]
    
    # Calculate statistics
    total_matches = len(themes)
    confidence_avg = analysis_result.get("confidence", 0.0)
    category_counts = {}
    for theme in themes:
        category = theme.get("category", "UNCATEGORIZED")
        if category in category_counts:
            category_counts[category] += 1
        else:
            category_counts[category] = 1
    
    # Get top 3 categories by count
    top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:3]
    top_category_names = [cat[0] for cat in top_categories]
    
    result = {
        "themes": themes,
        "stats": {
            "total_matches": total_matches,
            "confidence_avg": confidence_avg,
            "top_categories": top_category_names
        }
    }
    
    return result

# Mutation resolvers
@mutation_resolvers.field("createTheme")
async def resolve_create_theme(_, info, input):
    """Resolver for createTheme mutation."""
    logger.debug(f"Creating theme: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    try:
        # Convert input to ThemeCreate schema
        from app.schemas.theme import ThemeCreate
        
        # Create theme data with ID
        theme_create = ThemeCreate(
            name=input["name"],
            description=input.get("description", ""),
            parent_theme_id=input.get("parentThemeId")
        )
        
        # Create theme
        theme_data = await theme.create(session, obj_in=theme_create)
        logger.debug(f"Created theme: {theme_data}")
        
        # Ensure ID is present
        if not theme_data.get("id"):
            logger.error("Theme created without ID")
            theme_data["id"] = str(uuid4())
            logger.debug(f"Generated new ID: {theme_data['id']}")
        
        # Format and return theme data
        formatted_theme = format_theme(theme_data)
        logger.debug(f"Formatted theme: {formatted_theme}")
        return formatted_theme
    except Exception as e:
        logger.error(f"Error creating theme: {str(e)}")
        raise

@mutation_resolvers.field("updateTheme")
async def resolve_update_theme(_, info, id, input):
    """Resolver for updateTheme mutation."""
    logger.debug(f"Updating theme {id}: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    updated_theme = await theme.update(session, id=id, obj_in=input)
    
    if not updated_theme:
        raise Exception(f"Theme with ID {id} not found")
    
    return format_theme(updated_theme)

@mutation_resolvers.field("deleteTheme")
async def resolve_delete_theme(_, info, id):
    """Resolver for deleteTheme mutation."""
    logger.debug(f"Deleting theme: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # First check if the theme exists
    theme_data = await theme.get(session, id)
    
    if not theme_data:
        return False
    
    # Use Neo4j CRUD operation
    await theme.remove(session, id=id)
    
    return True

@mutation_resolvers.field("createThemeMapping")
async def resolve_create_theme_mapping(_, info, input):
    """Resolver for createThemeMapping mutation."""
    logger.debug(f"Creating theme mapping: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Extract input values
    source_type = input["sourceType"]
    source_id = input["sourceId"]
    theme_id = input["themeId"]
    mapping_type = input["mappingType"].lower()
    mapping_strength = input.get("mappingStrength", 1.0)
    context = input.get("context")
    
    # Use Neo4j CRUD operation
    mapping_data = await theme.create_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        mapping_type=mapping_type,
        mapping_strength=mapping_strength,
        source="manual",
        notes=context
    )
    
    return format_theme_mapping(mapping_data)

@mutation_resolvers.field("updateThemeMapping")
async def resolve_update_theme_mapping(_, info, id, input):
    """Resolver for updateThemeMapping mutation."""
    logger.debug(f"Updating theme mapping {id}: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        raise Exception(f"Invalid theme mapping ID format: {id}")
    
    # Extract input values
    source_type = input["sourceType"]
    mapping_type = input["mappingType"].lower()
    mapping_strength = input.get("mappingStrength")
    context = input.get("context")
    
    # Use Neo4j CRUD operation
    updated_mapping = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        mapping_type=mapping_type,
        mapping_strength=mapping_strength,
        notes=context
    )
    
    if not updated_mapping:
        raise Exception(f"Theme mapping with ID {id} not found")
    
    return format_theme_mapping(updated_mapping)

@mutation_resolvers.field("deleteThemeMapping")
async def resolve_delete_theme_mapping(_, info, id):
    """Resolver for deleteThemeMapping mutation."""
    logger.debug(f"Deleting theme mapping: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return False
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation
    result = await theme.remove_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id
    )
    
    return result

@mutation_resolvers.field("approveThemeMapping")
async def resolve_approve_theme_mapping(_, info, id):
    """Resolver for approveThemeMapping mutation."""
    logger.debug(f"Approving theme mapping: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        raise Exception(f"Invalid theme mapping ID format: {id}")
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation to update the relationship
    updated_mapping = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        source="manual",  # Mark as manually approved
        notes="Approved by admin"
    )
    
    if not updated_mapping:
        raise Exception(f"Theme mapping with ID {id} not found")
    
    return format_theme_mapping(updated_mapping)

@mutation_resolvers.field("rejectThemeMapping")
async def resolve_reject_theme_mapping(_, info, id):
    """Resolver for rejectThemeMapping mutation."""
    logger.debug(f"Rejecting theme mapping: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        return False
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation to remove the relationship
    result = await theme.remove_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id
    )
    
    return result

@mutation_resolvers.field("requestReview")
async def resolve_request_review(_, info, id):
    """Resolver for requestReview mutation."""
    logger.debug(f"Requesting review for theme mapping: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Parse the composite ID to get source and theme IDs
    try:
        source_id, theme_id = id.split("_", 1)
    except ValueError:
        logger.error(f"Invalid theme mapping ID format: {id}")
        raise Exception(f"Invalid theme mapping ID format: {id}")
    
    # Determine source type (this is a simplification - in reality, you'd need to check multiple types)
    source_type = "Story"  # Default assumption
    
    # Use Neo4j CRUD operation to update the relationship
    updated_mapping = await theme.update_relationship(
        session,
        source_type=source_type,
        source_id=source_id,
        theme_id=theme_id,
        notes="Needs review"
    )
    
    if not updated_mapping:
        raise Exception(f"Theme mapping with ID {id} not found")
    
    return format_theme_mapping(updated_mapping)

@mutation_resolvers.field("createThemeRelationship")
async def resolve_create_theme_relationship(_, info, input):
    """Resolver for createThemeRelationship mutation."""
    logger.debug(f"Creating theme relationship: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Extract input values
    source_id = input["sourceId"]
    target_id = input["targetId"]
    relationship_type = input["type"]
    
    # Prepare properties dict
    properties = {
        "strength": input.get("strength", 0.5),
        "tension": input.get("tension", 0.0),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Add optional properties if provided
    if "description" in input:
        properties["description"] = input["description"]
    if "dimensions" in input:
        properties["dimensions"] = input["dimensions"]
    if "culturalContext" in input:
        properties["cultural_context"] = input["culturalContext"]
    
    # Use Neo4j CRUD operation to create the relationship
    try:
        relationship_data = await theme.create_theme_relationship(
            session,
            source_theme_id=source_id,
            target_theme_id=target_id,
            relationship_type=relationship_type,
            properties=properties
        )
        
        # Format the result for GraphQL
        source = relationship_data["source"]
        target = relationship_data["target"]
        rel = relationship_data["relationship"]
        
        # Create the response object
        result = {
            "id": f"{source['id']}-{relationship_type}-{target['id']}",
            "sourceId": source["id"],
            "sourceName": source["name"],
            "targetId": target["id"],
            "targetName": target["name"],
            "type": relationship_type,
            "strength": rel.get("strength", 0.5),
            "tension": rel.get("tension", 0.0),
            "description": rel.get("description", ""),
            "dimensions": rel.get("dimensions", []),
            "culturalContext": rel.get("cultural_context", []),
            "version": rel.get("version"),
            "evidence": rel.get("evidence", ""),
            "confidence": rel.get("confidence"),
            "createdAt": rel.get("created_at", datetime.now().isoformat()),
            "updatedAt": rel.get("updated_at", datetime.now().isoformat())
        }
        
        return result
    except Exception as e:
        logger.error(f"Error creating theme relationship: {str(e)}")
        raise

@mutation_resolvers.field("updateThemeRelationship")
async def resolve_update_theme_relationship(_, info, id, input):
    """Resolver for updateThemeRelationship mutation."""
    logger.debug(f"Updating theme relationship: {id} with {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    try:
        # Parse the composite ID to get source, type and target
        try:
            source_id, rel_type, target_id = id.split("-", 2)
        except ValueError:
            raise Exception(f"Invalid relationship ID format: {id}")
        
        # If sourceId and targetId are provided, they must match the ID components
        if "sourceId" in input and input["sourceId"] != source_id:
            raise Exception("SourceId does not match the relationship ID")
        if "targetId" in input and input["targetId"] != target_id:
            raise Exception("TargetId does not match the relationship ID")
        
        # Create a new relationship with the updated properties if type has changed
        if "type" in input and input["type"] != rel_type:
            # First, delete the old relationship
            await session.run(
                f"""
                MATCH (source:Theme {{id: $source_id}})-[r:{rel_type}]->(target:Theme {{id: $target_id}})
                DELETE r
                """,
                {"source_id": source_id, "target_id": target_id}
            )
            
            # Then create the new relationship
            new_rel_type = input["type"]
            
            # Prepare properties for the new relationship
            properties = {
                "strength": input.get("strength", 0.5),
                "tension": input.get("tension", 0.0),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # Add optional properties if provided
            if "description" in input:
                properties["description"] = input["description"]
            if "dimensions" in input:
                properties["dimensions"] = input["dimensions"]
            if "culturalContext" in input:
                properties["cultural_context"] = input["culturalContext"]
            if "evidence" in input:
                properties["evidence"] = input["evidence"]
            if "confidence" in input:
                properties["confidence"] = input["confidence"]
            
            # Create the new relationship
            relationship_data = await theme.create_theme_relationship(
                session,
                source_theme_id=source_id,
                target_theme_id=target_id,
                relationship_type=new_rel_type,
                properties=properties
            )
            
            # Format the result
            source = relationship_data["source"]
            target = relationship_data["target"]
            rel = relationship_data["relationship"]
            
            # Create the response object
            result = {
                "id": f"{source['id']}-{new_rel_type}-{target['id']}",
                "sourceId": source["id"],
                "sourceName": source["name"],
                "targetId": target["id"],
                "targetName": target["name"],
                "type": new_rel_type,
                "strength": rel.get("strength", 0.5),
                "tension": rel.get("tension", 0.0),
                "description": rel.get("description", ""),
                "dimensions": rel.get("dimensions", []),
                "culturalContext": rel.get("cultural_context", []),
                "version": rel.get("version"),
                "evidence": rel.get("evidence", ""),
                "confidence": rel.get("confidence"),
                "createdAt": rel.get("created_at", datetime.now().isoformat()),
                "updatedAt": rel.get("updated_at", datetime.now().isoformat())
            }
            
            return result
        else:
            # Just update the properties of the existing relationship
            props = []
            params = {
                "source_id": source_id,
                "target_id": target_id
            }
            
            # Add parameters for properties to update
            if "strength" in input:
                props.append("r.strength = $strength")
                params["strength"] = input["strength"]
            if "tension" in input:
                props.append("r.tension = $tension")
                params["tension"] = input["tension"]
            if "description" in input:
                props.append("r.description = $description")
                params["description"] = input["description"]
            if "dimensions" in input:
                props.append("r.dimensions = $dimensions")
                params["dimensions"] = input["dimensions"]
            if "culturalContext" in input:
                props.append("r.cultural_context = $cultural_context")
                params["cultural_context"] = input["culturalContext"]
            if "evidence" in input:
                props.append("r.evidence = $evidence")
                params["evidence"] = input["evidence"]
            if "confidence" in input:
                props.append("r.confidence = $confidence")
                params["confidence"] = input["confidence"]
            
            # Always update the updated_at timestamp
            props.append("r.updated_at = $updated_at")
            params["updated_at"] = datetime.now().isoformat()
            
            # Build the SET clause
            set_clause = ", ".join(props)
            
            # Update the relationship
            query = f"""
            MATCH (source:Theme {{id: $source_id}})-[r:{rel_type}]->(target:Theme {{id: $target_id}})
            SET {set_clause}
            RETURN source, target, r
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                raise Exception(f"Relationship not found: {id}")
            
            # Format the result
            source = dict(record["source"])
            target = dict(record["target"])
            rel = dict(record["r"])
            
            # Create the response object
            result = {
                "id": f"{source['id']}-{rel_type}-{target['id']}",
                "sourceId": source["id"],
                "sourceName": source["name"],
                "targetId": target["id"],
                "targetName": target["name"],
                "type": rel_type,
                "strength": rel.get("strength", 0.5),
                "tension": rel.get("tension", 0.0),
                "description": rel.get("description", ""),
                "dimensions": rel.get("dimensions", []),
                "culturalContext": rel.get("cultural_context", []),
                "version": rel.get("version"),
                "evidence": rel.get("evidence", ""),
                "confidence": rel.get("confidence"),
                "createdAt": rel.get("created_at", datetime.now().isoformat()),
                "updatedAt": params["updated_at"]
            }
            
            return result
            
    except Exception as e:
        logger.error(f"Error updating theme relationship: {str(e)}")
        raise

@mutation_resolvers.field("deleteThemeRelationship")
async def resolve_delete_theme_relationship(_, info, id):
    """Resolver for deleteThemeRelationship mutation."""
    logger.debug(f"Deleting theme relationship: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    try:
        # Parse the composite ID to get source, type and target
        try:
            source_id, rel_type, target_id = id.split("-", 2)
        except ValueError:
            logger.error(f"Invalid relationship ID format: {id}")
            return False
        
        # Delete the relationship
        query = f"""
        MATCH (source:Theme {{id: $source_id}})-[r:{rel_type}]->(target:Theme {{id: $target_id}})
        DELETE r
        RETURN count(r) as deleted
        """
        
        result = await session.run(query, {"source_id": source_id, "target_id": target_id})
        record = await result.single()
        
        # Check if anything was deleted
        return record and record["deleted"] > 0
        
    except Exception as e:
        logger.error(f"Error deleting theme relationship: {str(e)}")
        return False

# Future improvements to address after legacy code cleanup is complete
"""
RESOLVERS IMPROVEMENT PLAN

The following areas should be improved after the legacy code cleanup is complete:

1. TODO Markers:
   - Implement actual cache hit rate calculation in resolve_theme_stats
   - Calculate proper confidence scores in analyzeMedia instead of hardcoded 0.85
   - Use actual timestamps for lastAnalyzed instead of current time

2. Source Type Assumptions:
   - Replace hardcoded "Story" default assumptions with proper detection
   - Create a helper function to determine source type from ID or data
   - Add validation for supported source types

3. Incomplete Implementations:
   - Complete the themeMappings resolver to handle fetching all mappings with various filter combinations
   - Consider adding pagination metadata (total count, pages, etc.)

4. Mock Data Handling:
   - Replace mock data in analyzeMedia with proper error responses
   - Add clear user feedback when requested data is not found
   - Consider implementing a stub service for development/testing

5. ID Standardization:
   - Create utility functions for ID validation and creation
   - Ensure consistent ID format handling across all resolvers
   - Follow the ID prefix standards defined in documentation (story_, theme_, mapping_)

6. Error Handling Consistency:
   - Standardize error responses (None vs. Exception)
   - Add more specific error types and messages
   - Ensure proper error propagation to GraphQL clients

7. Performance Optimization:
   - Add proper caching for expensive operations
   - Optimize Neo4j queries for theme analysis and recommendations
   - Consider batch loading related entities

8. Input Validation:
   - Add more robust validation for mutation inputs
   - Validate IDs before performing database operations
   - Handle edge cases like empty arrays or invalid enums

9. Resolver Organization:
   - Consider splitting resolvers into separate files by domain (theme, story, etc.)
   - Use context objects to share common functionality
   - Add middleware for cross-cutting concerns (logging, permissions, etc.)
"""