from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from pydantic import BaseModel, Field, constr

# Type alias for ID fields
StoryId = constr(strip_whitespace=True, min_length=1)

class StoryTag(BaseModel):
    """Tag metadata schema."""
    name: str
    description: Optional[str] = None
    category: str = "Other"
    rank: Optional[int] = None
    isGeneralSpoiler: bool = False
    isMediaSpoiler: bool = False

class StoryStudio(BaseModel):
    """Studio metadata schema."""
    id: Optional[int] = None
    name: str

class RelationTag(BaseModel):
    """Simplified tag schema for relations."""
    name: str
    rank: Optional[int] = None
    category: str = "Other"

class StoryRelation(BaseModel):
    """Model for a relation between stories."""
    id: StoryId
    relation_id: Optional[StoryId] = None
    title: str
    type: str
    media_type: str
    format: str
    status: str
    source: str
    average_score: Optional[float] = None
    popularity: Optional[int] = None
    cover_image: Optional[str] = None
    cover_image_large: Optional[str] = None
    description: Optional[str] = None
    native_title: Optional[str] = None
    genres: List[str] = Field(default_factory=list)
    tags: List[RelationTag] = Field(default_factory=list)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_main_studio: bool = False
    recommendation_strength: Optional[int] = None
    recommended_by: Optional[str] = None
    user_rating: Optional[int] = None

class StoryMetadata(BaseModel):
    """Story metadata schema."""
    genres: List[str] = Field(default_factory=list)
    tags: List[StoryTag] = Field(default_factory=list)
    studios: List[StoryStudio] = Field(default_factory=list)
    relations: List[StoryRelation] = Field(default_factory=list)

class StoryBase(BaseModel):
    """Base story schema."""
    title_english: Optional[str] = None
    title_romaji: str
    title_native: Optional[str] = None
    synopsis: Optional[str] = None
    media_type: str
    source: Optional[str] = "anilist"
    cover_image_large: Optional[str] = None
    cover_image_medium: Optional[str] = None
    banner_image: Optional[str] = None
    status: Optional[str] = None
    popularity: Optional[int] = None
    average_score: Optional[float] = None
    episode_count: Optional[int] = None
    episode_duration: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    season: Optional[str] = None
    story_metadata: Union[Dict, StoryMetadata] = Field(
        default_factory=lambda: {
            "genres": [],
            "tags": [],
            "studios": [],
            "relations": []
        }
    )

class StoryCreate(StoryBase):
    """Story creation schema."""
    external_id: StoryId

class StoryUpdate(StoryBase):
    """Schema for updating an existing story."""
    pass

class Story(StoryCreate):
    """Story schema with database fields."""
    id: StoryId
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic config."""
        from_attributes = True

class StorySearchResult(BaseModel):
    """Individual story search result schema."""
    id: StoryId
    external_id: StoryId
    title_english: Optional[str] = None
    title_romaji: str
    title_native: Optional[str] = None
    synopsis: Optional[str] = None
    media_type: str
    cover_image_medium: Optional[str] = None
    cover_image_large: Optional[str] = None
    banner_image: Optional[str] = None
    status: Optional[str] = None
    popularity: Optional[int] = None
    average_score: Optional[float] = None
    genres: List[str] = Field(default_factory=list)

    class Config:
        """Pydantic config."""
        from_attributes = True

class StorySearchInput(BaseModel):
    """Story search input parameters schema."""
    query: str
    page: int = Field(1, ge=1, description="Page number for pagination")
    per_page: int = Field(10, ge=1, le=50, description="Number of results per page")
    mediaType: Optional[str] = Field("all", description="Media type filter")
    sort: Optional[str] = Field("popularity", description="Sort order for results")

class StorySearch(BaseModel):
    """Story search results schema."""
    total: int
    items: List[StorySearchResult]

# Alias for backward compatibility
StorySearchResults = StorySearch

class StoryRecommendation(BaseModel):
    """Individual story recommendation schema."""
    id: StoryId
    external_id: StoryId
    title_english: Optional[str] = None
    title_romaji: str
    title_native: Optional[str] = None
    synopsis: Optional[str] = None
    media_type: str
    cover_image_medium: Optional[str] = None
    cover_image_large: Optional[str] = None
    banner_image: Optional[str] = None
    status: Optional[str] = None
    popularity: Optional[int] = None
    average_score: Optional[float] = None
    genres: List[str] = Field(default_factory=list)
    recommendation_strength: Optional[int] = None
    recommended_by: Optional[str] = None

    class Config:
        """Pydantic config."""
        from_attributes = True

class RecommendationFilters(BaseModel):
    """Filters for recommendations."""
    genres: Optional[List[str]] = None
    min_score: Optional[float] = Field(None, ge=0, le=100)
    status: Optional[str] = None
    limit: Optional[int] = Field(10, ge=1, le=25)

class RecommendationResponse(BaseModel):
    """Response model for recommendations endpoint."""
    recommendations: List[Dict[str, Any]] = Field(default_factory=list)
    count: int
    source: str = "none"  # 'neo4j', 'anilist', 'none', 'error'
    filters_applied: Optional[RecommendationFilters] = None
    error: Optional[str] = None