"""
Theme Relationship Service for managing relationships between themes.

This service abstracts and encapsulates the operations related to theme relationships
including creation, updating, deletion, and complex querying of relationships.
It provides a clean interface for resolver functions to work with Neo4j operations
without needing to directly construct Cypher queries.
"""
from typing import Dict, List, Any, Optional, Tuple, NamedTuple
import logging
from uuid import uuid4
from datetime import datetime
import neo4j
from neo4j import AsyncSession

from app.crud.neo4j import theme
from app.core.logging import get_logger
from app.graphql.resolvers.utils.id_standardization import (
    ensure_theme_id_prefix,
    remove_theme_id_prefix,
    parse_relationship_id,
    create_relationship_id,
    RelationshipIdComponents
)
from app.services.id_service import IdService

logger = get_logger(__name__)

class RelationshipInfo(NamedTuple):
    """Information about a relationship between themes."""
    source_id: str  # With prefix
    source_name: str
    target_id: str  # With prefix
    target_name: str
    relationship_type: str
    properties: Dict[str, Any]

class ThemeRelationshipService:
    """
    Service for managing theme relationships.
    
    This service provides methods for:
    1. Creating, updating, and deleting theme relationships
    2. Querying relationships with various filters
    3. Handling bidirectional relationships
    4. Batch operations on relationships
    
    It abstracts the complexity of Neo4j operations and provides a clean interface
    for resolver functions.
    """
    
    async def get_relationships(
        self,
        session: AsyncSession,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        relationship_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get theme relationships with optional filtering.
        
        Args:
            session: Neo4j database session
            source_id: Optional source theme ID to filter by
            target_id: Optional target theme ID to filter by
            relationship_type: Optional relationship type to filter by
            limit: Maximum number of results to return
            offset: Offset for pagination
            
        Returns:
            List of relationship dictionaries
        """
        try:
            # Standardize IDs if provided
            standard_source_id = IdService.standardize_id(source_id, "theme") if source_id else None
            standard_target_id = IdService.standardize_id(target_id, "theme") if target_id else None
            
            # Convert to database format for query
            db_source_id = IdService.to_database_id(standard_source_id, "theme") if standard_source_id else None
            db_target_id = IdService.to_database_id(standard_target_id, "theme") if standard_target_id else None
            
            # Build WHERE conditions based on provided filters
            where_clauses = []
            params = {
                "limit": int(limit),
                "offset": int(offset)
            }
            
            if db_source_id:
                where_clauses.append("source.id = $source_id")
                params["source_id"] = db_source_id
                
            if db_target_id:
                where_clauses.append("target.id = $target_id")
                params["target_id"] = db_target_id
                
            if relationship_type:
                where_clauses.append("type(r) = $rel_type")
                params["rel_type"] = relationship_type
                
            # Construct WHERE clause
            where_clause = " AND ".join(where_clauses)
            if where_clause:
                where_clause = f"WHERE {where_clause}"
                
            # Build and execute query
            query = f"""
            MATCH (source:Theme)-[r]->(target:Theme)
            {where_clause}
            RETURN source, target, type(r) as relationship_type, properties(r) as properties
            ORDER BY source.name, relationship_type, target.name
            SKIP $offset
            LIMIT $limit
            """
            
            result = await session.run(query, params)
            
            # Process results
            relationships = []
            async for record in result:
                source_node = record["source"]
                target_node = record["target"]
                relationship_type = record["relationship_type"]
                properties = record["properties"]
                
                # Extract data from nodes
                source_data = dict(source_node)
                target_data = dict(target_node)
                
                # Standardize IDs in result
                source_id = IdService.standardize_id(source_data.get("id", ""), "theme")
                target_id = IdService.standardize_id(target_data.get("id", ""), "theme")
                
                # Create relationship ID
                relationship_id = IdService.create_relationship_id(
                    source_id, relationship_type, target_id
                )
                
                # Build relationship dictionary
                relationship = {
                    "id": relationship_id,
                    "source_id": source_id,
                    "source_name": source_data.get("name", ""),
                    "target_id": target_id,
                    "target_name": target_data.get("name", ""),
                    "relationship_type": relationship_type,
                    **properties
                }
                
                relationships.append(relationship)
                
            return relationships
                
        except Exception as e:
            logger.error(f"Error getting theme relationships: {e}")
            raise
    
    async def create_relationship(
        self,
        session: AsyncSession,
        source_id: str,
        target_id: str,
        relationship_type: str,
        properties: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new relationship between themes.
        
        Args:
            session: Neo4j database session
            source_id: Source theme ID
            target_id: Target theme ID
            relationship_type: Type of relationship
            properties: Additional properties for the relationship
            
        Returns:
            Created relationship dictionary or None if creation failed
        """
        try:
            # Standardize IDs
            standard_source_id = IdService.standardize_id(source_id, "theme")
            standard_target_id = IdService.standardize_id(target_id, "theme")
            
            # Convert to database format for query
            db_source_id = IdService.to_database_id(standard_source_id, "theme")
            db_target_id = IdService.to_database_id(standard_target_id, "theme")
            
            # Verify themes exist
            source_exists = await self._theme_exists(session, db_source_id)
            target_exists = await self._theme_exists(session, db_target_id)
            
            if not source_exists:
                logger.error(f"Source theme {standard_source_id} does not exist")
                return None
                
            if not target_exists:
                logger.error(f"Target theme {standard_target_id} does not exist")
                return None
                
            # Add created_at timestamp if not provided
            if "created_at" not in properties:
                properties["created_at"] = datetime.now()
                
            # Always update updated_at timestamp
            properties["updated_at"] = datetime.now()
            
            # Convert properties to Neo4j-compatible format
            props_string = self._properties_to_cypher(properties)
            
            # Build and execute query
            query = f"""
            MATCH (source:Theme {{id: $source_id}}), (target:Theme {{id: $target_id}})
            CREATE (source)-[r:{relationship_type} {props_string}]->(target)
            RETURN source, target, type(r) as relationship_type, properties(r) as properties
            """
            
            params = {
                "source_id": db_source_id,
                "target_id": db_target_id
            }
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                logger.error(f"Failed to create relationship from {standard_source_id} to {standard_target_id}")
                return None
                
            # Extract data from record
            source_node = record["source"]
            target_node = record["target"]
            rel_type = record["relationship_type"]
            rel_props = record["properties"]
            
            # Extract data from nodes
            source_data = dict(source_node)
            target_data = dict(target_node)
            
            # Standardize IDs in result
            source_id = IdService.standardize_id(source_data.get("id", ""), "theme")
            target_id = IdService.standardize_id(target_data.get("id", ""), "theme")
            
            # Create relationship ID
            relationship_id = IdService.create_relationship_id(
                source_id, rel_type, target_id
            )
            
            # Build relationship dictionary
            relationship = {
                "id": relationship_id,
                "source_id": source_id,
                "source_name": source_data.get("name", ""),
                "target_id": target_id,
                "target_name": target_data.get("name", ""),
                "relationship_type": rel_type,
                **rel_props
            }
            
            logger.info(f"Created relationship: {relationship_id}")
            return relationship
            
        except Exception as e:
            logger.error(f"Error creating theme relationship: {e}")
            raise
    
    async def update_relationship(
        self,
        session: AsyncSession,
        relationship_id: str,
        properties: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing theme relationship.
        
        Args:
            session: Neo4j database session
            relationship_id: Relationship ID
            properties: Updated properties for the relationship
            
        Returns:
            Updated relationship dictionary or None if update failed
        """
        try:
            # Standardize and parse relationship ID
            standard_rel_id = IdService.standardize_id(relationship_id, "relationship")
            source_id, rel_type, target_id = IdService.parse_relationship_id(standard_rel_id)
            
            # Convert to database format for query
            db_source_id = IdService.to_database_id(source_id, "theme")
            db_target_id = IdService.to_database_id(target_id, "theme")
            
            # Always update updated_at timestamp
            properties["updated_at"] = datetime.now()
            
            # Convert properties to Neo4j-compatible format
            updates = []
            params = {
                "source_id": db_source_id,
                "target_id": db_target_id,
                "rel_type": rel_type
            }
            
            for key, value in properties.items():
                param_name = f"prop_{key}"
                params[param_name] = value
                updates.append(f"r.{key} = ${param_name}")
                
            updates_clause = ", ".join(updates)
            
            # Build and execute query
            query = f"""
            MATCH (source:Theme {{id: $source_id}})-[r:{rel_type}]->(target:Theme {{id: $target_id}})
            SET {updates_clause}
            RETURN source, target, type(r) as relationship_type, properties(r) as properties
            """
            
            result = await session.run(query, params)
            record = await result.single()
            
            if not record:
                logger.error(f"Failed to update relationship: {standard_rel_id}")
                return None
                
            # Extract data from record
            source_node = record["source"]
            target_node = record["target"]
            rel_type = record["relationship_type"]
            rel_props = record["properties"]
            
            # Extract data from nodes
            source_data = dict(source_node)
            target_data = dict(target_node)
            
            # Standardize IDs in result
            source_id = IdService.standardize_id(source_data.get("id", ""), "theme")
            target_id = IdService.standardize_id(target_data.get("id", ""), "theme")
            
            # Create relationship ID
            relationship_id = IdService.create_relationship_id(
                source_id, rel_type, target_id
            )
            
            # Build relationship dictionary
            relationship = {
                "id": relationship_id,
                "source_id": source_id,
                "source_name": source_data.get("name", ""),
                "target_id": target_id,
                "target_name": target_data.get("name", ""),
                "relationship_type": rel_type,
                **rel_props
            }
            
            logger.info(f"Updated relationship: {relationship_id}")
            return relationship
            
        except Exception as e:
            logger.error(f"Error updating theme relationship: {e}")
            raise
    
    async def delete_relationship(
        self,
        session: AsyncSession,
        relationship_id: str
    ) -> bool:
        """
        Delete a theme relationship.
        
        Args:
            session: Neo4j session
            relationship_id: Relationship ID (format: rel_source_id_TYPE_target_id)
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Parse the relationship ID
            components = parse_relationship_id(relationship_id)
            source_id = remove_theme_id_prefix(components.source_id)
            target_id = remove_theme_id_prefix(components.target_id)
            relationship_type = components.relationship_type
            
            # Check if relationship is bidirectional
            check_query = f"""
            MATCH (source:Theme)-[r:{relationship_type}]->(target:Theme)
            WHERE source.id = $source_id AND target.id = $target_id
            RETURN r.bidirectional as bidirectional
            """
            
            # Execute query to check bidirectional property
            result = await session.run(
                check_query,
                {
                    "source_id": source_id,
                    "target_id": target_id
                }
            )
            
            record = await result.single()
            is_bidirectional = record and record.get("bidirectional", False) if record else False
            
            # Create query for deleting the relationship
            query = f"""
            MATCH (source:Theme)-[r:{relationship_type}]->(target:Theme)
            WHERE source.id = $source_id AND target.id = $target_id
            DELETE r
            """
            
            # Execute delete query
            result = await session.run(
                query,
                {
                    "source_id": source_id,
                    "target_id": target_id
                }
            )
            
            success = result.summary().counters.relationships_deleted > 0
            
            # If bidirectional, also delete the inverse relationship
            if is_bidirectional:
                inverse_query = f"""
                MATCH (source:Theme)-[r:{relationship_type}]->(target:Theme)
                WHERE source.id = $target_id AND target.id = $source_id
                DELETE r
                """
                
                await session.run(
                    inverse_query,
                    {
                        "source_id": target_id,
                        "target_id": source_id
                    }
                )
            
            return success
        except Exception as e:
            logger.error(f"Error deleting theme relationship: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    async def find_relationships_by_themes(
        self,
        session: AsyncSession,
        theme_ids: List[str]
    ) -> List[Dict[str, Any]]:
        """
        Find relationships where any of the provided themes are involved.
        
        Args:
            session: Neo4j session
            theme_ids: List of theme IDs to check
            
        Returns:
            List of relationships involving any of the themes
        """
        # Normalize theme IDs
        clean_ids = [remove_theme_id_prefix(id) for id in theme_ids]
        
        # Create query for finding relationships
        query = """
        MATCH (t1:Theme)-[r]->(t2:Theme)
        WHERE t1.id IN $theme_ids OR t2.id IN $theme_ids
        RETURN t1 as source, r, t2 as target, type(r) as relationship_type
        """
        
        try:
            # Execute query
            result = await session.run(
                query,
                {
                    "theme_ids": clean_ids
                }
            )
            
            records = await result.fetch()
            
            # Format results
            relationships = []
            for record in records:
                source_data = dict(record["source"])
                target_data = dict(record["target"])
                relationship_data = dict(record["r"])
                relationship_type = record["relationship_type"]
                
                # Ensure ID format with theme_ prefix
                source_id = ensure_theme_id_prefix(source_data["id"])
                target_id = ensure_theme_id_prefix(target_data["id"])
                source_data["id"] = source_id
                target_data["id"] = target_id
                
                # Format relationship
                formatted_relationship = {
                    "id": create_relationship_id(source_id, relationship_type, target_id),
                    "sourceId": source_id,
                    "sourceName": source_data.get("name", "Unknown Theme"),
                    "targetId": target_id,
                    "targetName": target_data.get("name", "Unknown Theme"),
                    "type": relationship_type,
                    "strength": relationship_data.get("strength", 1.0),
                    "bidirectional": relationship_data.get("bidirectional", False),
                    "description": relationship_data.get("description"),
                    "createdAt": relationship_data.get("created_at", datetime.now().isoformat()),
                    "updatedAt": relationship_data.get("updated_at", datetime.now().isoformat())
                }
                
                relationships.append(formatted_relationship)
            
            return relationships
        except Exception as e:
            logger.error(f"Error finding theme relationships: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return [] 