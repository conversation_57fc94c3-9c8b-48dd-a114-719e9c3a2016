#!/usr/bin/env python3
"""
Standalone Test for Contextual Theme API

Tests the contextual theme mapping system with mock data that simulates
what the API would receive from AniList, without requiring full app configuration.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.contextual_theme_mapper import ContextualThemeMapper


async def test_contextual_api_standalone():
    """Test the contextual theme API with mock data simulating The Apothecary Diaries."""
    
    print("🧪 Testing Contextual Theme API (Standalone)")
    print("=" * 60)
    
    # Initialize theme mapper
    theme_mapper = ContextualThemeMapper()
    
    # Mock story metadata that simulates what AniList API would return
    # This represents The Apothecary Diaries with enhanced metadata
    story_metadata = {
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Historical", "category": "Setting", "rank": 85},
            {"name": "Court Politics", "category": "Theme", "rank": 80},
            {"name": "Imperial Palace", "category": "Setting", "rank": 75},
            {"name": "Mystery Solving", "category": "Theme", "rank": 70},
            {"name": "Female Protagonist", "category": "Cast", "rank": 90},
            {"name": "Intelligent Female Lead", "category": "Cast", "rank": 85},
            {"name": "Medicine", "category": "Theme", "rank": 75},
            {"name": "Poison", "category": "Theme", "rank": 70}
        ],
        "characters": [
            {
                "id": "character_123456",
                "name": "Maomao",
                "first_name": "Maomao",
                "last_name": None,
                "gender": "Female",
                "age": 17,
                "role": "MAIN",
                "description": "A young apothecary with extensive knowledge of medicine and poisons. Works as a servant in the imperial palace.",
                "image_medium": "https://example.com/maomao.jpg",
                "image_large": "https://example.com/maomao_large.jpg"
            },
            {
                "id": "character_123457",
                "name": "Jinshi",
                "first_name": "Jinshi",
                "last_name": None,
                "gender": "Male",
                "age": 18,
                "role": "MAIN",
                "description": "A beautiful eunuch who works in the imperial palace and takes interest in Maomao's abilities.",
                "image_medium": "https://example.com/jinshi.jpg",
                "image_large": "https://example.com/jinshi_large.jpg"
            },
            {
                "id": "character_123458",
                "name": "Gaoshun",
                "first_name": "Gaoshun",
                "last_name": None,
                "gender": "Male",
                "age": 37,
                "role": "SUPPORTING",
                "description": "Jinshi's loyal retainer and bodyguard.",
                "image_medium": "https://example.com/gaoshun.jpg",
                "image_large": "https://example.com/gaoshun_large.jpg"
            },
            {
                "id": "character_123459",
                "name": "Gyokuyou",
                "first_name": "Gyokuyou",
                "last_name": None,
                "gender": "Female",
                "age": 19,
                "role": "SUPPORTING",
                "description": "One of the Emperor's consorts, known for her intelligence and beauty.",
                "image_medium": "https://example.com/gyokuyou.jpg",
                "image_large": "https://example.com/gyokuyou_large.jpg"
            }
        ],
        "staff": [
            {
                "id": "staff_001",
                "name": "Norihiro Naganuma",
                "first_name": "Norihiro",
                "last_name": "Naganuma",
                "gender": "Male",
                "age": None,
                "role": "Director",
                "description": "Director of The Apothecary Diaries anime adaptation",
                "image_medium": None,
                "image_large": None
            },
            {
                "id": "staff_002",
                "name": "Touko Shino",
                "first_name": "Touko",
                "last_name": "Shino",
                "gender": "Female",
                "age": None,
                "role": "Original Character Design",
                "description": "Character designer for the series, known for detailed historical designs",
                "image_medium": None,
                "image_large": None
            },
            {
                "id": "staff_003",
                "name": "Natsu Hyuuga",
                "first_name": "Natsu",
                "last_name": "Hyuuga",
                "gender": None,
                "age": None,
                "role": "Original Story",
                "description": "Original light novel author",
                "image_medium": None,
                "image_large": None
            },
            {
                "id": "staff_004",
                "name": "Aoi Akashiro",
                "first_name": "Aoi",
                "last_name": "Akashiro",
                "gender": "Female",
                "age": None,
                "role": "Series Composition",
                "description": "Series composition and screenplay writer",
                "image_medium": None,
                "image_large": None
            }
        ]
    }
    
    print(f"📺 Anime: The Apothecary Diaries (Mock API Data)")
    print(f"📊 Genres: {', '.join(story_metadata.get('genres', []))}")
    
    # Simulate API character analysis
    characters = story_metadata.get("characters", [])
    main_chars = [c for c in characters if c.get('role') == 'MAIN']
    female_main = [c for c in main_chars if c.get('gender') == 'Female']
    
    character_analysis = {
        "total_characters": len(characters),
        "main_characters": len(main_chars),
        "female_main_characters": len(female_main),
        "has_female_mc": len(female_main) > 0,
        "female_mc_names": [c.get('name') for c in female_main]
    }
    
    print(f"\n👥 Character Analysis (API Format):")
    print(f"   Total Characters: {character_analysis['total_characters']}")
    print(f"   Main Characters: {character_analysis['main_characters']}")
    print(f"   Female Main Characters: {character_analysis['female_main_characters']}")
    print(f"   Female MC Names: {character_analysis['female_mc_names']}")
    
    # Simulate API staff analysis
    staff = story_metadata.get("staff", [])
    directors = [s for s in staff if 'Director' in s.get('role', '')]
    female_staff = [s for s in staff if s.get('gender') == 'Female']
    
    staff_analysis = {
        "total_staff": len(staff),
        "directors": len(directors),
        "female_staff": len(female_staff),
        "female_staff_names": [s.get('name') for s in female_staff[:5]]
    }
    
    print(f"\n🎬 Staff Analysis (API Format):")
    print(f"   Total Staff: {staff_analysis['total_staff']}")
    print(f"   Directors: {staff_analysis['directors']}")
    print(f"   Female Staff: {staff_analysis['female_staff']}")
    print(f"   Female Staff Names: {staff_analysis['female_staff_names']}")
    
    # Core API functionality - contextual theme analysis
    print(f"\n🎯 Performing Contextual Theme Analysis...")
    theme_matches = theme_mapper.analyze_story_themes(story_metadata)
    
    print(f"\n✅ API Response - Found {len(theme_matches)} theme matches:")
    print("-" * 60)
    
    # Convert to API response format
    api_theme_responses = []
    for i, match in enumerate(theme_matches, 1):
        api_response = {
            "theme_id": match.theme_id,
            "theme_name": match.theme_name,
            "confidence": match.confidence,
            "confidence_percent": f"{match.confidence * 100:.1f}%",
            "base_indicators": match.base_indicators,
            "context_boosts": match.context_boosts,
            "reasoning": match.reasoning
        }
        api_theme_responses.append(api_response)
        
        print(f"\n{i}. {match.theme_name}")
        print(f"   Confidence: {match.confidence * 100:.1f}%")
        if match.base_indicators:
            print(f"   Base Indicators: {', '.join(match.base_indicators)}")
        if match.context_boosts:
            print(f"   Context Boosts:")
            for boost_name, boost_value in match.context_boosts.items():
                print(f"     • {boost_name}: +{boost_value:.2f}")
        if match.reasoning:
            print(f"   Reasoning:")
            for reason in match.reasoning:
                print(f"     • {reason}")
    
    # Create complete API response
    analysis_summary = {
        "total_themes_detected": len(theme_matches),
        "high_confidence_themes": len([m for m in theme_matches if m.confidence >= 0.8]),
        "medium_confidence_themes": len([m for m in theme_matches if 0.5 <= m.confidence < 0.8]),
        "contextual_factors_detected": {
            "female_protagonist": character_analysis["has_female_mc"],
            "historical_setting": any("historical" in g.lower() for g in story_metadata.get("genres", [])),
            "mystery_elements": "Mystery" in story_metadata.get("genres", []),
            "female_creative_input": len(female_staff) > 0
        }
    }
    
    complete_api_response = {
        "anime_title": "The Apothecary Diaries",
        "anime_id": "161645",
        "genres": story_metadata.get("genres", []),
        "character_analysis": character_analysis,
        "staff_analysis": staff_analysis,
        "theme_matches": api_theme_responses,
        "analysis_summary": analysis_summary
    }
    
    print(f"\n📊 Analysis Summary (API Format):")
    print(f"   Total Themes Detected: {analysis_summary['total_themes_detected']}")
    print(f"   High Confidence (≥80%): {analysis_summary['high_confidence_themes']}")
    print(f"   Medium Confidence (50-79%): {analysis_summary['medium_confidence_themes']}")
    print(f"   Contextual Factors:")
    for factor, detected in analysis_summary['contextual_factors_detected'].items():
        status = "✅" if detected else "❌"
        print(f"     {status} {factor.replace('_', ' ').title()}: {detected}")
    
    # API Validation Tests
    print(f"\n🔍 API Validation Tests:")
    print("-" * 40)
    
    # Test 1: Female MC Detection
    female_mc_detected = character_analysis["has_female_mc"]
    print(f"✅ Female MC Detected: {female_mc_detected}")
    
    # Test 2: Female Agency Theme Confidence
    female_agency_theme = next((m for m in theme_matches if "Female Agency" in m.theme_name), None)
    if female_agency_theme:
        confidence_pct = female_agency_theme.confidence * 100
        print(f"✅ Female Agency Theme: {confidence_pct:.1f}% confidence")
        if confidence_pct >= 80:
            print(f"   🎯 HIGH CONFIDENCE - API working as expected!")
        else:
            print(f"   ⚠️  Lower than expected confidence")
    else:
        print(f"❌ Female Agency Theme: Not detected")
    
    # Test 3: Historical Context
    historical_detected = analysis_summary['contextual_factors_detected']['historical_setting']
    print(f"✅ Historical Setting: {historical_detected}")
    
    # Save complete API response
    with open("contextual_api_response.json", "w", encoding="utf-8") as f:
        json.dump(complete_api_response, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Complete API response saved to contextual_api_response.json")
    print(f"\n🎉 Contextual Theme API Test PASSED!")
    print(f"   ✅ API would return {len(theme_matches)} themes")
    print(f"   ✅ Character analysis working")
    print(f"   ✅ Staff analysis working") 
    print(f"   ✅ Contextual theme mapping working")
    print(f"   ✅ Response format correct")


if __name__ == "__main__":
    asyncio.run(test_contextual_api_standalone())
