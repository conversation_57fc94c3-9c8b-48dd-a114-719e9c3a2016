#!/usr/bin/env python3
"""
Debug script for theme mapping issues.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_available_themes():
    """Check what themes are available in the database."""
    print("🎯 Checking Available Themes")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/themes", timeout=10)
        
        if response.status_code == 200:
            themes = response.json()
            print(f"   ✅ Found {len(themes)} themes in database:")
            for theme in themes[:10]:  # Show first 10
                print(f"      • {theme.get('name', 'Unknown')} ({theme.get('category', 'Unknown')})")
            if len(themes) > 10:
                print(f"      ... and {len(themes) - 10} more")
        else:
            print(f"   ❌ Error getting themes: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_simple_theme_mapping():
    """Test the simple theme mapping API that should work."""
    print("\n🎯 Testing Simple Theme Mapping")
    print("=" * 40)
    
    anime_metadata = {
        "title_romaji": "Kusuriya no Hitorigoto",
        "title_english": "The Apothecary Diaries",
        "genres": ["Drama", "Mystery"],
        "tags": [
            {"name": "Female Protagonist", "rank": 95},
            {"name": "Historical", "rank": 90},
            {"name": "Palace", "rank": 85},
            {"name": "Medicine", "rank": 80}
        ],
        "synopsis": "Maomao lived a quiet life as an apothecary in the red-light district."
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/simple-themes/anime/analyze",
            json=anime_metadata,
            timeout=10
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            mappings = response.json()
            print(f"   ✅ Simple mapping found {len(mappings)} themes:")
            for mapping in mappings:
                print(f"      • {mapping['theme_name']}: {mapping['mapping_strength']:.1%} ({mapping['source']})")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_mapper_info():
    """Test the mapper info to see what mappings are configured."""
    print("\n🎯 Testing Mapper Configuration")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/theme-mapping/mapper/info", timeout=10)
        
        if response.status_code == 200:
            info = response.json()
            print(f"   ✅ Mapper Info:")
            print(f"      Description: {info['description']}")
            print(f"      Version: {info['version']}")
            print(f"      Total themes available: {info['total_themes_available']}")
            print(f"      Genre mappings: {len(info['genre_mappings'])}")
            print(f"      Tag mappings: {len(info['tag_mappings'])}")
            
            print(f"\n   📋 Genre Mappings:")
            for genre, theme in list(info['genre_mappings'].items())[:5]:
                print(f"      {genre} → {theme}")
            
            print(f"\n   🏷️ Tag Mappings:")
            for tag, theme in list(info['tag_mappings'].items())[:5]:
                print(f"      {tag} → {theme}")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_contextual_themes():
    """Test the new contextual theme analysis."""
    print("\n🎯 Testing Contextual Theme Analysis")
    print("=" * 40)
    
    try:
        # Test the contextual theme analysis for The Apothecary Diaries
        request_data = {
            "anime_id": "21202",
            "include_reasoning": True
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/21202/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Contextual analysis found {len(data['theme_matches'])} themes:")
            for match in data['theme_matches']:
                print(f"      • {match['theme_name']}: {match['confidence_percent']}")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_available_themes()
    test_simple_theme_mapping()
    test_mapper_info()
    test_contextual_themes()
