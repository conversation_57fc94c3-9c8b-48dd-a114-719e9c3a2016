#!/usr/bin/env python3
"""
Test the fixed frontend API calls.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_get_anime_themes():
    """Test the getAnimeThemes API call that loads on page mount."""
    print("🎯 Testing getAnimeThemes() - Called on Page Load")
    print("=" * 60)
    
    anime_id = "21202"  # The Apothecary Diaries
    
    print(f"Testing contextual themes for anime {anime_id} (page load)...")
    
    try:
        # This is what getAnimeThemes() now calls
        request_data = {
            "anime_id": anime_id,
            "include_reasoning": True
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Page should now show:")
            print(f"   📊 {len(data['theme_matches'])} Female Empowerment themes")
            
            for i, match in enumerate(data['theme_matches'], 1):
                confidence = match.get('confidence', 0)
                print(f"   {i}. {match['theme_name']}: {confidence:.1%}")
            
            print(f"\n   🎉 This should replace the old themes:")
            print(f"      ❌ Tragedy, Emotional Growth, Human Condition")
            print(f"      ✅ Female Agency, Professional Excellence, etc.")
            
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_create_anime_themes():
    """Test the createAnimeThemeMappings API call (Analyze Themes button)."""
    print(f"\n🎯 Testing createAnimeThemeMappings() - Analyze Themes Button")
    print("=" * 60)
    
    anime_id = "21202"
    
    print(f"Testing contextual themes for anime {anime_id} (analyze button)...")
    
    try:
        # This is what createAnimeThemeMappings() calls
        request_data = {
            "anime_id": anime_id,
            "include_reasoning": True
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Analyze button should work:")
            print(f"   📊 {len(data['theme_matches'])} themes returned")
            
            for i, match in enumerate(data['theme_matches'], 1):
                confidence = match.get('confidence', 0)
                print(f"   {i}. {match['theme_name']}: {confidence:.1%}")
            
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_different_anime():
    """Test with a different anime to see variety."""
    print(f"\n🎯 Testing Different Anime")
    print("=" * 40)
    
    # Test with a different anime ID
    anime_id = "1"  # Different anime
    
    try:
        request_data = {
            "anime_id": anime_id,
            "include_reasoning": True
        }
        
        response = requests.post(
            f"{BASE_URL}/contextual-themes/anime/{anime_id}/contextual-themes",
            json=request_data,
            timeout=30
        )
        
        print(f"   Anime ID {anime_id} - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Title: {data.get('anime_title', 'Unknown')}")
            print(f"   Themes: {len(data.get('theme_matches', []))}")
            
            for match in data.get('theme_matches', [])[:3]:  # Show first 3
                confidence = match.get('confidence', 0)
                print(f"      • {match['theme_name']}: {confidence:.1%}")
                
        else:
            print(f"   No themes for anime {anime_id}")
            
    except Exception as e:
        print(f"   Exception for anime {anime_id}: {str(e)}")

if __name__ == "__main__":
    test_get_anime_themes()
    test_create_anime_themes()
    test_different_anime()
