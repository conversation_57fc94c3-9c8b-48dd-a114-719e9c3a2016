# Thematic Mapping Process

## Overview

Thematic mapping is a core functionality of <PERSON><PERSON><PERSON> that translates media-specific metadata (genres, tags, descriptions) into universal themes that work across different media types. This document outlines how the thematic mapping process works and how it's implemented in the system.

## Current Implementation Status

As of the latest update, we have:

- [x] Defined the ThemeMapper interface with abstract methods
- [x] Implemented manual mappers for anime content
- [x] Created the factory pattern for mapper selection
- [x] Migrated theme and theme mapping storage to Neo4j
- [x] Implemented relationship-based theme connections in the graph database
- [x] Added support for mapping strength and categorization (primary, secondary, etc.)
- [x] Implemented theme analysis and recommendation via graph traversal
- [x] Migrated GraphQL resolvers to use Neo4j operations (largely complete)
- [x] Implemented basic Redis caching for theme data (partial)
- [x] Tested core story fetch endpoints with caching
- [ ] Integrated the Agent Service for LLM-powered mapping (planned)
- [ ] Implemented book and movie mappers (planned)
- [ ] Added user feedback mechanisms (planned)
- [ ] Created admin interface for theme management (in progress)
- [ ] Removed legacy SQLAlchemy theme code (in progress)

## ID Standardization

To ensure consistency across the system, we follow these ID formatting conventions:

| Entity Type | ID Format | Example |
|-------------|-----------|---------|
| Theme | `theme_<uuid>` | `theme_123e4567-e89b-12d3-a456-************` |
| Story | `story_<id>` | `story_21` (for One Piece) |
| Mapping | `mapping_<uuid>` | `mapping_123e4567-e89b-12d3-a456-************` |

All IDs should be prefixed according to their entity type throughout the system. The current caching and Neo4j operations respect these conventions. When working with external IDs:

- AniList IDs should be converted to our format: `story_<id>`
- New theme IDs should be generated with the `theme_` prefix
- Mapping relationships should use the `mapping_` prefix for their IDs

## Architecture

The thematic mapping system is designed with a pluggable architecture that supports both manual mapping rules and agent-powered mapping:

```
services/
└── mapping/
    ├── __init__.py
    ├── interface.py      # ABC interface definitions
    ├── anime_mapper.py   # Anime-specific implementations
    ├── book_mapper.py    # Book-specific implementations (future)
    ├── movie_mapper.py   # Movie-specific implementations (future)
    └── factory.py        # Factory for getting the right mapper
```

## Interface Design

Thematic mapping uses Python's Abstract Base Classes (ABC) to define a formal interface that all mappers must implement:

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any

class ThemeMapper(ABC):
    """Base interface for all media-to-universal theme mappers."""
    
    @abstractmethod
    def map_to_universal_themes(self, media_info: Dict[str, Any]) -> List[str]:
        """
        Maps media-specific information to a list of universal themes.
        
        Args:
            media_info: Dictionary containing media metadata (genres, tags, etc.)
            
        Returns:
            List of universal theme identifiers
        """
        pass
    
    @abstractmethod
    def get_confidence_scores(self, media_info: Dict[str, Any]) -> Dict[str, float]:
        """
        Returns confidence scores for theme mappings.
        
        Args:
            media_info: Dictionary containing media metadata
            
        Returns:
            Dictionary mapping theme identifiers to confidence scores (0.0-1.0)
        """
        pass
```

## Implementation Types

### Manual Mappers

Manual mappers use predefined rules and mappings to convert media-specific genres and tags to universal themes. The implementation is complete for anime content in `services/mapping/anime_mapper.py`.

Key implementations:
- `AnimeThemeMapperManual`: Maps anime genres and tags to universal themes
- Fixed mappings in `ANIME_GENRE_THEME_MAP` and `ANIME_TAG_THEME_MAP`
- Confidence scores calculated based on source (genres have higher confidence than tags)

### Agent-Based Mappers

Agent-based mapping is planned but not yet implemented. The current implementation (`AnimeThemeMapperAgent`) falls back to manual mapping.

## Neo4j Implementation

Theme mapping in Neo4j uses a relationship-based approach:

```
(Story)-[HAS_THEME]->(Theme)
```

The HAS_THEME relationship contains properties:
- `mapping_type`: primary, secondary, mood, character, or plot
- `mapping_strength`: A float between 0.0 and 1.0 indicating confidence
- `source`: "manual" or "agent" to track the mapping's origin
- `notes`: Optional context about why this mapping exists

This implementation is complete and used by the GraphQL resolvers.

## Redis Caching Layer

Theme data and analysis results are cached in Redis for improved performance:

- `ThemeRedisService`: Manages theme data in Redis with configurable TTLs
- Theme cache: 24 hours TTL
- Analysis cache: 1 hour TTL
- Stats cache: 5 minutes TTL

The caching functionality is implemented in `services/theme_redis.py` and ensures all IDs follow the standardized prefix conventions.

## Theme Analysis Service

The `ThemeAnalysisService` in `services/theme_analysis.py` provides:

- Mapping of genres to themes
- Analysis of tags for theme extraction
- Confidence scoring for theme mappings
- Categorization of themes (primary, secondary, mood, etc.)

This service works with both Redis caching and Neo4j storage, generating theme mappings with proper ID prefixes.

## GraphQL Implementation

The GraphQL schema and resolvers are implemented using Ariadne:

- Schema defined in `graphql/schema.graphql`
- Resolvers in `graphql/resolvers.py`
- Full theme-related queries and mutations available

The resolvers have been updated to use Neo4j operations, but some refinement may be needed for consistency.

## Legacy Code Status

Some legacy code still exists and needs to be addressed:

- `crud/theme.py`: Contains SQLAlchemy-based theme operations that should be removed or updated
- The migration to `crud/neo4j/theme.py` is not fully complete

## Next Steps (Updated)

1. **Complete Legacy Code Removal**:
   - Remove or update `crud/theme.py` to use Neo4j operations exclusively
   - Ensure all code paths use the Neo4j implementations
   - Standardize ID usage across all operations

2. **Improve Redis/Neo4j Integration**:
   - **Partially Complete**:
     - Basic Redis caching functionality for theme data works
     - Cache invalidation for theme updates has been implemented
     - Redis downtime recovery handling implemented
     - Concurrent Redis operations are functioning correctly
   - **In Progress**:
     - Integration between ThemeAnalysisService and Neo4j operations needs refinement
     - ThemeRedisService needs proper connection to Neo4j modules (via theme_redis attribute)
   - **Still Needed**:
     - Implementation of set_stats method in ThemeRedisService
     - Pipeline implementation for bulk operations needs to be completed
     - Cache update detection for stale data
     - Finalize cache key consistency standards across all operations

3. **Refine GraphQL Resolvers**:
   - Ensure all resolvers consistently use Neo4j operations
   - Standardize error handling and response formatting
   - Add missing resolver implementations if any

4. **Implement Admin Interface**:
   - Complete the admin interface for theme management
   - Add CRUD operations for themes through the admin UI
   - Implement theme relationship visualization

5. **Develop Agent Service Integration**:
   - Begin implementing the agent-based mapper using LLM capabilities
   - Create proper API endpoints for the Agent Service
   - Implement fallback mechanisms for when the Agent Service is unavailable

6. **Additional Media Types**:
   - Add mappers for books and movies based on the existing anime mapper pattern
   - Extend the theme mapping rules for different media types
   - Implement cross-media recommendations based on shared themes

7. **User Feedback Loop**:
   - Add mechanisms for users to provide feedback on theme mappings
   - Implement feedback-based improvement of theme mappings
   - Create analytics for theme mapping quality

8. **Performance Optimization**:
   - Analyze and optimize Neo4j queries for theme-based recommendations
   - Improve caching strategies based on usage patterns
   - Implement more granular cache invalidation

## Implementation Schedule

| Task | Priority | Status | Estimated Completion |
|------|----------|--------|----------------------|
| Legacy Code Removal | Critical | In Progress | 1 week |
| Redis/Neo4j Integration | High | Partially Complete | 2 weeks |
| GraphQL Resolver Refinement | High | Mostly Complete | 1 week |
| Admin Interface | Medium | Planned | 3 weeks |
| Agent Service Integration | Medium | Research Phase | 5 weeks |
| Book Mapper Implementation | Low | Planned | 6 weeks |
| Movie Mapper Implementation | Low | Planned | 6 weeks |
| User Feedback Mechanism | Medium | Planned | 4 weeks |
| Performance Optimization | High | Ongoing | Continuous |

## Testing Strategy

Testing for theme mapping includes:

1. **Component Tests**:
   - Test theme mappers in isolation
   - Validate mapping rules for different content types
   - Test confidence scoring algorithms

2. **Integration Tests**:
   - Verify Neo4j relationship creation for themes
   - Test Redis caching for theme data
   - Validate GraphQL resolver functionality

3. **End-to-End Tests**:
   - Test the complete flow from content retrieval to theme mapping to recommendation
   - Validate cross-media recommendations based on themes
   - Test the admin interface for theme management

Tests should be run through Docker using the provided scripts.

## Technical Dependencies

- Neo4j Graph Database (already configured)
- Redis Cache (configured, implementation in progress)
- GraphQL with Ariadne (in use, requires resolver refinements)
- FastAPI Backend (already configured)
- Agent Service (to be developed for LLM integration)

## Conclusion

The thematic mapping system forms the backbone of Tahimoto's cross-media recommendation capabilities. The core infrastructure is in place with Neo4j relationships and Redis caching, with tested story fetch endpoints. The immediate focus should be on completing the legacy code removal, refining the GraphQL resolvers, and implementing the admin interface.

Once these foundations are solid, we can move on to more advanced features like agent-based mapping and cross-media recommendation enhancements. The defined ID standardization and architecture patterns should be followed consistently across all new development. 