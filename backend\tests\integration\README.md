# Integration Tests

This directory contains integration tests for the <PERSON><PERSON><PERSON> backend, testing complete workflows and interactions between multiple components.

## What are Integration Tests?

Integration tests verify that multiple components of the system work together correctly. Key characteristics:

- Test complete workflows from end to end
- Use real dependencies where possible
- Verify system behavior as a whole
- Focus on interaction points between components

## Test Categories

### 1. API Endpoint Tests (`api/test_stories.py`)

Tests the REST API endpoints:
- Stories search API
- Story details API
- Recommendations API
- Error handling and edge cases

### 2. Neo4j Integration (`test_neo4j_integration.py`, `test_neo4j_index_performance.py`)

Tests integrated Neo4j functionality:
- Database connection and queries
- Index performance
- Transaction management
- Query performance benchmarks

### 3. Redis Integration (`test_neo4j_redis_integration.py`)

Tests Redis and Neo4j working together:
- Caching strategies
- Cache invalidation
- Redis pipeline operations
- Performance improvements with caching

### 4. End-to-End Workflows (`test_search_to_display_workflow.py`, `test_theme_cache_integration.py`)

Tests complete workflows:
- Search to recommendations flow
- Theme caching and refresh
- Data retrieval and transformation
- Error handling and recovery

## Neo4j Testing Strategies

### Using Real Neo4j Instances

For true integration testing, we use real Neo4j instances in Docker containers. This allows us to:
- Test actual database behavior
- Verify query performance
- Test transactions and concurrent operations
- Validate index behavior

### Mocking Neo4j Operations

For faster, more focused tests, we use mocking strategies:

1. **Session Mocking**: Create a `MockSession` class that mimics the Neo4j session behavior:
   ```python
   class MockSession:
       async def __aenter__(self):
           return self
       
       async def __aexit__(self, exc_type, exc_val, exc_tb):
           pass
       
       async def run(self, *args, **kwargs):
           return await mock_run(*args, **kwargs)
   ```

2. **CRUD Operation Mocking**: Mock CRUD operations to return predictable test data:
   ```python
   async def mock_get_story(*args, **kwargs):
       return {
           "id": "story_123456",
           "external_id": "123456",
           # other fields...
       }
   ```

3. **Recommendation Function Mocking**: Create specific mocks for complex operations:
   ```python
   async def mock_neo4j_recommendations(db, story_id, genres=None, min_score=0, limit=10, status=None):
       # Create base recommendations with filtering behavior
       # ...
   ```

4. **Patching Strategy**: Use `unittest.mock.patch` to replace functions and dependencies:
   ```python
   with patch('app.api.v1.endpoints.stories.get_db_session', return_value=MockSession()), \
        patch('app.crud.neo4j.story.CRUDStory.get', side_effect=mock_get_story):
       # Test code here
   ```

These mocking strategies allow us to test API endpoints without requiring an actual Neo4j database, making tests faster and more reliable.

## Containers for Integration Testing

The tests use Docker containers for isolated testing:

- **Neo4j**: Container for graph database testing
- **Redis**: Container for cache testing

These containers ensure tests run in a controlled environment without affecting development instances.

## Common Fixtures

The `conftest.py` in this directory provides fixtures specifically for integration testing:

- `docker_client`: Docker client for container management
- `neo4j_container`: Neo4j container for isolated testing
- `redis_container`: Redis container for cache testing
- `neo4j_test_session`: Session connected to test Neo4j container
- `redis_test_client`: Client connected to test Redis container
- `integration_client`: FastAPI test client for API testing
- `create_test_stories`: Factory for creating test stories in Neo4j
- `sample_stories_for_integration`: Sample story data for testing

## When to Write Integration Tests

Write integration tests when:

1. Testing complete workflows that span multiple components
2. Verifying interactions between different parts of the system
3. Testing with real external dependencies
4. Validating system performance under realistic conditions

## Best Practices

1. **Manage Test Data**: Create and clean up test data properly
2. **Isolate Test Environment**: Use containers to avoid affecting development instances
3. **Test Real Workflows**: Focus on testing realistic user journeys
4. **Control External Dependencies**: Use controlled versions of external services
5. **Handle Asynchronous Operations**: Account for async behavior in tests
6. **Use Appropriate Mocking**: Choose the right level of mocking based on test goals
7. **Balance Real vs. Mocked Dependencies**: Use real dependencies for true integration tests, but mock when appropriate for speed and isolation

## Running Integration Tests

```powershell
# Run all integration tests with Docker support
.\tests\scripts\run_tests.ps1 -TestType integration -WithDocker

# Reuse existing containers for faster testing
.\tests\scripts\run_tests.ps1 -TestType integration -WithDocker -ReuseContainers

# Run specific integration tests
.\tests\scripts\run_tests.ps1 -TestType integration -WithDocker -TestPattern "api"

# Run with coverage
.\tests\scripts\run_tests.ps1 -TestType integration -WithDocker -Coverage
```

Or manually:

```powershell
# Run directly with pytest (requires Docker containers)
python -m pytest tests/integration/ -m integration --run-container-tests -v

# Run in Docker
docker exec tahimoto-backend python -m pytest tests/integration/ -v
``` 