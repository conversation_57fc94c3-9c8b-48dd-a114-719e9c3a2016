"""
GraphQL resolvers for theme-related operations.
"""
import logging
from typing import Dict, List, Any
from datetime import datetime
from ariadne import QueryType, MutationType
from app.crud.neo4j import theme
import neo4j
from app.graphql.resolvers.utils.formatters import format_theme
from app.graphql.resolvers import query_resolvers, mutation_resolvers

logger = logging.getLogger(__name__)

# Initialize resolver objects
# query_resolvers = QueryType()
# mutation_resolvers = MutationType()

@query_resolvers.field("theme")
async def resolve_theme(_, info, id: str):
    """Resolver for theme query."""
    logger.debug(f"Resolving theme with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    theme_data = await theme.get(session, id)
    if not theme_data:
        return None
    
    return format_theme(theme_data)

@query_resolvers.field("themes")
async def resolve_themes(_, info, name=None, category=None, status=None, limit=10, offset=0, 
                        prioritize_connected=True):
    """Resolve themes."""
    logger.debug(f"Resolving themes with limit: {limit}, offset: {offset}")
    try:
        # Get the Neo4j session from the request
        session = info.context["request"].state.db_session
        
        # Prepare query parameters
        params = {"skip": offset, "limit": limit}
        conditions = []
        
        # Add filters if provided
        if name:
            params["name"] = f"(?i).*{name}.*"  # Case-insensitive substring match
            conditions.append("n.name =~ $name")
            
        if category:
            params["category"] = category
            conditions.append("n.category = $category")
            
        if status:
            params["status"] = status
            conditions.append("n.status = $status")
            
        # Build filter string
        filter_string = ""
        if conditions:
            filter_string = "WHERE " + " AND ".join(conditions)
        
        # Execute the query with explicit transaction management
        tx = await session.begin_transaction()
        try:
            # Execute the query with modified ordering to prioritize themes with relationships if requested
            if prioritize_connected:
                query = f"""
                MATCH (n:Theme)
                {filter_string}
                OPTIONAL MATCH (n)-[r]-() 
                WITH n, COUNT(r) as relationshipCount
                RETURN n, relationshipCount
                ORDER BY relationshipCount DESC, n.created_at DESC
                SKIP $skip
                LIMIT $limit
                """
            else:
                query = f"""
                MATCH (n:Theme)
                {filter_string}
                RETURN n
                ORDER BY n.created_at DESC
                SKIP $skip
                LIMIT $limit
                """
            
            logger.debug(f"Executing theme query: {query}")
            logger.debug(f"With parameters: {params}")
            
            result = await tx.run(query, params)
            records = await result.fetch(limit)
            
            logger.debug(f"Query returned {len(records)} records")
            
            # Transform records to expected format
            themes = []
            for i, record in enumerate(records):
                theme_data = dict(record["n"])
                logger.debug(f"Processing theme {i+1}: {theme_data}")
                
                # Ensure ID format with theme_ prefix
                if "id" in theme_data:
                    # Strip any existing theme_ prefix to avoid double prefixing
                    theme_id = theme_data["id"]
                    if theme_id.startswith("theme_"):
                        theme_id = theme_id[6:]  # Remove the "theme_" prefix
                    
                    # Add the theme_ prefix
                    theme_data["id"] = f"theme_{theme_id}"
                    logger.debug(f"Normalized ID: {theme_data['id']}")
                else:
                    logger.warning(f"Theme missing ID: {theme_data}")
                    continue  # Skip themes without ID
                    
                # Check if name is present
                if "name" not in theme_data:
                    logger.warning(f"Theme missing name: {theme_data}")
                    continue  # Skip themes without name
                    
                # Add theme to result list
                formatted_theme = format_theme(theme_data)
                logger.debug(f"Formatted theme: {formatted_theme}")
                themes.append(formatted_theme)
            
            # Commit the transaction
            await tx.commit()
            
            logger.debug(f"Returning {len(themes)} themes")
            return themes
        except Exception as e:
            # Rollback the transaction in case of error
            await tx.rollback()
            logger.error(f"Error resolving themes: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []
    except Exception as e:
        logger.error(f"Error resolving themes: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

@query_resolvers.field("themeStats")
async def resolve_theme_stats(_, info):
    """Resolver for themeStats query."""
    logger.debug("Resolving theme stats")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operations to get counts
    all_themes = await theme.get_multi(session, limit=1000)  # Get all themes (up to 1000)
    mapped_themes = [t for t in all_themes if t.get("status") == "MAPPED"]
    pending_themes = [t for t in all_themes if t.get("status") == "PENDING"]
    needs_review_themes = [t for t in all_themes if t.get("status") == "NEEDS_REVIEW"]
    
    # TODO: Implement actual cache hit rate calculation
    cache_hit_rate = 0.75  # Placeholder
    
    return {
        "totalThemes": len(all_themes),
        "mappedCount": len(mapped_themes),
        "pendingCount": len(pending_themes),
        "needsReviewCount": len(needs_review_themes),
        "cacheHitRate": cache_hit_rate
    }

@mutation_resolvers.field("createTheme")
async def resolve_create_theme(_, info, input):
    """Resolver for createTheme mutation."""
    logger.debug(f"Creating theme with input: {input}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Prepare theme data
    theme_data = {
        "name": input["name"],
        "description": input.get("description", ""),
        "category": input.get("category", "UNCATEGORIZED"),
        "sub_category": input.get("subCategory"),
        "parent_theme_id": input.get("parentThemeId"),
        "dimensions": input.get("dimensions", []),
        "status": input.get("status", "MAPPED"),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "cultural_context": input.get("culturalContext", []),
        "evolution_stage": input.get("evolutionStage"),
        "implicit_tags": input.get("implicitTags", [])
    }
    
    # Use Neo4j CRUD operation
    created_theme = await theme.create(session, theme_data)
    
    return format_theme(created_theme)

@mutation_resolvers.field("updateTheme")
async def resolve_update_theme(_, info, id, input):
    """Resolver for updateTheme mutation."""
    logger.debug(f"Updating theme with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Prepare update data
    update_data = {k: v for k, v in input.items() if v is not None}
    
    # Convert camelCase to snake_case for database
    if "subCategory" in update_data:
        update_data["sub_category"] = update_data.pop("subCategory")
    if "parentThemeId" in update_data:
        update_data["parent_theme_id"] = update_data.pop("parentThemeId")
    if "culturalContext" in update_data:
        update_data["cultural_context"] = update_data.pop("culturalContext")
    if "evolutionStage" in update_data:
        update_data["evolution_stage"] = update_data.pop("evolutionStage")
    if "implicitTags" in update_data:
        update_data["implicit_tags"] = update_data.pop("implicitTags")
    
    # Add updated timestamp
    update_data["updated_at"] = datetime.now().isoformat()
    
    # Use Neo4j CRUD operation
    updated_theme = await theme.update(session, id, update_data)
    
    return format_theme(updated_theme)

@mutation_resolvers.field("deleteTheme")
async def resolve_delete_theme(_, info, id):
    """Resolver for deleteTheme mutation."""
    logger.debug(f"Deleting theme with ID: {id}")
    # Get database session from context
    session = info.context["request"].state.db_session
    
    # Use Neo4j CRUD operation
    success = await theme.delete(session, id)
    
    return {"success": success, "id": id}
