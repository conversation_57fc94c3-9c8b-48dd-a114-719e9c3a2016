# Tag Combination Analyzer

## Overview

The Tag Combination Analyzer is a core component of the Tahimoto thematic mapping system. It analyzes combinations of genres and tags from media content to identify deeper thematic elements that might not be explicitly labeled. By recognizing patterns and relationships between different tags, the analyzer can extract more nuanced themes that enhance content discovery and recommendation.

![Tag Combination Analyzer Architecture](https://via.placeholder.com/800x400?text=Tag+Combination+Analyzer+Architecture)

## Key Features

- **Pattern Recognition**: Identifies meaningful combinations of tags and genres
- **Confidence Scoring**: Provides quantitative assessment of mapping reliability
- **Contextual Analysis**: Considers tag categories, ranks, and relationships
- **Flexible Mapping Types**: Categorizes themes by type (mood, genre_combination, setting, etc.)
- **Extensive Configuration**: Supports custom rules and pattern matching

## Architecture

The Tag Combination Analyzer operates as part of the mapping service layer in the Tahimoto backend:

```
app/
└── services/
    └── mapping/
        ├── interface.py         # ThemeMapper interface definition
        ├── anime_mapper.py      # Anime-specific mapper implementation
        ├── tag_combination_analyzer.py  # This service
        └── factory.py           # Factory for selecting the appropriate mapper
```

The analyzer maintains internal rules for tag combinations and provides methods for analyzing media content. It is designed to be used by specific mapper implementations (like `AnimeThemeMapperManual`) but can also be used standalone for analyzing tag data.

## API Reference

### Constructor

```python
def __init__(self, rules: Optional[List[Dict[str, Any]]] = None):
    """
    Initialize the TagCombinationAnalyzer with optional custom rules.
    
    Args:
        rules: Optional list of custom tag combination rules. If None, default rules are loaded.
    """
```

### Core Analysis Methods

#### `analyze_combinations`

```python
def analyze_combinations(self, genres: List[str], tags: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Analyze combinations of genres and tags to extract thematic elements.
    
    Args:
        genres: List of genre strings (e.g., ["Action", "Drama"])
        tags: List of tag dictionaries, each with at least a "name" key
             (e.g., [{"name": "School", "category": "Setting", "rank": 90}])
    
    Returns:
        List of theme mapping dictionaries with the following structure:
        {
            "theme_id": "psychological_thriller",
            "name": "Psychological Thriller",
            "confidence": 0.85,
            "mapping_type": "mood",
            "context": "Pattern match: Thriller, Psychological"
        }
    """
```

#### `get_confidence_scores`

```python
def get_confidence_scores(self, mappings: List[Dict[str, Any]]) -> Dict[str, float]:
    """
    Extract confidence scores from theme mappings.
    
    Args:
        mappings: List of theme mapping dictionaries from analyze_combinations
    
    Returns:
        Dictionary mapping theme_id to confidence score
        (e.g., {"psychological_thriller": 0.85})
    """
```

#### `get_mapping_types`

```python
def get_mapping_types(self, mappings: List[Dict[str, Any]]) -> Dict[str, str]:
    """
    Extract mapping types from theme mappings.
    
    Args:
        mappings: List of theme mapping dictionaries from analyze_combinations
    
    Returns:
        Dictionary mapping theme_id to mapping type
        (e.g., {"psychological_thriller": "mood"})
    """
```

### Pattern Matching Methods

#### `match_patterns`

```python
def match_patterns(self, normalized_tags: Set[str]) -> List[Dict[str, Any]]:
    """
    Match tag patterns against defined rules.
    
    Args:
        normalized_tags: Set of normalized tag names
    
    Returns:
        List of matched rules with theme information
    """
```

#### `normalize_tags`

```python
def normalize_tags(self, genres: List[str], tags: List[Dict[str, Any]]) -> Set[str]:
    """
    Normalize genres and tags into a unified set of strings for pattern matching.
    
    Args:
        genres: List of genre strings
        tags: List of tag dictionaries
    
    Returns:
        Set of normalized tag strings
    """
```

### Analysis Context Methods

#### `analyze_context`

```python
def analyze_context(self, genres: List[str], tags: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze the context of tags and genres for more complex theme extraction.
    
    Args:
        genres: List of genre strings
        tags: List of tag dictionaries
    
    Returns:
        Dictionary with contextual analysis information
    """
```

## Usage Examples

### Basic Usage

```python
from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer

# Create analyzer with default rules
analyzer = TagCombinationAnalyzer()

# Sample data
genres = ["Psychological", "Thriller", "Mystery"]
tags = [
    {"name": "School", "category": "Setting", "rank": 80},
    {"name": "Tragedy", "category": "Theme", "rank": 75},
    {"name": "Coming of Age", "category": "Theme", "rank": 70}
]

# Analyze the combinations
mappings = analyzer.analyze_combinations(genres, tags)

# Get confidence scores
confidence_scores = analyzer.get_confidence_scores(mappings)

# Get mapping types
mapping_types = analyzer.get_mapping_types(mappings)

# Print results
for mapping in mappings:
    print(f"Theme: {mapping['name']}")
    print(f"Confidence: {mapping['confidence']}")
    print(f"Type: {mapping['mapping_type']}")
    print(f"Context: {mapping['context']}\n")
```

### Integration with AnimeThemeMapperManual

```python
from app.services.mapping.anime_mapper import AnimeThemeMapperManual
from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer

# Create mapper and analyzer
analyzer = TagCombinationAnalyzer()
mapper = AnimeThemeMapperManual()

# Inject analyzer into mapper
mapper.tag_analyzer = analyzer

# Anime media data
anime_data = {
    "genres": ["Romance", "Comedy", "Slice of Life"],
    "tags": [
        {"name": "School", "category": "Setting", "rank": 90},
        {"name": "Iyashikei", "category": "Theme", "rank": 85},
    ]
}

# Get enhanced theme mapping
themes = mapper.get_enhanced_theme_mapping(anime_data)
print(f"Enhanced themes: {themes}")

# Get confidence scores
scores = mapper.get_confidence_scores(anime_data)
print(f"Confidence scores: {scores}")
```

### Custom Rules

```python
from app.services.mapping.tag_combination_analyzer import TagCombinationAnalyzer

# Define custom rules
custom_rules = [
    {
        "pattern": ["Military", "Mecha"],
        "result_theme": "military_sci_fi",
        "display_name": "Military Science Fiction",
        "confidence": 0.80,
        "theme_type": "genre_combination",
        "description": "Stories featuring military organizations using advanced technology"
    },
    {
        "pattern": ["Urban Fantasy", "Romance"],
        "result_theme": "urban_paranormal_romance",
        "display_name": "Urban Paranormal Romance",
        "confidence": 0.75,
        "theme_type": "genre_combination",
        "description": "Romance stories with supernatural elements in modern urban settings"
    }
]

# Create analyzer with custom rules
analyzer = TagCombinationAnalyzer(rules=custom_rules)

# Use analyzer with custom rules
genres = ["Military", "Science Fiction"]
tags = [{"name": "Mecha", "category": "Theme", "rank": 95}]

mappings = analyzer.analyze_combinations(genres, tags)
print(f"Custom analysis: {mappings}")
```

## Configuration

### Default Rules

The TagCombinationAnalyzer comes with a set of default rules defined internally. These rules map common tag combinations to universal themes with appropriate confidence scores and theme types.

Example default rules:

```python
[
    {
        "pattern": ["Thriller", "Psychological"],
        "result_theme": "psychological_thriller",
        "display_name": "Psychological Thriller",
        "confidence": 0.85,
        "theme_type": "mood",
        "description": "Dark exploration of the human mind under extreme stress"
    },
    {
        "pattern": ["School", "Romance", "Comedy"],
        "result_theme": "school_romantic_comedy",
        "display_name": "School Romantic Comedy",
        "confidence": 0.80,
        "theme_type": "genre_combination",
        "description": "Light-hearted romantic stories set in school environments"
    }
]
```

### Pattern Rule Structure

Each pattern rule requires the following fields:

| Field | Type | Description |
|-------|------|-------------|
| `pattern` | List[str] | List of tag/genre names that must be present |
| `result_theme` | str | Theme ID to use for the mapping |
| `display_name` | str | Human-readable name for the theme |
| `confidence` | float | Confidence score (0.0-1.0) for the mapping |
| `theme_type` | str | Type of theme (mood, genre_combination, setting, etc.) |
| `description` | str | Description of the theme |

### Custom Configuration

To use custom rules:

1. Define your rules following the structure above
2. Pass them to the TagCombinationAnalyzer constructor
3. The custom rules will completely replace the default rules

## Extension and Customization

### Adding New Pattern Rules

The simplest way to extend the analyzer is to add new pattern rules:

```python
analyzer = TagCombinationAnalyzer()

# Add new rules
analyzer.combination_rules.extend([
    {
        "pattern": ["Historical", "War", "Drama"],
        "result_theme": "historical_war_drama",
        "display_name": "Historical War Drama",
        "confidence": 0.85,
        "theme_type": "genre_combination",
        "description": "Dramatic stories set during historical wars"
    }
])
```

### Subclassing for Enhanced Functionality

For more complex customizations, you can subclass the TagCombinationAnalyzer:

```python
class EnhancedTagAnalyzer(TagCombinationAnalyzer):
    """Enhanced tag analyzer with additional features."""
    
    def __init__(self, rules=None, advanced_settings=None):
        super().__init__(rules)
        self.advanced_settings = advanced_settings or {}
    
    def analyze_combinations(self, genres, tags):
        # Get basic results from parent class
        basic_results = super().analyze_combinations(genres, tags)
        
        # Add your enhanced analysis
        enhanced_results = self._perform_enhanced_analysis(basic_results, genres, tags)
        
        return enhanced_results
    
    def _perform_enhanced_analysis(self, basic_results, genres, tags):
        # Your custom analysis logic here
        return basic_results  # Replace with your enhanced results
```

### Integration with External Services

You can extend the TagCombinationAnalyzer to work with external services:

```python
class AIEnhancedTagAnalyzer(TagCombinationAnalyzer):
    """Tag analyzer enhanced with AI capabilities."""
    
    def __init__(self, rules=None, ai_service_url=None):
        super().__init__(rules)
        self.ai_service_url = ai_service_url or "http://localhost:8000/ai/analyze"
    
    async def analyze_combinations_with_ai(self, genres, tags):
        # Get basic analysis
        basic_mappings = self.analyze_combinations(genres, tags)
        
        # Enhance with AI service
        enhanced_mappings = await self._enhance_with_ai(basic_mappings, genres, tags)
        
        return enhanced_mappings
    
    async def _enhance_with_ai(self, basic_mappings, genres, tags):
        # Implement AI service integration here
        # This could call an external API or a local model
        return basic_mappings  # Replace with AI-enhanced results
```

## Best Practices

1. **Regular Rule Updates**: Periodically review and update the combination rules to maintain accuracy
2. **Confidence Thresholds**: Use confidence scores to filter out low-confidence mappings
3. **Testing**: Test new rules with diverse content to ensure consistent results
4. **Fallback Mechanisms**: Always provide fallback options when no patterns match
5. **Performance**: Keep pattern matching efficient by using normalized tag sets

## Troubleshooting

### Common Issues

#### No Patterns Matched

If analyze_combinations returns an empty list:
- Verify that the input genres and tags are correctly formatted
- Check if the normalized tags match any defined patterns
- Consider adding more inclusive patterns or relaxing match requirements

#### Low Confidence Scores

If confidence scores are consistently low:
- Review the confidence values in your pattern rules
- Consider tag ranks/weights when calculating confidence
- Improve pattern specificity for higher confidence

#### Inconsistent Mapping Types

If mapping types are inconsistent:
- Standardize theme_type values in pattern rules
- Use the get_mapping_types method to verify mapping type consistency
- Document accepted mapping types for your application

## Future Enhancements

- **Machine Learning Integration**: Enhance pattern matching with ML-based recommendations
- **User Feedback Loop**: Incorporate user feedback to improve mapping accuracy
- **Cross-Media Analysis**: Extend analysis to handle different media types consistently
- **Semantic Understanding**: Add natural language processing for deeper theme extraction

## Related Documentation

- [Theme Mapping Architecture](./theme_mapping_architecture.md)
- [Neo4j Theme Storage](./neo4j_theme_storage.md)
- [Anime Mapper Implementation](./anime_mapper.md)
- [Mapping Factory Pattern](./mapping_factory.md) 