"""
Unit test fixtures and configuration.
These fixtures are designed for pure unit tests that don't require external dependencies.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Mark all tests in this directory as unit tests
pytest.mark.unit = pytest.mark.unit

# Add fixtures specific to unit tests
@pytest.fixture
def mock_anilist_service():
    """Create a mock AniList service for unit tests."""
    mock = MagicMock()
    
    # Mock the get_anime_details method
    mock.get_anime_details = AsyncMock()
    mock.get_anime_details.return_value = {
        "id": 21,
        "title": {"romaji": "One Piece", "english": "One Piece", "native": "ワンピース"},
        "description": "Test description",
        "coverImage": {"large": "http://example.com/image.jpg"},
        "genres": ["Action", "Adventure"],
        "tags": [{"name": "Pirates"}, {"name": "Shounen"}]
    }
    
    # Mock the transform_to_story method
    mock.transform_to_story.return_value = {
        "id": "story_21",
        "title": "One Piece",
        "description": "Test description",
        "image_url": "http://example.com/image.jpg",
        "themes": ["Action", "Adventure", "Pirates", "Shounen"],
        "source": "anilist",
        "source_id": 21,
        "last_updated": "2023-01-01T00:00:00"
    }
    
    return mock

@pytest.fixture
def mock_redis():
    """Create a mock Redis client for unit tests."""
    mock = MagicMock()
    
    # Mock common Redis methods
    mock.get = AsyncMock(return_value=None)
    mock.set = AsyncMock(return_value=True)
    mock.delete = AsyncMock(return_value=1)
    mock.exists = AsyncMock(return_value=0)
    mock.expire = AsyncMock(return_value=True)
    mock.pipeline = MagicMock(return_value=mock)
    mock.execute = AsyncMock(return_value=[])
    
    return mock

@pytest.fixture
def sample_story_data():
    """Provide sample story data for tests."""
    return {
        "id": "story_21",
        "title": "One Piece",
        "description": "Test description",
        "image_url": "http://example.com/image.jpg",
        "themes": ["Action", "Adventure", "Pirates", "Shounen"],
        "source": "anilist",
        "source_id": 21,
        "last_updated": "2023-01-01T00:00:00"
    } 