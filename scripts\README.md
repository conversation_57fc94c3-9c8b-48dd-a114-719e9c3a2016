# ID Standardization Tools

This directory contains tools for standardizing and validating IDs across the Tahimoto application.

## Overview

The ID standardization system ensures that all entity IDs follow a consistent format:
- All IDs have the correct prefix for their entity type (e.g., `theme_adventure`)
- IDs are used consistently in code and database
- Relationships between entities use standardized IDs

## Demo Scripts

### 1. Basic ID Service Demo

Run this script to see how the ID service works:

```bash
python run_id_checks.py
```

This demonstrates:
- Standardizing IDs with the correct prefix
- Converting IDs to database format
- Detecting UUID vs semantic IDs
- Validating ID format
- Finding entities by ID or name
- Generating cache keys
- Creating and parsing relationship IDs

### 2. Code Validation Demo

Run this script to see how the code validation works:

```bash
python validate_id_demo.py
```

This demonstrates:
- Scanning code for non-standard IDs
- Detecting ID issues in different contexts (variables, function calls, etc.)
- Generating reports of ID issues
- Automatically fixing ID issues

### 3. Database Validation Demo

Run this script to see how the database validation works:

```bash
python verify_database_demo.py
```

This demonstrates:
- Checking database entities for non-standard IDs
- Validating relationship IDs
- Generating reports of database ID issues
- Automatically fixing database ID issues

## Real Tools

The demo scripts show how the ID standardization system works. The real tools for your project are:

### 1. ID Service

The `IdService` class in `backend/app/services/id_service.py` provides methods for standardizing and validating IDs.

Example usage:

```python
from app.services.id_service import IdService

# Standardize an ID
theme_id = IdService.standardize_id("adventure", "theme")  # Returns "theme_adventure"

# Validate an ID
is_valid = IdService.validate_id("theme_adventure", "theme")  # Returns True

# Generate a new ID
new_id = IdService.generate_id("theme")  # Returns "theme_<uuid>"
```

### 2. Code Validation Script

The `validate_id_usage.py` script scans your codebase for non-standard IDs.

```bash
# Scan the backend code
python scripts/validate_id_usage.py --path=backend

# Scan and fix issues
python scripts/validate_id_usage.py --path=backend --autofix

# Generate a report
python scripts/validate_id_usage.py --path=backend --report=id_report.json
```

### 3. Database Validation Script

The `verify_database_ids.py` script checks your database for non-standard IDs.

```bash
# Check the database
python scripts/verify_database_ids.py

# Check and fix issues
python scripts/verify_database_ids.py --autofix

# Generate a report
python scripts/verify_database_ids.py --report=db_report.json
```

## CI Integration

The ID standardization checks are integrated into the CI pipeline via the GitHub workflow in `.github/workflows/check-id-standards.yml`.

This workflow:
1. Runs on pull requests to `main` and `develop` branches
2. Checks for ID standardization issues in the code
3. Comments on the PR with a summary of issues found
4. Fails the build if any issues are found

## Best Practices

1. Always use the `IdService` when working with IDs
2. Run the validation scripts locally before pushing code
3. Fix ID standardization issues before merging PRs
4. Use semantic IDs where possible for better readability
5. Keep the entity type prefixes consistent 