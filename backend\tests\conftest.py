import pytest
import asyncio
import os
from typing import As<PERSON><PERSON>enerator, Dict, Any, Optional
from unittest.mock import AsyncMock, MagicMock, patch

from app.main import app
from app.db.neo4j_session import get_db_session, init_db
from fastapi import FastAPI
from httpx import AsyncClient, ASGITransport

# Add command line option for container tests
def pytest_addoption(parser):
    """Add custom command-line options for pytest."""
    parser.addoption(
        "--run-container-tests", 
        action="store_true", 
        default=False, 
        help="Run tests that require Docker containers"
    )
    parser.addoption(
        "--reuse-containers", 
        action="store_true", 
        default=False, 
        help="Reuse existing Docker containers instead of creating new ones"
    )

# Skip marker for container tests
def pytest_configure(config):
    config.addinivalue_line(
        "markers", "container: mark test as requiring Docker containers"
    )

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def initialize_db():
    """Initialize the Neo4j database with constraints and initial data for testing."""
    await init_db()
    yield
    # No explicit teardown needed as we don't drop the database in Neo4j tests

@pytest.fixture
async def db() -> AsyncGenerator:
    """
    Create a fresh Neo4j session for every test.
    """
    async for session in get_db_session():
        yield session
        # Session is automatically closed after the yield due to the context manager in get_db_session

@pytest.fixture
async def client(db) -> AsyncGenerator[AsyncClient, None]:
    """
    Create a test client with the async infrastructure necessary for Neo4j.
    This runs in an async context to handle async database calls.
    """
    # Override the db dependency
    async def override_get_db():
        yield db

    app.dependency_overrides[get_db_session] = override_get_db
    
    # Create AsyncClient with ASGITransport for direct FastAPI app testing
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://testserver") as ac:
        yield ac
    
    # Clear overrides after test is complete
    app.dependency_overrides.clear()

# Common mock objects that can be used across test types
@pytest.fixture
def mock_neo4j_session():
    """Create a mock Neo4j session."""
    mock_session = AsyncMock()
    mock_session.run.return_value = AsyncMock()
    
    # Configure the run method to return a mock result
    async def mock_run(*args, **kwargs):
        mock_result = AsyncMock()
        mock_result.__aiter__.return_value = []
        return mock_result
    
    mock_session.run.side_effect = mock_run
    return mock_session

@pytest.fixture
def neo4j_record_factory():
    """Factory fixture to create Neo4j record mocks with specific values."""
    def _create_record(values: Dict[str, Any]):
        record = MagicMock()
        # Set up dictionary-like access
        record.__getitem__.side_effect = lambda key: values.get(key)
        record.get.side_effect = lambda key, default=None: values.get(key, default)
        # Set up attribute access
        for key, value in values.items():
            setattr(record, key, value)
        return record
    return _create_record

@pytest.fixture
def create_async_iterator():
    """Create an async iterator from a list."""
    def _create_iterator(items):
        async def _async_iterator():
            for item in items:
                yield item
        return _async_iterator()
    return _create_iterator

@pytest.fixture
def configure_mock_neo4j_run():
    """Configure the run method of a mock Neo4j session to return specific results for different queries."""
    def _configure(mock_session, query_results):
        """
        Configure mock_session.run to return different results based on the query.
        
        Args:
            mock_session: The mock Neo4j session
            query_results: A dictionary mapping query substrings to results
        """
        async def side_effect(query, **params):
            mock_result = AsyncMock()
            
            # Find a matching query substring
            for key, result in query_results.items():
                if key in query:
                    mock_result.__aiter__.return_value = result
                    return mock_result
            
            # Default empty result
            mock_result.__aiter__.return_value = []
            return mock_result
            
        mock_session.run.side_effect = side_effect
        return mock_session
    
    return _configure

# Add a fixture for temporary environment variables
@pytest.fixture
def env_vars():
    """Set temporary environment variables for tests."""
    original_values = {}
    
    def _set_vars(**kwargs):
        for key, value in kwargs.items():
            if key in os.environ:
                original_values[key] = os.environ[key]
            os.environ[key] = value
    
    yield _set_vars
    
    # Restore original values
    for key in original_values:
        os.environ[key] = original_values[key]

# Define a custom marker to skip tests if containers are not available
def pytest_collection_modifyitems(config, items):
    if not config.getoption("--run-container-tests"):
        skip_marker = pytest.mark.skip(reason="Need --run-container-tests option to run")
        for item in items:
            if "container" in item.keywords:
                item.add_marker(skip_marker) 