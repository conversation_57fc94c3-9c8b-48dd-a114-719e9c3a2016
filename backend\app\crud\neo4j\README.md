# Neo4j CRUD Operations

This directory contains the Neo4j implementation of CRUD (Create, Read, Update, Delete) operations for the Tahimoto application. These modules replace the previous SQLAlchemy-based CRUD operations.

## Overview

The Neo4j CRUD operations are designed to work with a graph database, taking advantage of the relationship-centric nature of Neo4j. This implementation focuses on:

1. Basic CRUD operations for nodes
2. Relationship management between nodes
3. Graph traversal for advanced queries
4. Performance optimization for common operations
5. Consistent ID standardization and handling

## Module Structure

- `__init__.py` - Exports the CRUD instances for easy importing
- `neo4j_base.py` - Base class with common CRUD operations
- `theme.py` - Theme-specific CRUD operations
- `story.py` - Story-specific CRUD operations

## Base CRUD Operations

The `CRUDBase` class in `neo4j_base.py` provides the following operations:

- `get(id)` - Get a node by ID
- `get_multi(skip, limit)` - Get multiple nodes with pagination
- `create(obj_in)` - Create a new node
- `update(id, obj_in)` - Update an existing node
- `remove(id)` - Delete a node

## Theme CRUD Operations

The `CRUDTheme` class in `theme.py` extends the base class with theme-specific operations:

- `standardize_id(id_value, prefix)` - Ensures IDs have consistent prefix formatting
- `get_by_name(name)` - Get a theme by name
- `get_multi(skip, limit, status)` - Get themes with optional status filtering
- `create_relationship(source_type, source_id, theme_id, ...)` - Create a HAS_THEME relationship
- `update_relationship(source_type, source_id, theme_id, ...)` - Update a HAS_THEME relationship
- `remove_relationship(source_type, source_id, theme_id)` - Remove a HAS_THEME relationship
- `get_relationship(source_type, source_id, theme_id)` - Get a specific HAS_THEME relationship
- `get_analysis(source_type, source_id)` - Get all theme mappings for a source
- `get_mappings_by_type(source_type, source_id, mapping_type)` - Get theme mappings of a specific type
- `find_similar_media(source_type, source_id, target_types, limit)` - Find media with similar themes
- `get_theme_network(theme_id, depth, limit)` - Get the network of related themes

All theme operations implement consistent ID handling, ensuring that:
- Theme IDs always have a "theme_" prefix
- Story IDs always have a "story_" prefix
- IDs are standardized both for input parameters and returned data

## Story CRUD Operations

The `CRUDStory` class in `story.py` extends the base class with story-specific operations:

- `get_by_external_id(external_id, source)` - Get a story by external ID and source
- `create_or_update(obj_in)` - Create a new story or update if it exists
- `is_stale(story_data, max_age_days)` - Check if story data is stale

## Usage Examples

### Creating a Theme

```python
from app.crud.neo4j import theme

async def create_new_theme(session, name, description):
    theme_data = {
        "name": name,
        "description": description,
        "status": "MAPPED"
    }
    return await theme.create(session, obj_in=theme_data)
```

### Creating a Theme Mapping

```python
from app.crud.neo4j import theme

async def map_theme_to_anime(session, anime_id, theme_id, strength=0.8):
    return await theme.create_relationship(
        session,
        source_type="Anime",
        source_id=anime_id,
        theme_id=theme_id,
        mapping_type="primary",
        mapping_strength=strength,
        source="manual"
    )
```

### Finding Similar Media

```python
from app.crud.neo4j import theme

async def find_similar_anime(session, anime_id, limit=5):
    return await theme.find_similar_media(
        session,
        source_type="Anime",
        source_id=anime_id,
        target_types=["Anime"],
        limit=limit
    )
```

## Cypher Query Examples

### Get Theme by Name

```cypher
MATCH (t:Theme)
WHERE t.name = $name
RETURN t
```

### Create Theme Mapping Relationship

```cypher
MATCH (s:{source_type} {id: $source_id})
MATCH (t:Theme {id: $theme_id})
MERGE (s)-[r:HAS_THEME]->(t)
SET r.mapping_type = $mapping_type,
    r.mapping_strength = $mapping_strength,
    r.source = $source,
    r.notes = $notes,
    r.created_at = datetime(),
    r.updated_at = datetime()
RETURN s, t, r
```

### Find Similar Media

```cypher
MATCH (source:{source_type} {id: $source_id})-[r1:HAS_THEME]->(t:Theme)<-[r2:HAS_THEME]-(target)
WHERE target:{target_types} AND target.id <> $source_id
WITH target, sum(r1.mapping_strength * r2.mapping_strength) AS similarity_score
ORDER BY similarity_score DESC
LIMIT $limit
RETURN target, similarity_score
```

## Neo4j Schema

### Nodes

- `Theme`: Represents a thematic element
  - Properties: `id`, `name`, `description`, `status`, `created_at`, `updated_at`

- `Story`: Represents a media item (anime, book, movie, etc.)
  - Properties: `id`, `external_id`, `source`, `title`, `description`, `genres`, `tags`, `created_at`, `updated_at`

### Relationships

- `HAS_THEME`: Connects a media item to a theme
  - Properties: `mapping_type`, `mapping_strength`, `source`, `notes`, `created_at`, `updated_at`

## Benefits Over SQLAlchemy

1. **Relationship-Centric**: Theme mappings are represented as relationships rather than separate entities
2. **Performance**: Graph traversal is more efficient for relationship-heavy queries
3. **Flexibility**: Easier to add new relationship types and properties
4. **Intuitive**: The data model more closely matches the conceptual model
5. **Advanced Queries**: Support for graph-specific operations like similarity scoring and network analysis 
6. **Consistent ID Handling**: Standardized ID prefixing and validation throughout the API 