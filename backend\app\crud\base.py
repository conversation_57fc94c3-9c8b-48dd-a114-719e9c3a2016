from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.logging import get_logger
from app.db.base_class import Base

logger = get_logger("crud_base")

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        **Parameters**
        * `model`: A SQLAlchemy model class
        """
        self.model = model
        self.logger = get_logger(f"crud_{model.__name__.lower()}")

    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """Get a record by ID."""
        try:
            self.logger.debug(f"Fetching {self.model.__name__} with ID: {id}")
            stmt = select(self.model).filter(self.model.id == id)
            result = await db.execute(stmt)
            obj = result.scalar_one_or_none()
            
            if obj:
                self.logger.debug(f"Found {self.model.__name__}: {id}")
            else:
                self.logger.debug(f"No {self.model.__name__} found with ID: {id}")
                
            return obj
            
        except Exception as e:
            self.logger.error(f"Error fetching {self.model.__name__} {id}: {str(e)}")
            raise

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """Get multiple records."""
        try:
            self.logger.debug(
                f"Fetching multiple {self.model.__name__} records "
                f"(skip={skip}, limit={limit})"
            )
            stmt = select(self.model).offset(skip).limit(limit)
            result = await db.execute(stmt)
            items = result.scalars().all()
            self.logger.debug(f"Found {len(items)} {self.model.__name__} records")
            return items
            
        except Exception as e:
            self.logger.error(
                f"Error fetching multiple {self.model.__name__} records: {str(e)}"
            )
            raise

    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """Create a new record."""
        try:
            self.logger.info(f"Creating new {self.model.__name__}")
            obj_in_data = jsonable_encoder(obj_in)
            db_obj = self.model(**obj_in_data)
            
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            
            self.logger.info(f"Created {self.model.__name__}: {db_obj.id}")
            return db_obj
            
        except Exception as e:
            self.logger.error(f"Error creating {self.model.__name__}: {str(e)}")
            await db.rollback()
            raise

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """Update a record."""
        try:
            self.logger.info(f"Updating {self.model.__name__}: {db_obj.id}")
            obj_data = jsonable_encoder(db_obj)
            
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.dict(exclude_unset=True)
                
            # Log significant changes
            changes = []
            for field in obj_data:
                if field in update_data and update_data[field] != obj_data[field]:
                    changes.append(f"{field}: {obj_data[field]} -> {update_data[field]}")
            
            if changes:
                self.logger.debug(f"Changes: {', '.join(changes)}")
            
            for field in obj_data:
                if field in update_data:
                    setattr(db_obj, field, update_data[field])
                    
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            
            self.logger.info(f"Updated {self.model.__name__}: {db_obj.id}")
            return db_obj
            
        except Exception as e:
            self.logger.error(f"Error updating {self.model.__name__} {db_obj.id}: {str(e)}")
            await db.rollback()
            raise

    async def remove(self, db: AsyncSession, *, id: int) -> ModelType:
        """Delete a record."""
        try:
            self.logger.info(f"Removing {self.model.__name__} with ID: {id}")
            stmt = select(self.model).filter(self.model.id == id)
            result = await db.execute(stmt)
            obj = result.scalar_one_or_none()
            
            if obj:
                await db.delete(obj)
                await db.commit()
                self.logger.info(f"Removed {self.model.__name__}: {id}")
            else:
                self.logger.warning(f"No {self.model.__name__} found to remove with ID: {id}")
                
            return obj
            
        except Exception as e:
            self.logger.error(f"Error removing {self.model.__name__} {id}: {str(e)}")
            await db.rollback()
            raise 