"""
Neo4j database connection and session management.
"""
from typing import Generator, Any
import logging
from neo4j import GraphDatabase, Driver, AsyncDriver, AsyncSession, AsyncGraphDatabase
from neo4j.exceptions import Neo4jError, ServiceUnavailable, TransientError
from app.core.config import settings

logger = logging.getLogger(__name__)

# Create Neo4j driver with transaction retry configuration
try:
    logger.info("Initializing Neo4j driver")
    # AsyncGraphDatabase is the async version of the driver
    driver = AsyncGraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD),
        # Configure max connection pool size
        max_connection_pool_size=50,
        # Configure connection acquisition timeout
        connection_acquisition_timeout=60,
        # Configure maximum transaction retry time
        max_transaction_retry_time=15.0
    )
    logger.info("Neo4j driver initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Neo4j driver: {str(e)}")
    raise


async def init_db():
    """
    Initialize the database with schema constraints.
    This should be called when the application starts.
    """
    logger.info("Initializing Neo4j database schema")
    
    try:
        async with driver.session() as session:
            # Create constraints for Theme nodes
            await session.execute_write(lambda tx: tx.run("""
                CREATE CONSTRAINT theme_id IF NOT EXISTS
                FOR (t:Theme) REQUIRE t.id IS UNIQUE
            """))
            
            # Create constraints for Story nodes
            await session.execute_write(lambda tx: tx.run("""
                CREATE CONSTRAINT story_id IF NOT EXISTS
                FOR (s:Story) REQUIRE s.id IS UNIQUE
            """))
            
            # Create constraints for Genre nodes
            await session.execute_write(lambda tx: tx.run("""
                CREATE CONSTRAINT genre_name IF NOT EXISTS
                FOR (g:Genre) REQUIRE g.name IS UNIQUE
            """))
            
            # Create constraints for Tag nodes
            await session.execute_write(lambda tx: tx.run("""
                CREATE CONSTRAINT tag_name IF NOT EXISTS
                FOR (t:Tag) REQUIRE t.name IS UNIQUE
            """))
            
            logger.info("Neo4j schema initialized successfully")
    except Neo4jError as e:
        logger.error(f"Neo4j schema initialization error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during Neo4j schema initialization: {str(e)}")
        raise


async def get_session() -> AsyncSession:
    """
    Get a Neo4j session without dependency injection.
    This is useful for non-request contexts like scheduled tasks.
    """
    logger.debug("Creating new Neo4j session directly")
    return driver.session()


async def get_db_session() -> Generator[AsyncSession, Any, None]:
    """
    FastAPI dependency that provides a Neo4j session.
    Ensures proper handling of session lifecycle and error conditions.
    """
    session = None
    try:
        logger.debug("Creating new Neo4j session")
        # Create session with transaction configuration
        session = driver.session(
            # Configure default access mode to read for safety
            default_access_mode="READ",
            # Configure fetch size for queries with large result sets
            fetch_size=1000,
            # Configure database to use (default is set in connection string)
            database=None,
            # Configure bookmark manager (for causal consistency)
            bookmarks=None
        )
        yield session
    except TransientError as e:
        logger.warning(f"Neo4j transient error: {str(e)}. This may require retrying the operation.")
        if session:
            await session.close()
        # Create a new session and yield it
        session = driver.session()
        yield session
    except Neo4jError as e:
        logger.error(f"Neo4j session error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in Neo4j session: {str(e)}")
        raise
    finally:
        if session:
            try:
                logger.debug("Closing Neo4j session")
                await session.close()
            except Exception as e:
                logger.error(f"Error closing Neo4j session: {str(e)}")


async def get_write_session() -> Generator[AsyncSession, Any, None]:
    """
    FastAPI dependency that provides a Neo4j session with write access.
    This should be used only for operations that need to modify data.
    """
    session = None
    try:
        logger.debug("Creating new Neo4j write session")
        # Create session with write access
        session = driver.session(
            # Configure default access mode to WRITE
            default_access_mode="WRITE",
            # Configure fetch size for queries with large result sets
            fetch_size=1000,
            # Configure database to use (default is set in connection string)
            database=None,
            # Configure bookmark manager (for causal consistency)
            bookmarks=None,
            # Add explicit transaction configuration for better resilience
            # Retry failed transactions up to 3 times with exponential backoff
            max_transaction_retry_time=15.0
        )
        yield session
    except TransientError as e:
        logger.warning(f"Neo4j transient error: {str(e)}. This may require retrying the operation.")
        if session:
            try:
                await session.close()
            except Exception as e_close:
                logger.warning(f"Error closing Neo4j session after transient error: {str(e_close)}")
        
        # Create a new session and yield it
        session = driver.session(default_access_mode="WRITE")
        yield session
    except Neo4jError as e:
        logger.error(f"Neo4j session error: {str(e)}")
        if "terminated" in str(e) or "Neo.ClientError.Transaction.Terminated" in str(e):
            logger.warning("Neo4j transaction was terminated. This might be due to concurrent operations or timeouts.")
            # Don't raise the exception - let the caller handle it based on context
            if session:
                try:
                    await session.close()
                except Exception as e_close:
                    logger.warning(f"Error closing Neo4j session after termination: {str(e_close)}")
            
            # Create a new session as a fallback
            session = driver.session(default_access_mode="WRITE")
            yield session
        else:
            # For other Neo4j errors, still raise
            raise
    except Exception as e:
        logger.error(f"Unexpected error in Neo4j session: {str(e)}")
        raise
    finally:
        if session:
            try:
                logger.debug("Closing Neo4j write session")
                await session.close()
            except Exception as e:
                logger.error(f"Error closing Neo4j write session: {str(e)}")


async def close_db_connection():
    """
    Close the Neo4j driver connection.
    This should be called when the application shuts down.
    """
    logger.info("Closing Neo4j driver connection")
    await driver.close()


async def verify_connectivity():
    """
    Verify that we can connect to the Neo4j database.
    """
    try:
        logger.debug("Verifying Neo4j connectivity")
        async with driver.session() as session:
            result = await session.execute_read(lambda tx: tx.run("RETURN 1 as n"))
            records = await result.records()
            if records and len(records) > 0 and records[0].get("n") == 1:
                logger.info("Successfully connected to Neo4j")
                return True
            else:
                logger.error("Failed to verify Neo4j connectivity")
                return False
    except ServiceUnavailable:
        logger.error("Neo4j service unavailable")
        return False
    except Exception as e:
        logger.error(f"Error verifying Neo4j connectivity: {str(e)}")
        return False 