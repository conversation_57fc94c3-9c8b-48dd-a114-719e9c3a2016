import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import aiohttp
from app.core.config import settings
from app.core.logging import get_logger

logger = logging.getLogger(__name__)

class AniListService:
    """Service for interacting with the AniList GraphQL API."""
    
    def __init__(self):
        self.base_url = "https://graphql.anilist.co"
        
    async def _make_request(self, query: str, variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make a request to the AniList GraphQL API."""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.base_url,
                json={"query": query, "variables": variables}
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"AniList API error: {error_text}")
                return await response.json()

    def _format_media_type(self, media_type: Optional[str]) -> str:
        """Format media type to match our MediaType enum."""
        if not media_type:
            logger.debug("No media type provided, defaulting to TV")
            return "TV"  # Default to TV as fallback
        
        # Log the incoming value
        logger.debug(f"Formatting media type: {media_type}")
        
        # Normalize the input
        media_type = str(media_type).upper().strip().replace(" ", "_")
        logger.debug(f"Normalized media type: {media_type}")
        
        # Handle special cases first
        media_type_map = {
            "TV": "TV",
            "TV_SHORT": "TV",
            "TV_SERIES": "TV",
            "TV_SHOW": "TV",
            "TELEVISION": "TV",
            "SERIES": "TV",
            "MOVIE": "MOVIE",
            "MOVIES": "MOVIE",
            "FILM": "MOVIE",
            "THEATRICAL": "MOVIE",
            "SPECIAL": "SPECIAL",
            "OVA": "OVA",
            "ONA": "ONA",
            "WEB_SERIES": "ONA",
            "WEB_SHOW": "ONA",
            "MUSIC": "SPECIAL",
            "MANGA": "MANGA",
            "NOVEL": "NOVEL",
            "ONE_SHOT": "MANGA",
            "DOUJIN": "MANGA",
            "MANHWA": "MANGA",
            "MANHUA": "MANGA",
            "LIGHT_NOVEL": "NOVEL",
            "UNKNOWN": "TV",  # Default unknown to TV
        }
        
        # Try to find an exact match first
        if media_type in media_type_map:
            result = media_type_map[media_type]
            logger.debug(f"Found exact match for media type {media_type} -> {result}")
            return result
        
        # If no exact match, try to find a partial match
        for key, value in media_type_map.items():
            if key in media_type:
                logger.debug(f"Found partial match for media type {media_type} using key {key} -> {value}")
                return value
        
        logger.debug(f"No match found for media type {media_type}, defaulting to TV")
        return "TV"  # Default to TV if no match found

    def _format_status(self, status: Optional[str]) -> str:
        """Format status to match our MediaStatus enum."""
        if not status:
            logger.debug("No status provided, defaulting to RELEASING")
            return "RELEASING"  # Default to RELEASING as fallback
        
        # Log the incoming value
        logger.debug(f"Formatting status: {status}")
        
        # Normalize the input
        status = str(status).upper().strip().replace(" ", "_")
        logger.debug(f"Normalized status: {status}")
        
        status_map = {
            "FINISHED": "FINISHED",
            "RELEASING": "RELEASING",
            "NOT_YET_RELEASED": "NOT_YET_RELEASED",
            "CANCELLED": "CANCELLED",
            "HIATUS": "HIATUS",
            "COMPLETED": "FINISHED",
            "CURRENTLY_AIRING": "RELEASING",
            "NOT_YET_AIRED": "NOT_YET_RELEASED",
            "UNKNOWN": "RELEASING",  # Default unknown to RELEASING
            "ENDED": "FINISHED",
            "DONE": "FINISHED",
            "ONGOING": "RELEASING",
            "AIRING": "RELEASING",
            "CURRENT": "RELEASING",
            "UPCOMING": "NOT_YET_RELEASED",
            "UNRELEASED": "NOT_YET_RELEASED",
            "ANNOUNCED": "NOT_YET_RELEASED",
            "CANCELED": "CANCELLED",
            "DISCONTINUED": "CANCELLED",
            "ON_HOLD": "HIATUS",
            "PAUSED": "HIATUS"
        }
        
        # Try to find an exact match first
        if status in status_map:
            result = status_map[status]
            logger.debug(f"Found exact match for status {status} -> {result}")
            return result
        
        # If no exact match, try to find a partial match
        for key, value in status_map.items():
            if key in status:
                logger.debug(f"Found partial match for status {status} using key {key} -> {value}")
                return value
        
        logger.debug(f"No match found for status {status}, defaulting to RELEASING")
        return "RELEASING"  # Default to RELEASING if no match found

    def _format_season(self, season: Optional[str], year: Optional[int]) -> str:
        """Format season to be user-friendly."""
        if not season:
            return str(year) if year else "Unknown"
        
        season = season.lower()
        season_map = {
            "winter": "Winter",
            "spring": "Spring",
            "summer": "Summer",
            "fall": "Fall"
        }
        formatted_season = season_map.get(season, season.title())
        return f"{formatted_season} {year}" if year else formatted_season

    def _parse_date(self, date_dict: Dict[str, Any]) -> Optional[datetime]:
        """Parse a date dictionary into a datetime object."""
        try:
            if all(date_dict.get(k) for k in ["year", "month", "day"]):
                return datetime(
                    year=date_dict.get("year"),
                    month=date_dict.get("month"),
                    day=date_dict.get("day")
                )
        except (ValueError, TypeError):
            pass
        return None

    async def transform_to_story(self, anilist_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform AniList media data to our Story format."""
        media = anilist_data.get("data", {}).get("Media", {})
        if not media:
            raise ValueError("Invalid AniList response format")

        # Get the media type from both type and format fields
        media_type = media.get("type", "ANIME")  # ANIME or MANGA
        media_format = media.get("format")  # More specific format like TV, MOVIE, etc.
        
        # Use format if available, otherwise use type
        final_media_type = self._format_media_type(media_format if media_format else media_type)

        # Parse dates
        start_date = media.get("startDate", {})
        end_date = media.get("endDate", {})
        
        start_datetime = self._parse_date(start_date)
        end_datetime = self._parse_date(end_date)

        # Transform relations with enhanced detail
        logger.debug(f"Raw relations data: {media.get('relations', {})}")
        logger.debug(f"Raw relations edges: {media.get('relations', {}).get('edges', [])}")
        logger.debug(f"Raw media data: {media}")

        # Check if relations exist in the response
        if "relations" not in media:
            logger.warning("No relations field found in media data")
        elif not media.get("relations", {}).get("edges"):
            logger.warning("No edges found in relations data")

        relations = [
            {
                "id": f"story_{edge.get('node', {}).get('id')}" if edge.get('node', {}).get('id') else None,
                "title": (
                    edge.get("node", {}).get("title", {}).get("english") or 
                    edge.get("node", {}).get("title", {}).get("romaji")
                ),
                "type": edge.get("relationType", "").replace("_", " ").title(),
                "media_type": edge.get("node", {}).get("type", "UNKNOWN"),
                "format": edge.get("node", {}).get("format", "UNKNOWN"),
                "status": self._format_status(edge.get("node", {}).get("status")),
                "source": edge.get("node", {}).get("source", "UNKNOWN"),
                "average_score": edge.get("node", {}).get("averageScore"),
                "popularity": edge.get("node", {}).get("popularity"),
                "cover_image": edge.get("node", {}).get("coverImage", {}).get("medium"),
                "cover_image_large": edge.get("node", {}).get("coverImage", {}).get("large"),
                "description": edge.get("node", {}).get("description", "").replace("<br>", "\n").replace("<br/>", "\n") if edge.get("node", {}).get("description") else None,
                "native_title": edge.get("node", {}).get("title", {}).get("native"),
                "genres": edge.get("node", {}).get("genres", []),
                "tags": [
                    {
                        "name": tag.get("name"),
                        "rank": tag.get("rank"),
                        "category": tag.get("category")
                    }
                    for tag in edge.get("node", {}).get("tags", [])
                    if tag and tag.get("name")
                ]
            }
            for edge in media.get("relations", {}).get("edges", [])
            if edge and edge.get("node") and edge.get("node", {}).get("title")
        ]

        # Log the transformed relations
        logger.debug(f"Transformed relations: {relations}")
        logger.debug(f"Number of relations: {len(relations)}")

        # Log each relation's transformation
        for i, edge in enumerate(media.get("relations", {}).get("edges", [])):
            if edge and edge.get("node"):
                logger.debug(f"Processing relation {i + 1}:")
                logger.debug(f"  - ID: {edge.get('node', {}).get('id')}")
                logger.debug(f"  - Title: {edge.get('node', {}).get('title')}")
                logger.debug(f"  - Type: {edge.get('relationType')}")
            else:
                logger.debug(f"Relation {i + 1} is None or empty")

        # Transform recommendations
        recommendations = [
            {
                "id": f"story_{rec.get('mediaRecommendation', {}).get('id')}" if rec.get('mediaRecommendation', {}).get('id') else None,
                "title": (
                    rec.get("mediaRecommendation", {}).get("title", {}).get("english") or 
                    rec.get("mediaRecommendation", {}).get("title", {}).get("romaji")
                ),
                "type": "RECOMMENDATION",
                "media_type": rec.get("mediaRecommendation", {}).get("type", "UNKNOWN"),
                "format": rec.get("mediaRecommendation", {}).get("format", "UNKNOWN"),
                "status": self._format_status(rec.get("mediaRecommendation", {}).get("status")),
                "source": rec.get("mediaRecommendation", {}).get("source", "UNKNOWN"),
                "average_score": rec.get("mediaRecommendation", {}).get("averageScore"),
                "popularity": rec.get("mediaRecommendation", {}).get("popularity"),
                "cover_image": rec.get("mediaRecommendation", {}).get("coverImage", {}).get("medium"),
                "cover_image_large": rec.get("mediaRecommendation", {}).get("coverImage", {}).get("large"),
                "description": rec.get("mediaRecommendation", {}).get("description", "").replace("<br>", "\n").replace("<br/>", "\n") if rec.get("mediaRecommendation", {}).get("description") else None,
                "native_title": rec.get("mediaRecommendation", {}).get("title", {}).get("native"),
                "genres": rec.get("mediaRecommendation", {}).get("genres", []),
                "tags": [
                    {
                        "name": tag.get("name"),
                        "rank": tag.get("rank"),
                        "category": tag.get("category")
                    }
                    for tag in rec.get("mediaRecommendation", {}).get("tags", [])
                    if tag and tag.get("name")
                ],
                "start_date": self._parse_date(rec.get("mediaRecommendation", {}).get("startDate", {})).isoformat() if self._parse_date(rec.get("mediaRecommendation", {}).get("startDate", {})) else None,
                "end_date": self._parse_date(rec.get("mediaRecommendation", {}).get("endDate", {})).isoformat() if self._parse_date(rec.get("mediaRecommendation", {}).get("endDate", {})) else None,
                "studios": [
                    {
                        "id": studio.get("id"),
                        "name": studio.get("name"),
                        "is_animation_studio": studio.get("isAnimationStudio", False)
                    }
                    for studio in rec.get("mediaRecommendation", {}).get("studios", {}).get("nodes", [])
                    if studio and studio.get("name")
                ],
                "recommendation_strength": rec.get("rating"),
                "recommended_by": rec.get("user", {}).get("name"),
                "user_rating": None if rec.get("userRating") == "NO_RATING" else rec.get("userRating")
            }
            for rec in media.get("recommendations", {}).get("nodes", [])
            if rec and rec.get("mediaRecommendation") and rec.get("mediaRecommendation", {}).get("title")
        ]

        # Extract and format metadata
        story_metadata = {
            "genres": media.get("genres", []),
            "tags": [
                {
                    "name": tag.get("name", ""),
                    "description": tag.get("description"),
                    "category": tag.get("category", "Other"),
                    "rank": tag.get("rank"),
                    "isGeneralSpoiler": tag.get("isGeneralSpoiler", False),
                    "isMediaSpoiler": tag.get("isMediaSpoiler", False)
                }
                for tag in media.get("tags", []) if tag and tag.get("name")
            ],
            "studios": [
                {
                    "id": f"studio_{studio.get('id')}" if studio.get('id') else None,
                    "name": studio.get("name", "Unknown Studio"),
                    "is_animation_studio": studio.get("isAnimationStudio", False)
                }
                for studio in media.get("studios", {}).get("nodes", [])
                if studio and studio.get("name")
            ],
            "relations": relations,
            "recommendations": recommendations,
            "characters": [
                {
                    "id": f"character_{char_edge.get('node', {}).get('id')}" if char_edge.get('node', {}).get('id') else None,
                    "name": char_edge.get('node', {}).get('name', {}).get('full', 'Unknown Character'),
                    "first_name": char_edge.get('node', {}).get('name', {}).get('first'),
                    "last_name": char_edge.get('node', {}).get('name', {}).get('last'),
                    "gender": char_edge.get('node', {}).get('gender'),
                    "age": char_edge.get('node', {}).get('age'),
                    "role": char_edge.get('role', 'BACKGROUND'),
                    "description": char_edge.get('node', {}).get('description'),
                    "image_medium": char_edge.get('node', {}).get('image', {}).get('medium'),
                    "image_large": char_edge.get('node', {}).get('image', {}).get('large')
                }
                for char_edge in media.get("characters", {}).get("edges", [])
                if char_edge and char_edge.get('node') and char_edge.get('node', {}).get('name', {}).get('full')
            ],
            "staff": [
                {
                    "id": f"staff_{staff_edge.get('node', {}).get('id')}" if staff_edge.get('node', {}).get('id') else None,
                    "name": staff_edge.get('node', {}).get('name', {}).get('full', 'Unknown Staff'),
                    "first_name": staff_edge.get('node', {}).get('name', {}).get('first'),
                    "last_name": staff_edge.get('node', {}).get('name', {}).get('last'),
                    "gender": staff_edge.get('node', {}).get('gender'),
                    "age": staff_edge.get('node', {}).get('age'),
                    "role": staff_edge.get('role', 'Unknown'),
                    "description": staff_edge.get('node', {}).get('description'),
                    "image_medium": staff_edge.get('node', {}).get('image', {}).get('medium'),
                    "image_large": staff_edge.get('node', {}).get('image', {}).get('large')
                }
                for staff_edge in media.get("staff", {}).get("edges", [])
                if staff_edge and staff_edge.get('node') and staff_edge.get('node', {}).get('name', {}).get('full')
            ],
            "start_date": start_datetime.isoformat() if start_datetime else None,
            "end_date": end_datetime.isoformat() if end_datetime else None,
            "anilist_id": media.get("id")  # Store the original AniList ID
        }

        # Get current time for created_at and updated_at
        current_time = datetime.utcnow()

        return {
            "external_id": f"story_{media.get('id')}" if media.get('id') else None,
            "title_english": media.get("title", {}).get("english"),
            "title_romaji": media.get("title", {}).get("romaji", "Unknown Title"),
            "title_native": media.get("title", {}).get("native"),
            "synopsis": media.get("description", "").replace("<br>", "\n").replace("<br/>", "\n") if media.get("description") else None,
            "media_type": final_media_type,
            "source": "anilist",  # Always set source for main story
            "cover_image_large": media.get("coverImage", {}).get("large"),
            "cover_image_medium": media.get("coverImage", {}).get("medium"),
            "banner_image": media.get("bannerImage"),
            "status": self._format_status(media.get("status")),
            "popularity": media.get("popularity"),
            "average_score": media.get("averageScore"),
            "episode_count": media.get("episodes"),
            "episode_duration": media.get("duration"),
            "start_date": start_datetime,
            "end_date": end_datetime,
            "season": self._format_season(media.get("season"), media.get("seasonYear")),
            # Add top-level genres and tags for frontend compatibility
            "genres": media.get("genres", []),
            "tags": story_metadata.get("tags", []),
            "story_metadata": story_metadata,
            "created_at": current_time,
            "updated_at": current_time
        }

    async def get_anime_details(self, media_id: str) -> Dict[str, Any]:
        """Get detailed information about an anime."""
        # Remove 'story_' prefix if present
        if media_id.startswith("story_"):
            media_id = media_id[6:]
            
        query = """
        query ($id: Int) {
            Media(id: $id, type: ANIME) {
                id
                title {
                    romaji
                    english
                    native
                }
                type
                format
                status
                description
                startDate {
                    year
                    month
                    day
                }
                endDate {
                    year
                    month
                    day
                }
                season
                seasonYear
                episodes
                duration
                coverImage {
                    large
                    medium
                }
                bannerImage
                genres
                averageScore
                popularity
                tags {
                    id
                    name
                    description
                    category
                    rank
                    isGeneralSpoiler
                    isMediaSpoiler
                }
                studios {
                    nodes {
                        id
                        name
                        isAnimationStudio
                    }
                }
                relations {
                    edges {
                        relationType
                        node {
                            id
                            type
                            format
                            status
                            source
                            averageScore
                            popularity
                            title {
                                romaji
                                english
                                native
                            }
                            coverImage {
                                medium
                                large
                            }
                            description(asHtml: false)
                            genres
                            tags {
                                name
                                rank
                                category
                            }
                        }
                    }
                }
                recommendations(sort: RATING_DESC) {
                    nodes {
                        rating
                        userRating
                        user {
                            name
                        }
                        mediaRecommendation {
                            id
                            type
                            format
                            status
                            source
                            averageScore
                            popularity
                            title {
                                romaji
                                english
                                native
                            }
                            coverImage {
                                medium
                                large
                            }
                            description(asHtml: false)
                            genres
                            tags {
                                name
                                rank
                                category
                            }
                        }
                    }
                }
                characters(sort: ROLE, perPage: 10) {
                    edges {
                        role
                        node {
                            id
                            name {
                                full
                                first
                                last
                            }
                            gender
                            age
                            description
                            image {
                                medium
                                large
                            }
                        }
                    }
                }
                staff(sort: RELEVANCE, perPage: 10) {
                    edges {
                        role
                        node {
                            id
                            name {
                                full
                                first
                                last
                            }
                            gender
                            age
                            description
                            image {
                                medium
                                large
                            }
                        }
                    }
                }
            }
        }
        """
        
        try:
            response = await self._make_request(query, {"id": int(media_id)})
            logger.debug(f"Raw AniList response: {response}")
            return response
        except ValueError as e:
            raise ValueError(f"Failed to get anime details: {str(e)}")

    async def search_anime(
        self,
        query: str,
        page: int = 1,
        per_page: int = 10,
        media_type: str = "all",
        sort: str = "popularity"
    ) -> Dict[str, Any]:
        """Search for anime using the AniList API."""
        logger.info(f"[AniList] Starting anime search with query: {query}, page: {page}, per_page: {per_page}, media_type: {media_type}, sort: {sort}")
        
        # Convert sort parameter to AniList format
        sort_value = "POPULARITY_DESC"
        if sort == "score":
            sort_value = "SCORE_DESC"
        elif sort == "title":
            sort_value = "TITLE_ROMAJI"
        elif sort == "newest":
            sort_value = "START_DATE_DESC"
        
        # Build the format condition
        format_condition = ""
        if media_type and media_type != "all":
            format_condition = f", format: {media_type.upper().replace(' ', '_')}"
        
        search_query = f"""
        query ($page: Int, $perPage: Int, $search: String) {{
            Page(page: $page, perPage: $perPage) {{
                pageInfo {{
                    total
                    currentPage
                    lastPage
                    hasNextPage
                    perPage
                }}
                media(search: $search, type: ANIME{format_condition}, sort: {sort_value}) {{
                    id
                    title {{
                        romaji
                        english
                        native
                    }}
                    description
                    format
                    status
                    episodes
                    duration
                    genres
                    tags {{
                        id
                        name
                        description
                        category
                        rank
                        isGeneralSpoiler
                        isMediaSpoiler
                    }}
                    startDate {{
                        year
                        month
                        day
                    }}
                    endDate {{
                        year
                        month
                        day
                    }}
                    season
                    seasonYear
                    coverImage {{
                        large
                        medium
                    }}
                    bannerImage
                    popularity
                    averageScore
                    source
                    relations {{
                        edges {{
                            relationType
                            node {{
                                id
                                type
                                format
                                status
                                source
                                averageScore
                                popularity
                                title {{
                                    romaji
                                    english
                                    native
                                }}
                                coverImage {{
                                    medium
                                    large
                                }}
                                description(asHtml: false)
                                genres
                                tags {{
                                    name
                                    rank
                                    category
                                }}
                            }}
                        }}
                    }}
                    characters(sort: ROLE, perPage: 10) {{
                        edges {{
                            role
                            node {{
                                id
                                name {{
                                    full
                                    first
                                    last
                                }}
                                gender
                                age
                                description
                                image {{
                                    medium
                                    large
                                }}
                            }}
                        }}
                    }}
                    staff(sort: RELEVANCE, perPage: 10) {{
                        edges {{
                            role
                            node {{
                                id
                                name {{
                                    full
                                    first
                                    last
                                }}
                                gender
                                age
                                description
                                image {{
                                    medium
                                    large
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        }}
        """
        
        variables = {
            "search": query,
            "page": page,
            "perPage": per_page
        }
        
        try:
            logger.info("[AniList] Making API request for search")
            result = await self._make_request(search_query, variables)
            logger.info(f"[AniList] Received search response: {bool(result)}")
            return result
        except Exception as e:
            logger.error(f"[AniList] Search request failed: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to search anime: {str(e)}")

    async def get_recommendations(self, external_id: str, limit: int = 10) -> Dict[str, Any]:
        """
        Get recommendations for a story from AniList.
        
        Args:
            external_id: The external ID of the story to get recommendations for
            limit: Maximum number of recommendations to return
            
        Returns:
            Dictionary containing list of recommendation objects and count
        """
        # Remove 'story_' prefix if present
        if external_id.startswith("story_"):
            external_id = external_id[6:]
            
        logger.info(f"[AniList] Getting recommendations for media ID: {external_id}, limit: {limit}")
        
        query = """
        query ($id: Int, $limit: Int) {
            Media(id: $id, type: ANIME) {
                id
                title {
                    romaji
                    english
                    native
                }
                recommendations(sort: RATING_DESC, perPage: $limit) {
                    nodes {
                        rating
                        userRating
                        mediaRecommendation {
                            id
                            title {
                                romaji
                                english
                                native
                            }
                            type
                            format
                            status
                            source
                            averageScore
                            popularity
                            coverImage {
                                medium
                                large
                            }
                            description(asHtml: false)
                            genres
                            status
                            episodes
                            startDate {
                                year
                                month
                                day
                            }
                            endDate {
                                year
                                month
                                day
                            }
                        }
                    }
                }
            }
        }
        """
        
        variables = {
            "id": int(external_id),
            "limit": limit
        }
        
        try:
            logger.info("[AniList] Making API request for recommendations")
            response = await self._make_request(query, variables)
            logger.debug(f"[AniList] Raw recommendations response: {response}")
            
            media = response.get("data", {}).get("Media", {})
            if not media:
                logger.warning(f"[AniList] No media found for ID: {external_id}")
                return {
                    "recommendations": [],
                    "count": 0
                }
                
            recommendations_data = media.get("recommendations", {}).get("nodes", [])
            
            # Transform each recommendation
            recommendations = []
            for rec in recommendations_data:
                if not rec or not rec.get("mediaRecommendation"):
                    continue
                    
                media_rec = rec.get("mediaRecommendation", {})
                
                # Format dates
                start_date = self._parse_date(media_rec.get("startDate", {}))
                end_date = self._parse_date(media_rec.get("endDate", {}))
                
                # Create formatted recommendation
                recommendation = {
                    "id": f"story_{media_rec.get('id')}" if media_rec.get('id') else None,
                    "title": (
                        media_rec.get("title", {}).get("english") or 
                        media_rec.get("title", {}).get("romaji")
                    ),
                    "native_title": media_rec.get("title", {}).get("native"),
                    "media_type": self._format_media_type(media_rec.get("format")),
                    "status": self._format_status(media_rec.get("status")),
                    "synopsis": media_rec.get("description", "").replace("<br>", "\n").replace("<br/>", "\n") if media_rec.get("description") else None,
                    "cover_image": media_rec.get("coverImage", {}).get("medium"),
                    "cover_image_large": media_rec.get("coverImage", {}).get("large"),
                    "average_score": media_rec.get("averageScore"),
                    "popularity": media_rec.get("popularity"),
                    "genres": media_rec.get("genres", []),
                    "episode_count": media_rec.get("episodes"),
                    "start_date": start_date.isoformat() if start_date else None,
                    "end_date": end_date.isoformat() if end_date else None,
                    "strength": rec.get("rating", 0) * 20 if rec.get("rating") else 0  # Convert 0-5 scale to 0-100
                }
                
                recommendations.append(recommendation)
            
            logger.info(f"[AniList] Processed {len(recommendations)} recommendations")
            return {
                "recommendations": recommendations,
                "count": len(recommendations)
            }
            
        except Exception as e:
            # Handle 404 errors more gracefully - media not found
            error_msg = str(e)
            if "Not Found" in error_msg and "404" in error_msg:
                logger.warning(f"[AniList] Media ID {external_id} not found in AniList")
                return {
                    "recommendations": [],
                    "count": 0
                }
            
            # Log and re-raise other errors
            logger.error(f"[AniList] Failed to get recommendations: {error_msg}", exc_info=True)
            raise ValueError(f"Failed to get recommendations: {error_msg}")

    async def transform_media_to_story_data(self, media: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform a single AniList media item to our Story format.
        
        This method wraps a single media item in the structure expected by transform_to_story.
        It's used primarily by the search endpoint which receives individual media items.
        
        Args:
            media: An individual media item from AniList API response
            
        Returns:
            Transformed story data in our application format
        """
        logger.debug(f"Transforming media item to story data: {media.get('id')}")
        
        # Wrap the media item in the structure expected by transform_to_story
        wrapped_data = {"data": {"Media": media}}
        
        # Use the existing transform_to_story method
        return await self.transform_to_story(wrapped_data)

anilist_service = AniListService() 