# Test Scripts

This directory contains scripts for running tests and managing the test environment for the Tahimoto backend.

## Primary Script: `run_tests.ps1`

The main script for running tests is `run_tests.ps1`, which supports running different types of tests with various options.

### Usage

```powershell
.\run_tests.ps1 [options]
```

### Options

| Option | Description |
|--------|-------------|
| `-TestType` | Type of test to run: `unit`, `component`, `infrastructure`, `integration`, or `all` (default: `unit`) |
| `-RebuildContainer` | Rebuild Docker container before running tests |
| `-ReuseContainers` | Reuse existing Docker containers for integration tests |
| `-Coverage` | Include code coverage reporting |
| `-Verbose` | Show verbose test output |
| `-WithDocker` | Run tests in Docker container |
| `-TestPattern` | Run tests matching a specific pattern |
| `-TestModule` | Specify a specific test module to run |
| `-AdditionalArgs` | Additional arguments to pass to pytest |

### Examples

```powershell
# Run unit tests
.\run_tests.ps1 -TestType unit

# Run component tests with coverage
.\run_tests.ps1 -TestType component -Coverage

# Run infrastructure tests
.\run_tests.ps1 -TestType infrastructure

# Run integration tests with Docker support
.\run_tests.ps1 -TestType integration -WithDocker

# Run tests matching a specific pattern
.\run_tests.ps1 -TestType unit -TestPattern "serialization"

# Run tests with specific module
.\run_tests.ps1 -TestType component -TestModule "components/test_cache_component.py"

# Run all tests with Docker and verbose output
.\run_tests.ps1 -TestType all -WithDocker -Verbose
```

## Script Features

### Docker Integration

The script can:
- Check if Docker is running
- Build or rebuild Docker containers as needed
- Create Docker networks
- Start and stop containers
- Execute tests inside containers

### Test Configuration

The script builds pytest commands with:
- Appropriate markers for test types
- Coverage reporting options
- Pattern matching
- Module targeting

### Output Formatting

The script provides:
- Color-coded output for better readability
- Clear success/failure indicators
- Detailed test information

## Cleanup Script: `cleanup_old_scripts.ps1`

This script helps clean up old test scripts and artifacts that are no longer needed.

### Usage

```powershell
.\cleanup_old_scripts.ps1 [options]
```

## Best Practices

1. **Use the Script**: Instead of running pytest directly, use the script for consistent test execution
2. **Specify Test Type**: Always specify which type of test you want to run
3. **Use Coverage**: Enable coverage reporting to track test coverage
4. **Docker for Integration**: Use the `-WithDocker` flag for integration tests to ensure a clean environment
5. **Target Specific Tests**: Use `-TestPattern` to run specific tests during development

## Main Test Script

The primary script for running tests is `run_tests.ps1`, which is a PowerShell script that provides a flexible interface for running different types of tests.

### Usage

```powershell
# Run all tests
./run_tests.ps1

# Run only component tests
./run_tests.ps1 -TestType component

# Run API tests
./run_tests.ps1 -TestType api

# Run infrastructure tests
./run_tests.ps1 -TestType infrastructure

# Rebuild containers and run all tests
./run_tests.ps1 -RebuildContainer
```

### Available Test Types

- `all`: Run all tests (default)
- `component`: Run component-specific tests
- `api`: Run API endpoint tests
- `resolver`: Run GraphQL resolver tests
- `neo4j`: Run Neo4j connection tests
- `anilist`: Run AniList integration tests
- `infrastructure`: Run all infrastructure tests

## Previous Scripts

The following scripts have been consolidated into `run_tests.ps1`:

- `run_component_tests.ps1`
- `run_docker_tests.ps1`
- `run_docker_tests.bat`
- `run_anilist_tests.bat`
- `test_in_docker.ps1`

These scripts are no longer necessary and can be removed from the project root and backend directory.

## Directory Structure

The testing structure is organized as follows:

```
backend/
  └── tests/
      ├── api/                    # API tests
      │   └── stories/            # Story API tests
      │       └── components/     # Component-specific tests
      ├── infrastructure/         # Infrastructure tests
      ├── scripts/                # Test scripts (this directory)
      ├── conftest.py             # Pytest configuration
      └── test_utils.py           # Common test utilities
```

## Running Tests in Docker

All tests are designed to run within Docker containers, which ensures a consistent testing environment. The `run_tests.ps1` script handles container execution automatically.

## Adding New Test Types

To add a new test type, modify the `run_tests.ps1` script by:

1. Adding the new type to the `ValidateSet` parameter
2. Adding a new case in the `switch` statement with appropriate test commands 