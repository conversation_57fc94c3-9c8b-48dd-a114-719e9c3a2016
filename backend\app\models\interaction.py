from datetime import datetime
from typing import Optional, Dict
from sqlalchemy import String, Integer
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column
from app.db.base_class import Base
from enum import Enum as PyEnum

class InteractionType(PyEnum):
    VIEW = "VIEW"
    CLICK = "CLICK"
    SEARCH = "SEARCH"
    RATE = "RATE"
    BOOKMARK = "BOOKMARK"

class ContentType(PyEnum):
    ANIME = "ANIME"
    MANGA = "MANGA"
    THEME = "THEME"

class UserInteraction(Base):
    """Model for tracking user interactions with content."""
    
    # Basic Info
    session_id: Mapped[str] = mapped_column(String(100), index=True)
    interaction_type: Mapped[InteractionType] = mapped_column(String(50))
    content_id: Mapped[Optional[int]] = mapped_column(index=True)
    content_type: Mapped[ContentType] = mapped_column(String(50))
    
    # Additional Data
    interaction_metadata: Mapped[Dict] = mapped_column(
        JSONB,
        default={},
        nullable=False
    )
    
    def __repr__(self) -> str:
        """String representation of the interaction."""
        return f"<UserInteraction {self.interaction_type} on {self.content_type}:{self.content_id}>"

class SearchQuery(Base):
    """Model for tracking user search queries."""
    
    # Basic Info
    session_id: Mapped[str] = mapped_column(String(100), index=True)
    query: Mapped[str] = mapped_column(String, index=True)
    
    # Search Details
    search_filters: Mapped[Optional[Dict]] = mapped_column(
        JSONB,
        default={},
        nullable=True
    )
    results_count: Mapped[int] = mapped_column(Integer, nullable=False)
    
    def __repr__(self) -> str:
        """String representation of the search query."""
        return f"<SearchQuery '{self.query}' ({self.results_count} results)>" 