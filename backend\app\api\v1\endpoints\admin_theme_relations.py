"""
Admin Theme Relations API Endpoints

Admin interface for reviewing and managing contextual theme suggestions.
Provides endpoints for human-in-the-loop theme validation and approval workflow.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from app.services.contextual_theme_mapper import ContextualThemeMapper
from app.services.anilist import AniListService

logger = logging.getLogger(__name__)
router = APIRouter()


class ThemeRelationSuggestion(BaseModel):
    """Model for theme relation suggestions."""
    suggestion_id: str
    anime_id: str
    anime_title: str
    theme_id: str
    theme_name: str
    confidence: float
    confidence_percent: str
    reasoning: List[str]
    base_indicators: List[str]
    context_boosts: Dict[str, float]
    status: str = "pending"  # pending, approved, rejected
    created_at: str
    reviewed_at: Optional[str] = None
    reviewed_by: Optional[str] = None
    admin_notes: Optional[str] = None


class ThemeRelationReview(BaseModel):
    """Model for admin review of theme relations."""
    suggestion_id: str
    action: str = Field(..., description="approve, reject, or flag")
    admin_notes: Optional[str] = Field(None, description="Admin review notes")
    confidence_override: Optional[float] = Field(None, description="Override confidence score")


class AdminDashboardResponse(BaseModel):
    """Response model for admin dashboard."""
    total_suggestions: int
    pending_suggestions: int
    approved_suggestions: int
    rejected_suggestions: int
    high_confidence_pending: int
    recent_suggestions: List[ThemeRelationSuggestion]


class AnimeThemeAnalysisRequest(BaseModel):
    """Request model for analyzing anime themes for admin review."""
    anime_id: str
    force_refresh: bool = Field(default=False, description="Force refresh analysis")


# In-memory storage for demo (in production, use database)
theme_suggestions_db = {}
suggestion_counter = 0


def generate_suggestion_id() -> str:
    """Generate unique suggestion ID."""
    global suggestion_counter
    suggestion_counter += 1
    return f"suggestion_{suggestion_counter:06d}"


async def get_contextual_theme_mapper() -> ContextualThemeMapper:
    """Get contextual theme mapper instance."""
    return ContextualThemeMapper()


async def get_anilist_service() -> AniListService:
    """Get AniList service instance."""
    return AniListService()


@router.get("/dashboard", response_model=AdminDashboardResponse)
async def get_admin_dashboard() -> AdminDashboardResponse:
    """
    Get admin dashboard with theme relation statistics.
    
    Returns overview of pending, approved, and rejected theme suggestions.
    """
    try:
        suggestions = list(theme_suggestions_db.values())
        
        total_suggestions = len(suggestions)
        pending_suggestions = len([s for s in suggestions if s["status"] == "pending"])
        approved_suggestions = len([s for s in suggestions if s["status"] == "approved"])
        rejected_suggestions = len([s for s in suggestions if s["status"] == "rejected"])
        high_confidence_pending = len([s for s in suggestions if s["status"] == "pending" and s["confidence"] >= 0.8])
        
        # Get recent suggestions (last 10)
        recent_suggestions = sorted(suggestions, key=lambda x: x["created_at"], reverse=True)[:10]
        recent_suggestions_models = [ThemeRelationSuggestion(**s) for s in recent_suggestions]
        
        return AdminDashboardResponse(
            total_suggestions=total_suggestions,
            pending_suggestions=pending_suggestions,
            approved_suggestions=approved_suggestions,
            rejected_suggestions=rejected_suggestions,
            high_confidence_pending=high_confidence_pending,
            recent_suggestions=recent_suggestions_models
        )
        
    except Exception as e:
        logger.error(f"Error getting admin dashboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting dashboard: {str(e)}")


@router.post("/analyze-anime", response_model=List[ThemeRelationSuggestion])
async def analyze_anime_for_admin(
    request: AnimeThemeAnalysisRequest,
    theme_mapper: ContextualThemeMapper = Depends(get_contextual_theme_mapper),
    anilist_service: AniListService = Depends(get_anilist_service)
) -> List[ThemeRelationSuggestion]:
    """
    Analyze anime and create theme relation suggestions for admin review.
    
    This endpoint fetches anime data, performs contextual theme analysis,
    and creates suggestions that admins can review and approve/reject.
    """
    try:
        logger.info(f"Analyzing anime {request.anime_id} for admin review")
        
        # Check if we already have suggestions for this anime (unless force refresh)
        existing_suggestions = [
            s for s in theme_suggestions_db.values() 
            if s["anime_id"] == request.anime_id
        ]
        
        if existing_suggestions and not request.force_refresh:
            logger.info(f"Returning existing suggestions for anime {request.anime_id}")
            return [ThemeRelationSuggestion(**s) for s in existing_suggestions]
        
        # For demo purposes, use mock data (in production, fetch from AniList)
        # This simulates The Apothecary Diaries analysis
        mock_story_metadata = {
            "genres": ["Drama", "Mystery"],
            "tags": [
                {"name": "Historical", "category": "Setting", "rank": 85},
                {"name": "Court Politics", "category": "Theme", "rank": 80},
                {"name": "Female Protagonist", "category": "Cast", "rank": 90}
            ],
            "characters": [
                {
                    "name": "Maomao",
                    "gender": "Female",
                    "role": "MAIN",
                    "description": "Young apothecary with medical knowledge"
                }
            ],
            "staff": [
                {
                    "name": "Touko Shino",
                    "gender": "Female",
                    "role": "Character Design"
                }
            ]
        }
        
        # Perform contextual theme analysis
        theme_matches = theme_mapper.analyze_story_themes(mock_story_metadata)
        
        # Create suggestions for admin review
        suggestions = []
        current_time = datetime.utcnow().isoformat()
        
        for match in theme_matches:
            suggestion_id = generate_suggestion_id()
            
            suggestion = {
                "suggestion_id": suggestion_id,
                "anime_id": request.anime_id,
                "anime_title": "The Apothecary Diaries",  # Mock title
                "theme_id": match.theme_id,
                "theme_name": match.theme_name,
                "confidence": match.confidence,
                "confidence_percent": f"{match.confidence * 100:.1f}%",
                "reasoning": match.reasoning,
                "base_indicators": match.base_indicators,
                "context_boosts": match.context_boosts,
                "status": "pending",
                "created_at": current_time,
                "reviewed_at": None,
                "reviewed_by": None,
                "admin_notes": None
            }
            
            # Store in database
            theme_suggestions_db[suggestion_id] = suggestion
            suggestions.append(ThemeRelationSuggestion(**suggestion))
        
        logger.info(f"Created {len(suggestions)} theme suggestions for anime {request.anime_id}")
        return suggestions
        
    except Exception as e:
        logger.error(f"Error analyzing anime for admin: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing anime: {str(e)}")


@router.get("/suggestions", response_model=List[ThemeRelationSuggestion])
async def get_theme_suggestions(
    status: Optional[str] = Query(None, description="Filter by status: pending, approved, rejected"),
    min_confidence: Optional[float] = Query(None, description="Minimum confidence threshold"),
    limit: int = Query(50, description="Maximum number of suggestions to return")
) -> List[ThemeRelationSuggestion]:
    """
    Get theme relation suggestions with optional filtering.
    
    Allows admins to view and filter theme suggestions by status and confidence.
    """
    try:
        suggestions = list(theme_suggestions_db.values())
        
        # Apply filters
        if status:
            suggestions = [s for s in suggestions if s["status"] == status]
        
        if min_confidence is not None:
            suggestions = [s for s in suggestions if s["confidence"] >= min_confidence]
        
        # Sort by confidence (highest first) and limit
        suggestions = sorted(suggestions, key=lambda x: x["confidence"], reverse=True)[:limit]
        
        return [ThemeRelationSuggestion(**s) for s in suggestions]
        
    except Exception as e:
        logger.error(f"Error getting theme suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting suggestions: {str(e)}")


@router.post("/suggestions/{suggestion_id}/review")
async def review_theme_suggestion(
    suggestion_id: str,
    review: ThemeRelationReview,
    admin_user: str = Query("admin", description="Admin user ID")
) -> Dict[str, Any]:
    """
    Review a theme relation suggestion (approve, reject, or flag).
    
    Allows admins to approve or reject theme suggestions with notes.
    """
    try:
        if suggestion_id not in theme_suggestions_db:
            raise HTTPException(status_code=404, detail=f"Suggestion not found: {suggestion_id}")
        
        suggestion = theme_suggestions_db[suggestion_id]
        
        # Validate action
        if review.action not in ["approve", "reject", "flag"]:
            raise HTTPException(status_code=400, detail="Action must be 'approve', 'reject', or 'flag'")
        
        # Update suggestion
        suggestion["status"] = review.action + ("ed" if review.action != "flag" else "ged")
        suggestion["reviewed_at"] = datetime.utcnow().isoformat()
        suggestion["reviewed_by"] = admin_user
        suggestion["admin_notes"] = review.admin_notes
        
        if review.confidence_override is not None:
            suggestion["confidence"] = review.confidence_override
            suggestion["confidence_percent"] = f"{review.confidence_override * 100:.1f}%"
        
        logger.info(f"Admin {admin_user} {review.action}ed suggestion {suggestion_id}")
        
        return {
            "message": f"Suggestion {review.action}ed successfully",
            "suggestion_id": suggestion_id,
            "new_status": suggestion["status"],
            "reviewed_by": admin_user,
            "reviewed_at": suggestion["reviewed_at"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reviewing suggestion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reviewing suggestion: {str(e)}")


@router.get("/suggestions/{suggestion_id}", response_model=ThemeRelationSuggestion)
async def get_theme_suggestion(suggestion_id: str) -> ThemeRelationSuggestion:
    """
    Get a specific theme relation suggestion by ID.
    
    Returns detailed information about a single theme suggestion.
    """
    try:
        if suggestion_id not in theme_suggestions_db:
            raise HTTPException(status_code=404, detail=f"Suggestion not found: {suggestion_id}")
        
        suggestion = theme_suggestions_db[suggestion_id]
        return ThemeRelationSuggestion(**suggestion)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting suggestion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting suggestion: {str(e)}")


@router.delete("/suggestions/{suggestion_id}")
async def delete_theme_suggestion(
    suggestion_id: str,
    admin_user: str = Query("admin", description="Admin user ID")
) -> Dict[str, str]:
    """
    Delete a theme relation suggestion.
    
    Allows admins to remove suggestions that are no longer relevant.
    """
    try:
        if suggestion_id not in theme_suggestions_db:
            raise HTTPException(status_code=404, detail=f"Suggestion not found: {suggestion_id}")
        
        del theme_suggestions_db[suggestion_id]
        
        logger.info(f"Admin {admin_user} deleted suggestion {suggestion_id}")
        
        return {
            "message": "Suggestion deleted successfully",
            "suggestion_id": suggestion_id,
            "deleted_by": admin_user
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting suggestion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting suggestion: {str(e)}")


@router.get("/stats")
async def get_theme_relation_stats() -> Dict[str, Any]:
    """
    Get statistics about theme relations and admin activity.
    
    Returns metrics for monitoring the theme suggestion system.
    """
    try:
        suggestions = list(theme_suggestions_db.values())
        
        # Calculate statistics
        total_suggestions = len(suggestions)
        if total_suggestions == 0:
            return {
                "total_suggestions": 0,
                "approval_rate": 0.0,
                "average_confidence": 0.0,
                "theme_distribution": {},
                "confidence_distribution": {"high": 0, "medium": 0, "low": 0}
            }
        
        approved_count = len([s for s in suggestions if s["status"] == "approved"])
        approval_rate = approved_count / total_suggestions if total_suggestions > 0 else 0.0
        
        confidences = [s["confidence"] for s in suggestions]
        average_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        # Theme distribution
        theme_counts = {}
        for s in suggestions:
            theme_name = s["theme_name"]
            theme_counts[theme_name] = theme_counts.get(theme_name, 0) + 1
        
        # Confidence distribution
        high_conf = len([c for c in confidences if c >= 0.8])
        medium_conf = len([c for c in confidences if 0.5 <= c < 0.8])
        low_conf = len([c for c in confidences if c < 0.5])
        
        return {
            "total_suggestions": total_suggestions,
            "approval_rate": round(approval_rate, 3),
            "average_confidence": round(average_confidence, 3),
            "theme_distribution": theme_counts,
            "confidence_distribution": {
                "high": high_conf,
                "medium": medium_conf,
                "low": low_conf
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting theme relation stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting stats: {str(e)}")
