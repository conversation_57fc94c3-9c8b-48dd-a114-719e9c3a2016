#!/usr/bin/env python3
"""
Check exact theme names in the database.
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def check_all_theme_names():
    """Get all theme names from the database."""
    print("🎯 All Theme Names in Database")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/themes", timeout=10)
        
        if response.status_code == 200:
            themes = response.json()
            print(f"   ✅ Found {len(themes)} themes:")
            
            # Group by category
            by_category = {}
            for theme in themes:
                category = theme.get('category', 'Unknown')
                if category not in by_category:
                    by_category[category] = []
                by_category[category].append(theme.get('name', 'Unknown'))
            
            for category, theme_names in by_category.items():
                print(f"\n   📂 {category}:")
                for name in sorted(theme_names):
                    print(f"      • {name}")
                    
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def check_specific_themes():
    """Check if specific theme names exist."""
    print("\n🎯 Checking Specific Theme Names")
    print("=" * 50)
    
    # These are the theme names that CrossMediaAnimeMapper is looking for
    expected_themes = [
        "Comedic", "Romantic", "Dark", "Cozy", "Tense",
        "Journey", "Mystery", "Tragedy", "Fantasy World", 
        "Sci-Fi Future", "Historical", "School Life", 
        "Team Dynamics", "Friendship"
    ]
    
    try:
        response = requests.get(f"{BASE_URL}/themes", timeout=10)
        
        if response.status_code == 200:
            themes = response.json()
            theme_names = [theme.get('name', '') for theme in themes]
            
            print("   Checking expected theme names:")
            for expected in expected_themes:
                if expected in theme_names:
                    print(f"      ✅ {expected} - FOUND")
                else:
                    print(f"      ❌ {expected} - NOT FOUND")
                    # Look for similar names
                    similar = [name for name in theme_names if expected.lower() in name.lower() or name.lower() in expected.lower()]
                    if similar:
                        print(f"         Similar: {', '.join(similar)}")
                        
        else:
            print(f"   ❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

if __name__ == "__main__":
    check_all_theme_names()
    check_specific_themes()
